# WebRTC 远程控制系统

这是一个基于WebRTC的远程控制系统，支持键鼠事件传递和H264视频流播放。

## 项目结构

```
webrtc-demo/
├── frontend/                 # Vue3前端项目
│   ├── src/
│   │   ├── App.vue          # 主应用组件 (增强的键鼠事件处理)
│   │   └── main.js          # 入口文件
│   └── package.json
├── webrtc-server/           # C++后端项目
│   └── webrtc-server/
│       ├── webrtc-server-safe.cpp   # 安全版本 (仅键鼠事件)
│       ├── webrtc-server-full.cpp   # 完整版本 (键鼠+视频)
│       ├── WebRTCManager.cpp        # WebRTC管理器
│       ├── WebRTCManager.h
│       ├── UDPReceiver.cpp          # UDP接收器
│       ├── UDPReceiver.h
│       └── webrtc-server.vcxproj
├── test-video-stream.bat    # 视频流测试脚本
├── VIDEO_STREAMING_GUIDE.md # 视频流指南
├── ENHANCED_INPUT_TESTING.md # 增强输入测试指南
└── README.md
```

## 功能特性

### 🎮 增强的输入事件处理

- **详细鼠标事件**: 移动、按下、释放、点击、双击、滚轮、右键菜单
- **完整键盘支持**: 按键按下/释放、修饰键组合(Ctrl+Alt+等)、重复按键检测
- **按钮识别**: LEFT, MIDDLE, RIGHT, BACK, FORWARD鼠标按钮
- **实时传输**: 通过WebRTC DataChannel低延迟传输

### 📺 H264视频流

- **UDP接收**: 监听端口5000接收ffmpeg推流的H264数据
- **WebRTC转发**: 实时转发到前端进行播放
- **多种源支持**: 测试图案、桌面捕获、视频文件、摄像头
- **性能优化**: 硬件编码支持、可调节质量参数

### 🔗 WebRTC连接

- **P2P通信**: 前后端直接建立WebRTC连接
- **自动协商**: 动态SDP协商，支持数据通道和视频轨道
- **状态监控**: 详细的连接状态和错误报告

## 技术栈

### 前端

- Vue 3
- Element Plus
- WebRTC API
- Vite

### 后端

- C++ (VS2022)
- libdatachannel
- httplib
- nlohmann/json

## 安装和运行

### 前端设置

1. 进入前端目录：

```bash
cd frontend
```

2. 安装依赖：

```bash
npm install
```

3. 启动开发服务器：

```bash
npm run dev
```

前端将在 http://localhost:5173 运行

### 后端设置

1. 确保已安装以下依赖：

   - libdatachannel
   - vcpkg (用于管理C++依赖)
2. 使用Visual Studio 2022打开解决方案：

```
webrtc-server/webrtc-server.sln
```

3. 编译并运行项目

后端将在端口8080启动HTTP服务器

## 使用方法

### 1. 启动系统

1. 首先启动后端服务器
2. 然后启动前端开发服务器
3. 在浏览器中打开 http://localhost:5173

### 2. 建立连接

1. 在前端界面点击"连接服务器"按钮
2. 等待WebRTC连接建立成功
3. 连接成功后，数据通道状态会显示"已打开"

### 3. 测试键鼠事件

1. 确保"键鼠控制"开关已打开
2. 在视频区域移动鼠标或点击
3. 按下键盘按键
4. 在后端控制台查看事件输出

### 4. 测试视频流

1. 使用ffmpeg推送H264流到后端：

```bash
# 推送视频文件
ffmpeg -re -i input.mp4 -c:v libx264 -f h264 udp://localhost:5000

# 推送摄像头
ffmpeg -f dshow -i video="USB Camera" -c:v libx264 -preset ultrafast -tune zerolatency -f h264 udp://localhost:5000

# 推送屏幕录制
ffmpeg -f gdigrab -i desktop -c:v libx264 -preset ultrafast -tune zerolatency -f h264 udp://localhost:5000
```

2. 视频流将自动在前端播放

## 配置选项

### 前端配置

- 服务器地址：默认 http://localhost:8080
- 键鼠控制：可通过界面开关控制
- 视频播放：自动检测视频流

### 后端配置

- HTTP端口：8080
- UDP端口：5000 (用于接收H264流)
- WebRTC配置：使用Google STUN服务器

## 故障排除

### 连接问题

1. **SDP协商失败**：

   - 错误信息：`The order of m-lines in answer doesn't match order in offer`
   - 解决方案：确保后端正确处理offer中的媒体行顺序
   - 已修复：后端现在动态根据offer内容添加轨道
2. **防火墙问题**：

   - 确保防火墙允许端口8080和5000
   - Windows防火墙可能阻止连接
3. **依赖问题**：

   - 检查libdatachannel是否正确安装
   - 确保vcpkg依赖正确配置

### 视频问题

1. **ffmpeg 视频推流命令**：

   ```bash
   ffmpeg -re -i universe.mp4 -map 0:v -c:v libx264 -f rtp rtp://localhost:5000
   ffmpeg -re -i test.h264 -map 0:v -c:v libx264 -f rtp rtp://localhost:5000
   ffmpeg -re -i arashi.mp4 -map 0:v -c:v h264_nvenc -rc cbr -b:v 400k -f rtp rtp://localhost:5000

   ffmpeg -re -i arashi.mp4 -map 0:v -c:v libx264 -rc cbr -b:v 600k -f rtp rtp://localhost:5000
   ```

   **ffmepg 音频推流命令**

   ```bash
   ffmpeg -re -i universe.mp4 -map 0:a -c:a aac -ar 44100 -ac 2 -b:a 192k -profile:a aac_low -f adts udp://localhost:5001
   ```

   **桌面捕获推流命令：**

   ```bash
   1.普通命令 ffmpeg -f gdigrab -framerate 30 -i desktop ^
     		  -c:v libx264 ^
     		  -b:v 600k ^
     		  -g 60 ^
     		  -f rtp rtp://127.0.0.1:5000
   2.因特尔集成显卡： ffmpeg -init_hw_device qsv=hw -filter_hw_device hw ^
    		 -f gdigrab -framerate 15 -i desktop ^
     		-c:v h264_qsv -b:v 600k -g 30 ^
     		-f rtp rtp://127.0.0.1:5000
   3.英伟达显卡：  ffmpeg  -f gdigrab -framerate 15 -i desktop ^
     			-c:v h264_nvenc -preset llhp -rc cbr -b:v 600k -g 30 ^
     			-f rtp rtp://127.0.0.1:5000

   ```
2. **UDP端口问题**：

   - 检查UDP端口5000是否被占用
   - 使用 `netstat -an | findstr 5000` 检查端口状态
3. **视频编码问题**：

   - 确保H264编码参数正确
   - 检查视频分辨率和帧率设置

### 键鼠事件问题

1. **数据通道未打开**：

   - 确保WebRTC连接已建立
   - 检查数据通道状态显示"已打开"
2. **事件不响应**：

   - 确保键鼠控制开关已启用
   - 检查浏览器控制台是否有错误

### 调试技巧

1. **使用测试页面**：

   - 打开 `test-connection.html` 进行简单连接测试
   - 查看浏览器控制台的详细日志
2. **后端日志**：

   - 后端会输出详细的调试信息
   - 查看SDP交换过程的日志
3. **网络检查**：

   ```bash
   # 检查HTTP服务器
   curl http://localhost:8080/config

   # 检查UDP端口
   netstat -an | findstr 5000
   ```

## 开发说明

### 添加新功能

1. 前端：修改 `frontend/src/App.vue`
2. 后端：修改相应的C++文件

### 调试

1. 前端：使用浏览器开发者工具
2. 后端：使用Visual Studio调试器

## 许可证

MIT License
