<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 视频播放性能监控</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin: 0;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        }
        
        .card h3 {
            margin: 0 0 15px 0;
            color: #4a5568;
            font-size: 1.2em;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 10px 0;
            padding: 8px 12px;
            background: #f7fafc;
            border-radius: 6px;
            border-left: 4px solid #4299e1;
        }
        
        .metric-label {
            font-weight: 500;
            color: #2d3748;
        }
        
        .metric-value {
            font-weight: bold;
            color: #1a202c;
        }
        
        .status-good { border-left-color: #48bb78; }
        .status-warning { border-left-color: #ed8936; }
        .status-error { border-left-color: #f56565; }
        
        .fps-display {
            font-size: 3em;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            color: #4299e1;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .warnings {
            max-height: 300px;
            overflow-y: auto;
            background: #fff5f5;
            border-radius: 8px;
            padding: 15px;
        }
        
        .warning-item {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px;
            margin: 5px 0;
            background: white;
            border-radius: 6px;
            border-left: 4px solid #f56565;
        }
        
        .warning-time {
            font-size: 0.8em;
            color: #718096;
            min-width: 80px;
        }
        
        .controls {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin: 30px 0;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 1em;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4299e1, #3182ce);
            color: white;
        }
        
        .btn-success {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #f56565, #e53e3e);
            color: white;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.2);
        }
        
        .chart-container {
            height: 200px;
            background: #f7fafc;
            border-radius: 8px;
            margin: 15px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #718096;
            font-style: italic;
        }
        
        .recommendations {
            background: linear-gradient(135deg, #e6fffa, #b2f5ea);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .recommendations h3 {
            color: #234e52;
            margin: 0 0 15px 0;
        }
        
        .recommendation-item {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 6px;
        }
        
        .recommendation-icon {
            font-size: 1.2em;
            margin-top: 2px;
        }
        
        .footer {
            text-align: center;
            color: rgba(255,255,255,0.8);
            margin-top: 40px;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 视频播放性能监控</h1>
            <p>实时监控WebRTC视频流播放性能，诊断卡顿问题</p>
        </div>

        <div class="controls">
            <button class="btn-primary" onclick="startMonitoring()">🎬 开始监控</button>
            <button class="btn-success" onclick="exportReport()">📊 导出报告</button>
            <button class="btn-danger" onclick="clearData()">🗑️ 清空数据</button>
        </div>

        <div class="dashboard">
            <!-- FPS监控 -->
            <div class="card">
                <h3>📺 帧率监控</h3>
                <div class="fps-display" id="currentFps">-- FPS</div>
                <div class="metric status-good">
                    <span class="metric-label">平均帧率</span>
                    <span class="metric-value" id="avgFps">-- FPS</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最低帧率</span>
                    <span class="metric-value" id="minFps">-- FPS</span>
                </div>
                <div class="metric">
                    <span class="metric-label">最高帧率</span>
                    <span class="metric-value" id="maxFps">-- FPS</span>
                </div>
            </div>

            <!-- 延迟监控 -->
            <div class="card">
                <h3>⏱️ 延迟监控</h3>
                <div class="metric">
                    <span class="metric-label">解码延迟</span>
                    <span class="metric-value" id="decodeLatency">-- ms</span>
                </div>
                <div class="metric">
                    <span class="metric-label">渲染延迟</span>
                    <span class="metric-value" id="renderLatency">-- ms</span>
                </div>
                <div class="metric status-warning">
                    <span class="metric-label">总延迟</span>
                    <span class="metric-value" id="totalLatency">-- ms</span>
                </div>
            </div>

            <!-- 解码器状态 -->
            <div class="card">
                <h3>🎮 解码器状态</h3>
                <div class="metric">
                    <span class="metric-label">队列大小</span>
                    <span class="metric-value" id="queueSize">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">总帧数</span>
                    <span class="metric-value" id="totalFrames">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">丢帧数</span>
                    <span class="metric-value" id="droppedFrames">--</span>
                </div>
                <div class="metric status-good">
                    <span class="metric-label">成功率</span>
                    <span class="metric-value" id="successRate">-- %</span>
                </div>
            </div>

            <!-- 网络状态 -->
            <div class="card">
                <h3>🌐 网络状态</h3>
                <div class="metric">
                    <span class="metric-label">带宽</span>
                    <span class="metric-value" id="bandwidth">-- KB/s</span>
                </div>
                <div class="metric">
                    <span class="metric-label">接收包数</span>
                    <span class="metric-value" id="packetsReceived">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">丢包数</span>
                    <span class="metric-value" id="packetsLost">--</span>
                </div>
            </div>

            <!-- 内存使用 -->
            <div class="card">
                <h3>💾 内存使用</h3>
                <div class="metric">
                    <span class="metric-label">已使用</span>
                    <span class="metric-value" id="memoryUsed">-- MB</span>
                </div>
                <div class="metric">
                    <span class="metric-label">总内存</span>
                    <span class="metric-value" id="memoryTotal">-- MB</span>
                </div>
                <div class="metric status-warning">
                    <span class="metric-label">使用率</span>
                    <span class="metric-value" id="memoryPercentage">-- %</span>
                </div>
            </div>

            <!-- 性能警告 -->
            <div class="card">
                <h3>⚠️ 性能警告</h3>
                <div class="warnings" id="warningsContainer">
                    <div style="text-align: center; color: #718096; padding: 20px;">
                        暂无警告信息
                    </div>
                </div>
            </div>
        </div>

        <!-- 性能建议 -->
        <div class="recommendations">
            <h3>💡 性能优化建议</h3>
            <div class="recommendation-item">
                <span class="recommendation-icon">🎯</span>
                <div>
                    <strong>降低分辨率：</strong>如果FPS低于25，建议将分辨率从1600x960降低到1280x720
                </div>
            </div>
            <div class="recommendation-item">
                <span class="recommendation-icon">⚡</span>
                <div>
                    <strong>减少缓冲：</strong>如果延迟超过100ms，建议减少解码器队列大小
                </div>
            </div>
            <div class="recommendation-item">
                <span class="recommendation-icon">🔧</span>
                <div>
                    <strong>硬件加速：</strong>确保浏览器启用了GPU硬件加速功能
                </div>
            </div>
            <div class="recommendation-item">
                <span class="recommendation-icon">🌐</span>
                <div>
                    <strong>网络优化：</strong>如果丢包率超过5%，检查网络连接质量
                </div>
            </div>
        </div>

        <div class="footer">
            <p>🎮 WebRTC远程桌面性能监控工具 | 实时诊断视频播放性能</p>
        </div>
    </div>

    <script>
        let isMonitoring = false;
        let monitoringInterval;
        let mockData = {
            fps: { current: 30, average: 28, min: 25, max: 30 },
            latency: { decode: 15, render: 8, total: 23 },
            decoder: { queueSize: 2, totalFrames: 1500, droppedFrames: 12, successRate: 99.2 },
            network: { bandwidth: 850, packetsReceived: 1500, packetsLost: 8 },
            memory: { used: 45, total: 128, percentage: 35 },
            warnings: []
        };

        function startMonitoring() {
            if (isMonitoring) {
                stopMonitoring();
                return;
            }

            isMonitoring = true;
            document.querySelector('.btn-primary').textContent = '⏸️ 停止监控';
            
            // 模拟数据更新
            monitoringInterval = setInterval(updateMockData, 1000);
            updateDisplay();
        }

        function stopMonitoring() {
            isMonitoring = false;
            document.querySelector('.btn-primary').textContent = '🎬 开始监控';
            clearInterval(monitoringInterval);
        }

        function updateMockData() {
            // 模拟性能数据变化
            mockData.fps.current = 25 + Math.random() * 10;
            mockData.latency.decode = 10 + Math.random() * 20;
            mockData.latency.render = 5 + Math.random() * 15;
            mockData.latency.total = mockData.latency.decode + mockData.latency.render;
            mockData.decoder.queueSize = Math.floor(Math.random() * 5);
            mockData.network.bandwidth = 700 + Math.random() * 300;
            mockData.memory.percentage = 30 + Math.random() * 20;

            // 随机添加警告
            if (Math.random() < 0.1) {
                const warnings = [
                    '帧率过低: 23.5 FPS',
                    '解码延迟过高: 45.2ms',
                    '解码队列过长: 6 帧',
                    '内存使用过高: 85%'
                ];
                const warning = warnings[Math.floor(Math.random() * warnings.length)];
                mockData.warnings.unshift({
                    message: warning,
                    timestamp: new Date().toLocaleTimeString(),
                    id: Date.now()
                });
                
                if (mockData.warnings.length > 10) {
                    mockData.warnings.pop();
                }
            }

            updateDisplay();
        }

        function updateDisplay() {
            // 更新FPS显示
            document.getElementById('currentFps').textContent = Math.round(mockData.fps.current) + ' FPS';
            document.getElementById('avgFps').textContent = Math.round(mockData.fps.average) + ' FPS';
            document.getElementById('minFps').textContent = Math.round(mockData.fps.min) + ' FPS';
            document.getElementById('maxFps').textContent = Math.round(mockData.fps.max) + ' FPS';

            // 更新延迟显示
            document.getElementById('decodeLatency').textContent = mockData.latency.decode.toFixed(1) + ' ms';
            document.getElementById('renderLatency').textContent = mockData.latency.render.toFixed(1) + ' ms';
            document.getElementById('totalLatency').textContent = mockData.latency.total.toFixed(1) + ' ms';

            // 更新解码器状态
            document.getElementById('queueSize').textContent = mockData.decoder.queueSize;
            document.getElementById('totalFrames').textContent = mockData.decoder.totalFrames;
            document.getElementById('droppedFrames').textContent = mockData.decoder.droppedFrames;
            document.getElementById('successRate').textContent = mockData.decoder.successRate + '%';

            // 更新网络状态
            document.getElementById('bandwidth').textContent = Math.round(mockData.network.bandwidth) + ' KB/s';
            document.getElementById('packetsReceived').textContent = mockData.network.packetsReceived;
            document.getElementById('packetsLost').textContent = mockData.network.packetsLost;

            // 更新内存使用
            document.getElementById('memoryUsed').textContent = mockData.memory.used + ' MB';
            document.getElementById('memoryTotal').textContent = mockData.memory.total + ' MB';
            document.getElementById('memoryPercentage').textContent = Math.round(mockData.memory.percentage) + '%';

            // 更新警告列表
            updateWarnings();
        }

        function updateWarnings() {
            const container = document.getElementById('warningsContainer');
            
            if (mockData.warnings.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #718096; padding: 20px;">暂无警告信息</div>';
                return;
            }

            container.innerHTML = mockData.warnings.map(warning => `
                <div class="warning-item">
                    <span class="warning-time">${warning.timestamp}</span>
                    <span>⚠️ ${warning.message}</span>
                </div>
            `).join('');
        }

        function exportReport() {
            const report = {
                timestamp: new Date().toISOString(),
                performance: mockData,
                recommendations: [
                    '建议降低分辨率以提升帧率',
                    '优化网络连接减少丢包',
                    '启用硬件加速提升解码性能'
                ]
            };

            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `performance-report-${new Date().toISOString().slice(0, 19)}.json`;
            a.click();
            URL.revokeObjectURL(url);

            alert('📊 性能报告已导出！');
        }

        function clearData() {
            mockData.warnings = [];
            mockData.decoder.totalFrames = 0;
            mockData.decoder.droppedFrames = 0;
            mockData.network.packetsReceived = 0;
            mockData.network.packetsLost = 0;
            updateDisplay();
            alert('🗑️ 数据已清空！');
        }

        // 初始化显示
        updateDisplay();
    </script>
</body>
</html>
