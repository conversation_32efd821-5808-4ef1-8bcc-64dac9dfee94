<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebCodecs 支持测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>WebCodecs API 支持测试</h1>
    
    <div id="results"></div>
    
    <script>
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            document.getElementById('results').appendChild(div);
        }

        function runTests() {
            addResult('<h3>🔍 开始WebCodecs支持检测...</h3>');
            
            // 基本信息
            addResult(`<strong>浏览器信息:</strong><br><pre>${navigator.userAgent}</pre>`);
            addResult(`<strong>协议:</strong> ${location.protocol}`);
            addResult(`<strong>主机:</strong> ${location.hostname}`);
            
            // 检查各个WebCodecs API
            const apis = [
                'VideoDecoder',
                'VideoEncoder', 
                'VideoFrame',
                'EncodedVideoChunk',
                'AudioDecoder',
                'AudioEncoder',
                'AudioData',
                'EncodedAudioChunk'
            ];
            
            let allSupported = true;
            apis.forEach(api => {
                const supported = api in window;
                addResult(`<strong>${api}:</strong> ${supported ? '✅ 支持' : '❌ 不支持'}`, supported ? 'success' : 'error');
                if (!supported) allSupported = false;
            });
            
            // 总结
            if (allSupported) {
                addResult('<h3>🎉 WebCodecs API 完全支持！</h3>', 'success');
                
                // 尝试创建VideoDecoder
                try {
                    const decoder = new VideoDecoder({
                        output: (frame) => {
                            addResult('✅ VideoDecoder 创建成功', 'success');
                            frame.close();
                        },
                        error: (error) => {
                            addResult(`❌ VideoDecoder 错误: ${error.message}`, 'error');
                        }
                    });
                    
                    // 检查H.264支持
                    VideoDecoder.isConfigSupported({
                        codec: 'avc1.64001f',
                        codedWidth: 1920,
                        codedHeight: 1080
                    }).then(result => {
                        addResult(`<strong>H.264 High Profile 支持:</strong> ${result.supported ? '✅ 支持' : '❌ 不支持'}`, result.supported ? 'success' : 'error');
                        if (result.config) {
                            addResult(`<strong>配置详情:</strong><br><pre>${JSON.stringify(result.config, null, 2)}</pre>`);
                        }
                    }).catch(error => {
                        addResult(`❌ H.264支持检查失败: ${error.message}`, 'error');
                    });
                    
                } catch (error) {
                    addResult(`❌ VideoDecoder 创建失败: ${error.message}`, 'error');
                }
                
            } else {
                addResult('<h3>❌ WebCodecs API 不完全支持</h3>', 'error');
                addResult('<p><strong>解决方案:</strong></p><ul><li>确保使用 Chrome 94+ 或 Edge 94+</li><li>如果使用较新版本仍有问题，请在 chrome://flags/ 中启用 "Experimental Web Platform features"</li><li>确保网站运行在 HTTPS 或 localhost 环境</li></ul>');
            }
            
            // 检查是否在安全上下文中
            addResult(`<strong>安全上下文 (isSecureContext):</strong> ${window.isSecureContext ? '✅ 是' : '❌ 否'}`, window.isSecureContext ? 'success' : 'error');
            
            // 检查Chrome版本
            const chromeMatch = navigator.userAgent.match(/Chrome\/(\d+)/);
            if (chromeMatch) {
                const chromeVersion = parseInt(chromeMatch[1]);
                addResult(`<strong>Chrome 版本:</strong> ${chromeVersion}`, chromeVersion >= 94 ? 'success' : 'error');
                if (chromeVersion < 94) {
                    addResult('❌ Chrome 版本过低，WebCodecs 需要 Chrome 94+', 'error');
                }
            }
        }

        // 运行测试
        runTests();
    </script>
</body>
</html>
