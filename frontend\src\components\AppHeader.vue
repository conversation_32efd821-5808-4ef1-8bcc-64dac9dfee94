<template>
  <div class="app-header">
    <div class="header-content">
      <div class="header-left">
        <el-icon class="header-icon"><VideoCamera /></el-icon>
        <h1>WebRTC H264 远程控制系统</h1>
      </div>
      <div class="header-right">
        <div class="connection-status">
          <el-tag
            :type="connected ? 'success' : 'info'"
            size="large"
            :effect="connected ? 'dark' : 'plain'">
            <el-icon><Connection /></el-icon>
            {{ connected ? '已连接' : '未连接' }}
          </el-tag>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { VideoCamera, Connection } from '@element-plus/icons-vue'

// Props
defineProps({
  connected: {
    type: Boolean,
    default: false
  }
})
</script>

<style scoped>
.app-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #3b82f6;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.header-right {
  display: flex;
  align-items: center;
}

.connection-status .el-tag {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 6px;
}
</style>
