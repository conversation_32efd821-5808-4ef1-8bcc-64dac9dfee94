<template>
  <div class="app-layout">
    <!-- 侧边导航栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><VideoCamera /></el-icon>
          <span v-show="!sidebarCollapsed" class="logo-text">WebRTC控制</span>
        </div>
        <el-button
          class="collapse-btn"
          :icon="sidebarCollapsed ? Expand : Fold"
          @click="toggleSidebar"
          text />
      </div>
      
      <el-menu
        :default-active="$route.name"
        class="sidebar-menu"
        :collapse="sidebarCollapsed"
        router>
        <el-menu-item
          v-for="route in menuRoutes"
          :key="route.name"
          :index="route.name">
          <el-icon><component :is="route.meta.icon" /></el-icon>
          <template #title>{{ route.meta.title }}</template>
        </el-menu-item>
      </el-menu>
      

    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  VideoCamera,
  DataAnalysis,
  Document,
  Expand,
  Fold
} from '@element-plus/icons-vue'

// 侧边栏状态
const sidebarCollapsed = ref(false)

// 菜单路由
const menuRoutes = [
  { name: 'main', meta: { title: '远程控制', icon: 'VideoCamera' } },
  { name: 'status', meta: { title: '系统统计', icon: 'DataAnalysis' } },
  { name: 'logs', meta: { title: '系统日志', icon: 'Document' } }
]

// 方法
function toggleSidebar() {
  sidebarCollapsed.value = !sidebarCollapsed.value
}
</script>

<style scoped>
.app-layout {
  display: flex;
  height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.sidebar {
  width: 180px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(226, 232, 240, 0.8);
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.sidebar.collapsed {
  width: 50px;
}

.sidebar-header {
  height: 72px;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  font-size: 28px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  filter: drop-shadow(0 2px 4px rgba(99, 102, 241, 0.2));
}

.logo-text {
  font-size: 18px;
  font-weight: 700;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
}

.collapse-btn {
  padding: 10px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.collapse-btn:hover {
  background: rgba(99, 102, 241, 0.1);
  transform: scale(1.05);
}

.sidebar-menu {
  flex: 1;
  border: none;
  background: transparent;
  padding: 16px 12px;
}

.sidebar-menu :deep(.el-menu-item) {
  margin-bottom: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  font-weight: 500;
}

.sidebar-menu :deep(.el-menu-item:hover) {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
  transform: translateX(4px);
}

.sidebar-menu :deep(.el-menu-item.is-active) {
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  color: white;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
}



.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}



.page-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  background: transparent;
}

/* 深色主题适配 */
:global(.dark) .app-layout {
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
}

:global(.dark) .sidebar {
  background: rgba(15, 23, 42, 0.95);
  border-right-color: rgba(51, 65, 85, 0.8);
}

:global(.dark) .sidebar-header {
  border-bottom-color: rgba(51, 65, 85, 0.6);
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%);
}

:global(.dark) .logo-text {
  background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}




</style>
