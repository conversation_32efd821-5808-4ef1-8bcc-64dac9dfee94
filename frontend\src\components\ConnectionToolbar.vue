<template>
  <div class="toolbar">
    <!-- 主控制区域 -->
    <div class="main-controls">
      <!-- 连接控制组 -->
      <div class="connection-group">
        <div class="input-section">
          <label class="input-label">服务器地址</label>
          <el-input
            :model-value="serverUrl"
            @update:model-value="$emit('update:serverUrl', $event)"
            placeholder="http://localhost:8080"
            :disabled="connected"
            size="default"
            class="server-input">
            <template #prefix>
              <el-icon><Link /></el-icon>
            </template>
          </el-input>
        </div>

        <div class="action-buttons">
          <el-button
            type="primary"
            size="default"
            @click="$emit('connect')"
            :loading="connecting"
            :disabled="connected"
            class="connect-btn">
            <el-icon><VideoPlay /></el-icon>
            {{ connecting ? '连接中...' : '连接' }}
          </el-button>

          <el-button
            type="danger"
            size="default"
            @click="$emit('disconnect')"
            :disabled="!connected"
            class="disconnect-btn">
            <el-icon><VideoPause /></el-icon>
            断开连接
          </el-button>

          <el-button
            type="info"
            size="default"
            @click="$emit('diagnose')"
            :disabled="connecting"
            class="diagnose-btn">
            <el-icon><Tools /></el-icon>
            网络诊断
          </el-button>
        </div>
      </div>

    </div>

    <!-- 第二行：控制选项和状态指示器 -->
    <div class="second-row">
      <div class="controls-section">
        <div class="control-option">
          <label class="option-label">键鼠控制</label>
          <el-switch
            :model-value="mouseControlEnabled"
            @update:model-value="$emit('update:mouseControlEnabled', $event)"
            size="default"
            :disabled="!dataChannelOpen" />
        </div>

        <el-button
          size="default"
          @click="$emit('clearLogs')"
          class="clear-btn">
          <el-icon><Delete /></el-icon>
          清空日志
        </el-button>
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicators">
        <div class="status-item" :class="connected ? 'status-success' : 'status-default'">
          <div class="status-icon">
            <el-icon><Connection /></el-icon>
          </div>
          <div class="status-info">
            <span class="status-label">连接状态</span>
            <span class="status-value">{{ connectionStatus }}</span>
          </div>
        </div>

        <div class="status-item" :class="dataChannelOpen ? 'status-success' : 'status-warning'">
          <div class="status-icon">
            <el-icon><Message /></el-icon>
          </div>
          <div class="status-info">
            <span class="status-label">数据通道</span>
            <span class="status-value">{{ dataChannelOpen ? '已打开' : '未打开' }}</span>
          </div>
        </div>

        <div class="status-item" :class="videoReceived ? 'status-success' : 'status-warning'">
          <div class="status-icon">
            <el-icon><VideoCamera /></el-icon>
          </div>
          <div class="status-info">
            <span class="status-label">视频流</span>
            <span class="status-value">{{ videoReceived ? '正在接收' : '等待中' }}</span>
          </div>
        </div>

        <div class="status-item" :class="mouseControlEnabled && dataChannelOpen ? 'status-success' : 'status-default'">
          <div class="status-icon">
            <el-icon><Mouse /></el-icon>
          </div>
          <div class="status-info">
            <span class="status-label">键鼠控制</span>
            <span class="status-value">{{ mouseControlEnabled && dataChannelOpen ? '已激活' : '未激活' }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import {
  Link,
  VideoPlay,
  VideoPause,
  Connection,
  Message,
  VideoCamera,
  Delete,
  Tools,
  Mouse
} from '@element-plus/icons-vue'

// Props
defineProps({
  serverUrl: {
    type: String,
    required: true
  },
  connected: {
    type: Boolean,
    default: false
  },
  connecting: {
    type: Boolean,
    default: false
  },
  connectionStatus: {
    type: String,
    default: '未连接'
  },
  dataChannelOpen: {
    type: Boolean,
    default: false
  },
  videoReceived: {
    type: Boolean,
    default: false
  },
  mouseControlEnabled: {
    type: Boolean,
    default: true
  }
})

// Emits
defineEmits([
  'update:serverUrl',
  'update:mouseControlEnabled',
  'connect',
  'disconnect',
  'clearLogs',
  'diagnose'
])
</script>

<style scoped>
.toolbar {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  gap: 6px;
  flex-shrink: 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.main-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
  flex-wrap: wrap;
}

.connection-group {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.input-section {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.input-section:hover {
  background: linear-gradient(135deg, rgba(248, 250, 252, 1) 0%, rgba(241, 245, 249, 1) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
}

.input-label {
  font-size: 14px;
  color: #475569;
  font-weight: 600;
  white-space: nowrap;
  letter-spacing: -0.025em;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 6px;
}

.second-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 12px;
}

.controls-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-option {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 6px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.control-option:hover {
  background: linear-gradient(135deg, rgba(248, 250, 252, 1) 0%, rgba(241, 245, 249, 1) 100%);
  transform: translateY(-1px);
}

.option-label {
  font-size: 14px;
  color: #475569;
  font-weight: 600;
  white-space: nowrap;
}

.server-input {
  width: 280px;
}

.server-input :deep(.el-input__wrapper) {
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.server-input :deep(.el-input__wrapper:hover) {
  border-color: rgba(99, 102, 241, 0.4);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
}

.server-input :deep(.el-input__wrapper.is-focus) {
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 按钮样式 */
.connect-btn,
.disconnect-btn,
.diagnose-btn,
.clear-btn {
  padding: 8px 14px !important;
  border-radius: 8px !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1) !important;
}

.connect-btn:hover,
.disconnect-btn:hover,
.diagnose-btn:hover,
.clear-btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
}

.connect-btn {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
  border: none !important;
}

.disconnect-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  border: none !important;
}

.diagnose-btn {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
  border: none !important;
}

.clear-btn {
  background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
  border: none !important;
  color: white !important;
}

/* 状态指示器 */
.status-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 6px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 3px 6px;
  border-radius: 4px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.8);
}

.status-item:hover {
  background: rgba(255, 255, 255, 1);
  transform: translateY(-1px);
}

.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 12px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.status-label {
  font-size: 10px;
  font-weight: 500;
  color: #64748b;
  line-height: 1;
}

.status-value {
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
}

.button-group :deep(.el-button),
.action-group :deep(.el-button) {
  border-radius: 6px;
  font-weight: 600;
  transition: all 0.2s ease;
  font-size: 12px;
  padding: 6px 12px;
}

.button-group :deep(.el-button:hover),
.action-group :deep(.el-button:hover) {
  transform: translateY(-1px);
}

.button-group :deep(.el-button--primary) {
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  border: none;
}

.button-group :deep(.el-button--danger) {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
}

.button-group :deep(.el-button--info) {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  border: none;
}

.control-group :deep(.el-switch) {
  --el-switch-on-color: #6366f1;
}

/* 状态颜色 */
.status-success .status-icon {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  color: white;
}

.status-success .status-value {
  color: #22c55e;
}

.status-warning .status-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
}

.status-warning .status-value {
  color: #f59e0b;
}

.status-default .status-icon {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  color: white;
}

.status-default .status-value {
  color: #94a3b8;
}

/* 深色主题适配 */
:global(.dark) .toolbar {
  background: rgba(15, 23, 42, 0.95);
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

:global(.dark) .toolbar-group,
:global(.dark) .status-indicators {
  background: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.8) 100%);
  border-color: rgba(51, 65, 85, 0.6);
}

:global(.dark) .toolbar-group:hover,
:global(.dark) .status-indicators:hover {
  background: linear-gradient(135deg, rgba(15, 23, 42, 1) 0%, rgba(30, 41, 59, 1) 100%);
}

:global(.dark) .toolbar-group label {
  color: #cbd5e1;
}

:global(.dark) .status-indicator {
  background: rgba(15, 23, 42, 0.8);
  color: #cbd5e1;
}

:global(.dark) .status-indicator:hover {
  background: rgba(15, 23, 42, 1);
}
</style>
