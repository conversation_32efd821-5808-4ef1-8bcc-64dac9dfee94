<template>
    <div 
      class="control-area"
      ref="controlArea"
      tabindex="0"
      @mousemove="handleMouseMove"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @wheel="handleMouseWheel"
      @keydown="handleKeyDown"
      @keyup="handleKeyUp"
    >
      <div class="instructions">
        <h3>Control Area</h3>
        <p>Move mouse, scroll wheel, and press keys in this area</p>
      </div>
      
      <div v-if="activeEvent" class="event-display">
        Last event: {{ activeEvent.type }}
      </div>
      
      <div class="mouse-position">
        Mouse: ({{ mouseX }}, {{ mouseY }})
      </div>
    </div>
  </template>
  
  <script>
  import { ref, onMounted } from 'vue';
  
  export default {
    emits: ['event'],
    
    setup(props, { emit }) {
      const mouseX = ref(0);
      const mouseY = ref(0);
      const activeEvent = ref(null);
      
      // 获取控制区域尺寸
      const getAreaRect = () => {
        const area = document.querySelector('.control-area');
        return area.getBoundingClientRect();
      };
      
      // 发送事件到父组件
      const sendEvent = (type, data = {}) => {
        const event = {
          type,
          ...data,
          timestamp: Date.now()
        };
        activeEvent.value = event;
        emit('event', event);
      };
      
      // 事件处理器
      const handleMouseMove = (e) => {
        const rect = getAreaRect();
        mouseX.value = e.clientX - rect.left;
        mouseY.value = e.clientY - rect.top;
        
        sendEvent('mousemove', {
          x: mouseX.value,
          y: mouseY.value,
          normalizedX: mouseX.value / rect.width,
          normalizedY: mouseY.value / rect.height
        });
      };
      
      const handleMouseDown = (e) => {
        sendEvent('mousedown', { button: e.button });
      };
      
      const handleMouseUp = (e) => {
        sendEvent('mouseup', { button: e.button });
      };
      
      const handleMouseWheel = (e) => {
        sendEvent('wheel', {
          deltaX: e.deltaX,
          deltaY: e.deltaY,
          deltaZ: e.deltaZ
        });
      };
      
      const handleKeyDown = (e) => {
        sendEvent('keydown', {
          key: e.key,
          code: e.code,
          ctrl: e.ctrlKey,
          shift: e.shiftKey,
          alt: e.altKey,
          meta: e.metaKey
        });
      };
      
      const handleKeyUp = (e) => {
        sendEvent('keyup', {
          key: e.key,
          code: e.code,
          ctrl: e.ctrlKey,
          shift: e.shiftKey,
          alt: e.altKey,
          meta: e.metaKey
        });
      };
      
      // 设置焦点
      onMounted(() => {
        const area = document.querySelector('.control-area');
        area.focus();
      });
      
      return {
        mouseX,
        mouseY,
        activeEvent,
        handleMouseMove,
        handleMouseDown,
        handleMouseUp,
        handleMouseWheel,
        handleKeyDown,
        handleKeyUp
      };
    }
  };
  </script>
  
  <style>
  .control-area {
    width: 100%;
    height: 300px;
    border: 2px solid #42b883;
    border-radius: 8px;
    padding: 20px;
    background-color: #f8f9fa;
    cursor: all-scroll;
    outline: none;
    position: relative;
    overflow: hidden;
  }
  
  .instructions {
    text-align: center;
    margin-bottom: 20px;
    color: #555;
  }
  
  .event-display {
    position: absolute;
    bottom: 10px;
    left: 10px;
    padding: 5px 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 4px;
  }
  
  .mouse-position {
    position: absolute;
    bottom: 10px;
    right: 10px;
    padding: 5px 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    border-radius: 4px;
  }
  
  .control-area:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
  }
  </style>