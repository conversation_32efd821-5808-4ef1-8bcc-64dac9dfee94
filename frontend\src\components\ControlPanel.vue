<template>
  <div class="control-panel">
    <div class="control-toolbar">
      <div class="connection-controls">
        <!-- 服务器地址输入 -->
        <el-input
          :model-value="serverUrl"
          @update:model-value="$emit('update:serverUrl', $event)"
          placeholder="服务器地址"
          style="width: 200px"
          :disabled="connecting || connected">
          <template #prepend>服务器</template>
        </el-input>

        <!-- 连接控制按钮 -->
        <el-button
          type="primary"
          :loading="connecting"
          :disabled="connected"
          @click="handleConnect">
          {{ connecting ? '连接中...' : '连接' }}
        </el-button>

        <el-button
          type="danger"
          :disabled="!connected && !connecting"
          @click="handleDisconnect">
          断开连接
        </el-button>

        <!-- 视频控制按钮 -->
        <el-button
          type="warning"
          :disabled="!connected"
          @click="handleRequestKeyFrame"
          title="解决花屏问题">
          🔑 请求关键帧
        </el-button>

        <el-button
          type="info"
          :disabled="!connected"
          @click="handleResetPlayer"
          title="重置播放器">
          🔄 重置播放器
        </el-button>

        <!-- 解码器控制 -->
        <el-dropdown @command="handleSwitchDecoder" trigger="click">
          <el-button type="primary">
            {{ currentDecoder }} <el-icon><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="fixed">🎨 修复Canvas播放器 (推荐)</el-dropdown-item>
              <el-dropdown-item command="broadway">🎭 Broadway H264播放器</el-dropdown-item>
              <el-dropdown-item command="ffmpeg">🎬 FFmpeg播放器</el-dropdown-item>
              <el-dropdown-item command="simple">🎨 简单Canvas播放器</el-dropdown-item>
              <el-dropdown-item command="webcodecs">🚀 WebCodecs播放器</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>

        <el-button
          type="info"
          @click="handleToggleDecoder"
          title="切换到下一个解码器">
          🔄 切换解码器
        </el-button>

        <!-- 诊断和测试按钮 -->
        <el-button
          type="warning"
          @click="handleDiagnose"
          title="诊断解码器状态">
          🔍 诊断解码器
        </el-button>

        <el-button
          type="info"
          :disabled="!connected"
          @click="handleTestConnection">
          测试连接
        </el-button>

        <el-button
          type="primary"
          @click="handleCheckCodecSupport">
          检查编解码器支持
        </el-button>

        <el-button
          type="warning"
          size="small"
          @click="handleTestWebCodecs">
          🧪 测试WebCodecs
        </el-button>
      </div>

      <!-- 状态指示器 -->
      <div class="status-indicators">
        <el-tag
          :type="connectionStatusType"
          size="small">
          {{ connectionStatus }}
        </el-tag>

        <el-tag
          v-if="isReceivingVideo"
          type="success"
          size="small">
          📺 视频
        </el-tag>

        <el-tag
          v-if="isReceivingAudio"
          type="success"
          size="small">
          🔊 音频
        </el-tag>

        <el-tag
          v-if="currentDecoder"
          :type="useWebCodecs ? 'success' : 'info'"
          size="small">
          {{ currentDecoder }}
        </el-tag>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ArrowDown } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  serverUrl: String,
  connected: Boolean,
  connecting: Boolean,
  connectionStatus: String,
  isReceivingVideo: Boolean,
  isReceivingAudio: Boolean,
  useWebCodecs: Boolean,
  currentDecoder: String
})

// Emits
const emit = defineEmits([
  'update:serverUrl',
  'connect',
  'disconnect',
  'requestKeyFrame',
  'resetPlayer',
  'toggleDecoder',
  'switchDecoder',
  'diagnose',
  'testConnection',
  'checkCodecSupport'
])

// 计算属性
const connectionStatusType = computed(() => {
  if (props.connected) return 'success'
  if (props.connecting) return 'warning'
  return 'info'
})

// 事件处理
const handleConnect = () => emit('connect')
const handleDisconnect = () => emit('disconnect')
const handleRequestKeyFrame = () => emit('requestKeyFrame')
const handleResetPlayer = () => emit('resetPlayer')
const handleToggleDecoder = () => emit('toggleDecoder')
const handleSwitchDecoder = (decoderType) => emit('switchDecoder', decoderType)
const handleDiagnose = () => emit('diagnose')
const handleTestConnection = () => emit('testConnection')
const handleCheckCodecSupport = () => emit('checkCodecSupport')
const handleTestWebCodecs = () => {
  // 直接在这里测试WebCodecs
  console.log('🧪 手动测试WebCodecs支持...')

  const apis = ['VideoDecoder', 'VideoEncoder', 'VideoFrame', 'EncodedVideoChunk']
  const results = apis.map(api => ({
    api,
    supported: api in window
  }))

  console.table(results)

  const allSupported = results.every(r => r.supported)
  console.log(`🧪 WebCodecs总体支持: ${allSupported ? '✅ 支持' : '❌ 不支持'}`)

  if (allSupported) {
    try {
      const decoder = new VideoDecoder({
        output: (frame) => {
          console.log('✅ VideoDecoder创建并运行成功')
          frame.close()
        },
        error: (error) => {
          console.error('❌ VideoDecoder错误:', error)
        }
      })
      console.log('✅ VideoDecoder实例创建成功')
    } catch (error) {
      console.error('❌ VideoDecoder创建失败:', error)
    }
  }
}
</script>

<style scoped>
.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(229, 231, 235, 0.8);
  padding: 16px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.control-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.connection-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .control-toolbar {
    flex-direction: column;
    align-items: stretch;
  }

  .connection-controls {
    justify-content: center;
  }

  .status-indicators {
    justify-content: center;
  }
}

/* 深色主题适配 */
:global(.dark) .control-panel {
  background: rgba(15, 23, 42, 0.95);
  border-bottom-color: rgba(51, 65, 85, 0.6);
}
</style>
