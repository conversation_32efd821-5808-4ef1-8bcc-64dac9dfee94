<template>
  <div id="app">
    <!-- 顶部导航栏 -->
    <div class="app-header">
      <div class="header-content">
        <div class="header-left">
          <el-icon class="header-icon"><VideoCamera /></el-icon>
          <h1>WebRTC H264 远程控制系统</h1>
        </div>
        <div class="header-right">
          <div class="connection-status">
            <el-tag
              :type="connected ? 'success' : 'info'"
              size="large"
              :effect="connected ? 'dark' : 'plain'">
              <el-icon><Connection /></el-icon>
              {{ connected ? '已连接' : '未连接' }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="app-main">
      <!-- 顶部工具栏 -->
      <div class="toolbar">
        <!-- 连接控制 -->
        <div class="toolbar-section">
          <div class="toolbar-group">
            <label>服务器地址</label>
            <el-input
              v-model="serverUrl"
              placeholder="http://localhost:8080"
              :disabled="connected"
              size="small"
              class="server-input">
              <template #prefix>
                <el-icon><Link /></el-icon>
              </template>
            </el-input>
          </div>

          <div class="toolbar-group">
            <el-button
              type="primary"
              size="small"
              @click="connect"
              :loading="connecting"
              :disabled="connected"
              class="connect-btn">
              <el-icon><VideoPlay /></el-icon>
              {{ connecting ? '连接中...' : '连接' }}
            </el-button>

            <el-button
              type="danger"
              size="small"
              @click="disconnect"
              :disabled="!connected"
              class="disconnect-btn">
              <el-icon><VideoPause /></el-icon>
              断开
            </el-button>
          </div>
        </div>

        <!-- 状态信息 -->
        <div class="toolbar-section">
          <div class="status-indicators">
            <div class="status-indicator">
              <el-icon :class="connected ? 'status-success' : 'status-default'">
                <Connection />
              </el-icon>
              <span>{{ connectionStatus }}</span>
            </div>

            <div class="status-indicator">
              <el-icon :class="dataChannelOpen ? 'status-success' : 'status-warning'">
                <Message />
              </el-icon>
              <span>{{ dataChannelOpen ? '数据通道已打开' : '数据通道未打开' }}</span>
            </div>

            <div class="status-indicator">
              <el-icon :class="videoReceived ? 'status-success' : 'status-warning'">
                <VideoCamera />
              </el-icon>
              <span>{{ videoReceived ? '正在接收视频' : '等待视频流' }}</span>
            </div>
          </div>
        </div>

        <!-- 控制选项 -->
        <div class="toolbar-section">
          <div class="toolbar-group">
            <label>键鼠控制</label>
            <el-switch
              v-model="mouseControlEnabled"
              size="small"
              :disabled="!dataChannelOpen" />
          </div>

          <div class="toolbar-group">
            <el-button size="small" @click="clearLogs">
              <el-icon><Delete /></el-icon>
              清空日志
            </el-button>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="content-area">
        <!-- 视频区域 -->
        <div class="video-section">
          <div class="video-container" ref="videoContainer" tabindex="0"
               @mousemove="handleMouseMove"
               @mousedown="handleMouseDown"
               @mouseup="handleMouseUp"
               @click="handleMouseClick"
               @dblclick="handleMouseDoubleClick"
               @wheel="handleMouseWheel"
               @contextmenu="handleContextMenu"
               @keydown="handleKeyDown"
               @keyup="handleKeyUp">

            <canvas id="videoCanvas" class="video-stream"></canvas>

            <div v-if="!videoReceived" class="video-placeholder">
              <div class="placeholder-content">
                <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
                <h3>等待 H264 视频流</h3>
                <p>请先连接服务器，然后使用 FFmpeg 推送视频流到端口 5000</p>
                <el-button type="primary" size="small" @click="showStreamingHelp">
                  <el-icon><QuestionFilled /></el-icon>
                  查看推流帮助
                </el-button>
                <br><br>
                <el-button size="small" @click="testCanvas">测试Canvas</el-button>
                <el-button size="small" @click="testAudio">测试音频</el-button>
                <el-button size="small" @click="forceShowVideo">强制显示视频</el-button>
                <el-button size="small" @click="debugStatus">调试状态</el-button>
              </div>
            </div>

            <div v-if="videoReceived && mouseControlEnabled" class="control-hint">
              <el-icon><Mouse /></el-icon>
              <span>键鼠控制已激活</span>
            </div>
          </div>
        </div>

        <!-- 日志区域 -->
        <div class="log-section">
          <div class="log-header">
            <el-icon><Document /></el-icon>
            <span>系统日志</span>
          </div>
          <div class="log-container">
            <div
              v-for="(log, index) in logs"
              :key="index"
              :class="['log-entry', `log-${log.type.toLowerCase()}`]">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type">[{{ log.type }}]</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
            <div v-if="logs.length === 0" class="log-empty">
              <el-icon><Document /></el-icon>
              <span>暂无日志信息</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoCamera,
  Connection,
  Link,
  VideoPlay,
  VideoPause,
  Monitor,
  Message,
  Cpu,
  Setting,
  VideoCameraFilled,
  QuestionFilled,
  Mouse,
  Document,
  Delete
} from '@element-plus/icons-vue'

// Reactive data
const serverUrl = ref('http://localhost:8080')
const connected = ref(false)
const connecting = ref(false)
const connectionStatus = ref('未连接')
const dataChannelOpen = ref(false)
const videoReceived = ref(false)
const mouseControlEnabled = ref(true)
const logs = ref([])

// WebRTC related
let pc = ref(null)
let dc = ref(null)
let audioChannel = ref(null)

// H264 decoding related
let h264Decoder = null
let canvas = null
let canvasCtx = null
let isDecoderConfigured = false
let frameCount = 0
let startTime = 0

// Performance monitoring
let lastFpsCheck = 0
let fpsCounter = 0
let currentFps = 0
let performanceStats = {
  avgDecodeTime: 0,
  avgRenderTime: 0,
  totalFrames: 0,
  droppedFrames: 0
}

// Audio decoding related
let audioDecoder = null
let audioContext = null
let isAudioDecoderConfigured = false
let audioFrameCount = 0
let audioDecodeErrors = 0
let audioFramesPlayed = 0

// Template references
const videoContainer = ref(null)

// 添加日志 - 新日志显示在上方
function addLog(type, message) {
  const now = new Date()
  const time = now.toLocaleTimeString()
  logs.value.unshift({ type, message, time })

  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value.pop()
  }

  console.log(`[${type}] ${message}`)
}

// 清空日志
function clearLogs() {
  logs.value = []
  addLog('INFO', '日志已清空')
}

// 显示推流帮助
function showStreamingHelp() {
  ElMessage.info({
    message: '使用命令: ffmpeg -re -i input.mp4 -c:v libx264 -crf 18 -f h264 udp://localhost:5000',
    duration: 5000,
    showClose: true
  })
}

// 测试Canvas
function testCanvas() {
  if (!canvas || !canvasCtx) {
    addLog('ERROR', 'Canvas未初始化，尝试重新初始化...')
    initCanvas()
    if (!canvas || !canvasCtx) {
      addLog('ERROR', 'Canvas初始化失败')
      return
    }
  }

  // 绘制测试图案
  canvas.width = 640
  canvas.height = 360
  canvasCtx.fillStyle = 'red'
  canvasCtx.fillRect(0, 0, 213, 120)
  canvasCtx.fillStyle = 'green'
  canvasCtx.fillRect(213, 0, 214, 120)
  canvasCtx.fillStyle = 'blue'
  canvasCtx.fillRect(427, 0, 213, 120)

  canvasCtx.fillStyle = 'white'
  canvasCtx.font = '24px Arial'
  canvasCtx.fillText('Canvas Test Pattern', 10, 200)
  canvasCtx.fillText(`Size: ${canvas.width}x${canvas.height}`, 10, 240)
  canvasCtx.fillText(`Display: ${canvas.offsetWidth}x${canvas.offsetHeight}`, 10, 280)

  addLog('SUCCESS', 'Canvas测试图案已绘制')
  console.log('Canvas测试完成:', {
    canvas: !!canvas,
    canvasCtx: !!canvasCtx,
    width: canvas.width,
    height: canvas.height,
    offsetWidth: canvas.offsetWidth,
    offsetHeight: canvas.offsetHeight
  })
}

// 测试音频
function testAudio() {
  if (!audioContext) {
    addLog('ERROR', '音频上下文未初始化，尝试重新初始化...')
    initAudioDecoder()
    if (!audioContext) {
      addLog('ERROR', '音频上下文初始化失败')
      return
    }
  }

  try {
    // 确保音频上下文启动
    if (audioContext.state === 'suspended') {
      audioContext.resume().then(() => {
        addLog('INFO', '音频上下文已恢复')
        playTestTone()
      })
    } else {
      playTestTone()
    }
  } catch (error) {
    addLog('ERROR', `音频测试失败: ${error.message}`)
  }
}

// 播放测试音调
function playTestTone() {
  try {
    const duration = 1 // 1秒
    const frequency = 440 // 440Hz
    const sampleRate = audioContext.sampleRate
    const frames = sampleRate * duration

    const audioBuffer = audioContext.createBuffer(1, frames, sampleRate)
    const channelData = audioBuffer.getChannelData(0)

    // 生成正弦波
    for (let i = 0; i < frames; i++) {
      channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3
    }

    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)
    source.start()

    addLog('SUCCESS', '测试音调已播放 (440Hz, 1秒)')
  } catch (error) {
    addLog('ERROR', `播放测试音调失败: ${error.message}`)
  }
}

// 强制显示视频
function forceShowVideo() {
  videoReceived.value = true
  addLog('INFO', '强制设置videoReceived=true，隐藏占位符')

  nextTick(() => {
    const canvasElem = document.querySelector('#videoCanvas')
    if (canvasElem) {
      canvasElem.style.display = 'block'
      canvasElem.style.position = 'relative'
      canvasElem.style.zIndex = '20'
      canvasElem.style.backgroundColor = 'black'
      addLog('SUCCESS', `Canvas强制显示: ${canvasElem.offsetWidth}x${canvasElem.offsetHeight}`)
    } else {
      addLog('ERROR', 'Canvas元素未找到')
    }
  })
}

// 调试状态
function debugStatus() {
  const canvasElem = document.querySelector('#videoCanvas')
  const placeholderElem = document.querySelector('.video-placeholder')

  addLog('DEBUG', '=== 调试状态信息 ===')
  addLog('DEBUG', `videoReceived: ${videoReceived.value}`)
  addLog('DEBUG', `dataChannelOpen: ${dataChannelOpen.value}`)
  addLog('DEBUG', `frameCount: ${frameCount}`)
  addLog('DEBUG', `canvas存在: ${!!canvasElem}`)
  addLog('DEBUG', `canvasCtx存在: ${!!canvasCtx}`)

  if (canvasElem) {
    addLog('DEBUG', `Canvas尺寸: ${canvasElem.width}x${canvasElem.height}`)
    addLog('DEBUG', `Canvas显示尺寸: ${canvasElem.offsetWidth}x${canvasElem.offsetHeight}`)
    addLog('DEBUG', `Canvas display: ${canvasElem.style.display}`)
    addLog('DEBUG', `Canvas visibility: ${canvasElem.style.visibility}`)
    addLog('DEBUG', `Canvas zIndex: ${canvasElem.style.zIndex}`)
  }

  if (placeholderElem) {
    addLog('DEBUG', `占位符显示: ${placeholderElem.style.display !== 'none'}`)
    addLog('DEBUG', `占位符可见: ${placeholderElem.offsetWidth > 0}`)
  }

  addLog('DEBUG', '=== 调试信息结束 ===')
}

// 获取解码器状态文本
function getDecoderStatusText() {
  if (!h264Decoder) {
    return '未初始化'
  }

  switch (h264Decoder.state) {
    case 'unconfigured':
      return '未配置'
    case 'configured':
      return '已配置'
    case 'closed':
      return '已关闭'
    default:
      return h264Decoder.state || '未知'
  }
}

// 获取解码器状态类型（用于标签颜色）
function getDecoderStatusType() {
  if (!h264Decoder) {
    return 'info'
  }

  switch (h264Decoder.state) {
    case 'configured':
      return 'success'
    case 'closed':
      return 'danger'
    case 'unconfigured':
      return 'warning'
    default:
      return 'info'
  }
}

// 初始化H264解码器
function initH264Decoder() {
  if (!window.VideoDecoder) {
    addLog('ERROR', '浏览器不支持WebCodecs API，需要Chrome 94+')
    ElMessage.error('浏览器不支持WebCodecs API，请使用Chrome 94+')
    return false
  }

  try {
    // 如果已有解码器，先关闭它
    if (h264Decoder) {
      try {
        if (h264Decoder.state !== 'closed') {
          h264Decoder.close()
        }
      } catch (e) {
        console.warn('关闭旧解码器时出错:', e)
      }
    }

    h264Decoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 将解码后的帧绘制到canvas
          if (canvas && canvasCtx) {
            // 动态调整canvas尺寸
            if (canvas.width !== frame.displayWidth || canvas.height !== frame.displayHeight) {
              canvas.width = frame.displayWidth
              canvas.height = frame.displayHeight
              // 设置CSS尺寸以保持宽高比
              const aspectRatio = frame.displayWidth / frame.displayHeight
              const maxWidth = 800
              const maxHeight = 600

              let displayWidth = frame.displayWidth
              let displayHeight = frame.displayHeight

              if (displayWidth > maxWidth) {
                displayWidth = maxWidth
                displayHeight = maxWidth / aspectRatio
              }

              if (displayHeight > maxHeight) {
                displayHeight = maxHeight
                displayWidth = maxHeight * aspectRatio
              }

              canvas.style.width = `${displayWidth}px`
              canvas.style.height = `${displayHeight}px`

              addLog('INFO', `Canvas尺寸调整为: ${frame.displayWidth}x${frame.displayHeight} (显示: ${displayWidth.toFixed(0)}x${displayHeight.toFixed(0)})`)
            }

            // 使用requestAnimationFrame优化渲染
            requestAnimationFrame(() => {
              try {
                // 先清空Canvas
                canvasCtx.clearRect(0, 0, canvas.width, canvas.height)

                // 绘制视频帧
                canvasCtx.drawImage(frame, 0, 0)

                // 移除调试信息显示 - 不再需要在视频上显示调试信息

                frame.close()
              } catch (renderError) {
                addLog('ERROR', `渲染帧时出错: ${renderError.message}`)
                frame.close()
              }
            })

            frameCount++

            // 更新性能统计
            updatePerformanceStats()

            // 减少日志频率
            if (frameCount % 120 === 0) {
              const elapsed = (Date.now() - startTime) / 1000
              const fps = frameCount / elapsed
              addLog('SUCCESS', `已渲染 ${frameCount} 帧H264视频 (${frame.displayWidth}x${frame.displayHeight}, ${fps.toFixed(1)} FPS, 丢帧: ${frameDropCount})`)
            }

            // 每渲染一帧都确保videoReceived状态正确
            if (!videoReceived.value) {
              videoReceived.value = true
              addLog('INFO', '视频帧渲染成功，更新videoReceived状态')
            }
          } else {
            addLog('ERROR', `Canvas未初始化: canvas=${!!canvas}, canvasCtx=${!!canvasCtx}`)
            frame.close() // 确保释放帧资源
          }
        } catch (error) {
          addLog('ERROR', `渲染帧时出错: ${error.message}`)
          console.error('渲染错误详情:', error)
          frame.close() // 确保释放帧资源
        }
      },
      error: (error) => {
        addLog('ERROR', `H264解码错误: ${error.message}`)
        console.error('H264 Decoder Error:', error)

        // 标记解码器为未配置状态
        isDecoderConfigured = false

        // 尝试重新初始化（延迟执行避免递归）
        setTimeout(() => {
          addLog('INFO', '尝试重新初始化H264解码器...')
          initH264Decoder()
        }, 2000)
      }
    })

    // 使用优化的配置选择
    const configs = getOptimalDecoderConfig()

    let configSuccess = false
    for (const config of configs) {
      try {
        h264Decoder.configure(config)
        configSuccess = true
        addLog('SUCCESS', `H264解码器配置成功: ${config.codec} (硬件加速: ${config.hardwareAcceleration})`)
        break
      } catch (e) {
        addLog('WARNING', `配置 ${config.codec} 失败: ${e.message}`)
      }
    }

    if (!configSuccess) {
      throw new Error('所有解码器配置都失败')
    }

    isDecoderConfigured = true
    startTime = Date.now() // 记录开始时间用于FPS计算

    return true
  } catch (error) {
    addLog('ERROR', `H264解码器初始化失败: ${error.message}`)
    console.error('H264 Decoder Init Error:', error)
    isDecoderConfigured = false
    return false
  }
}

// 帧缓冲队列
let frameQueue = []
let isProcessingQueue = false
let lastTimestamp = 0
let frameDropCount = 0

// 处理接收到的H264数据
function handleH264Data(data) {
  if (!h264Decoder || !isDecoderConfigured) {
    addLog('ERROR', 'H264解码器未就绪')
    return
  }

  try {
    // 检查解码器状态
    if (h264Decoder.state !== 'configured') {
      addLog('WARNING', `解码器状态异常: ${h264Decoder.state}，跳过此帧`)
      return
    }

    const uint8Data = new Uint8Array(data)

    // 验证数据完整性
    if (uint8Data.length < 5) {
      addLog('WARNING', `H264数据太短: ${uint8Data.length} 字节`)
      return
    }

    // 改进的NAL单元分析
    const nalUnits = parseNALUnits(uint8Data)
    let isKeyFrame = false
    let frameType = 'unknown'

    for (const nal of nalUnits) {
      const nalType = nal.type
      switch (nalType) {
        case 1: // 非IDR切片
          frameType = 'P帧'
          break
        case 5: // IDR切片
          isKeyFrame = true
          frameType = 'IDR帧'
          break
        case 7: // SPS
          isKeyFrame = true
          frameType = 'SPS'
          break
        case 8: // PPS
          isKeyFrame = true
          frameType = 'PPS'
          break
        case 6: // SEI
          frameType = 'SEI'
          break
      }
    }

    // 生成单调递增的时间戳
    const currentTime = performance.now() * 1000
    if (currentTime <= lastTimestamp) {
      lastTimestamp += 33333 // 强制33.33ms间隔
    } else {
      lastTimestamp = currentTime
    }

    // 创建编码视频块
    const chunk = new EncodedVideoChunk({
      type: isKeyFrame ? 'key' : 'delta',
      timestamp: lastTimestamp,
      data: data,
      duration: 33333 // 30fps，每帧33.33ms
    })

    // 添加到队列而不是直接解码
    frameQueue.push({
      chunk: chunk,
      isKeyFrame: isKeyFrame,
      frameType: frameType,
      size: uint8Data.length,
      nalCount: nalUnits.length
    })

    // 限制队列大小，防止内存溢出
    if (frameQueue.length > 10) {
      const dropped = frameQueue.shift()
      frameDropCount++
      if (frameDropCount % 10 === 0) {
        addLog('WARNING', `帧队列过长，已丢弃 ${frameDropCount} 帧`)
      }
    }

    // 启动队列处理
    processFrameQueue()

    // 减少日志频率，只在关键帧或每60帧记录一次
    if (isKeyFrame || frameCount % 60 === 0) {
      addLog('INFO', `接收H264数据: ${uint8Data.length} 字节, ${frameType}, NAL单元: ${nalUnits.length}, 队列: ${frameQueue.length}`)
    }
  } catch (error) {
    addLog('ERROR', `H264数据处理失败: ${error.message}`)
    console.error('H264 Data Processing Error:', error)
  }
}

// 处理帧队列
async function processFrameQueue() {
  if (isProcessingQueue || frameQueue.length === 0) {
    return
  }

  isProcessingQueue = true

  try {
    while (frameQueue.length > 0) {
      const frameData = frameQueue.shift()

      try {
        // 检查解码器队列大小
        if (h264Decoder.decodeQueueSize > 5) {
          addLog('WARNING', `解码器队列过长: ${h264Decoder.decodeQueueSize}，等待处理`)
          await new Promise(resolve => setTimeout(resolve, 16)) // 等待一帧时间
          continue
        }

        // 解码帧
        h264Decoder.decode(frameData.chunk)

        // 控制解码速度，避免过快
        if (frameQueue.length > 3) {
          await new Promise(resolve => setTimeout(resolve, 8)) // 8ms延迟
        }

      } catch (error) {
        addLog('ERROR', `解码帧失败: ${error.message}`)

        // 如果是关键帧解码失败，尝试重新初始化
        if (frameData.isKeyFrame && error.message.includes('Decoding error')) {
          addLog('INFO', '关键帧解码失败，重新初始化解码器...')
          setTimeout(() => {
            initH264Decoder()
          }, 1000)
          break
        }
      }
    }
  } finally {
    isProcessingQueue = false
  }
}

// 解析NAL单元
function parseNALUnits(data) {
  const nalUnits = []
  let i = 0

  while (i < data.length - 4) {
    // 查找NAL起始码 0x00 0x00 0x00 0x01
    if (data[i] === 0x00 && data[i+1] === 0x00 &&
        data[i+2] === 0x00 && data[i+3] === 0x01) {

      if (i + 4 < data.length) {
        const nalHeader = data[i + 4]
        const nalType = nalHeader & 0x1F
        const nalRefIdc = (nalHeader >> 5) & 0x03

        nalUnits.push({
          type: nalType,
          refIdc: nalRefIdc,
          offset: i,
          header: nalHeader
        })
      }
      i += 4
    } else {
      i++
    }
  }

  return nalUnits
}

// 性能监控和自适应调整
function updatePerformanceStats() {
  const now = performance.now()

  // 计算FPS
  fpsCounter++
  if (now - lastFpsCheck >= 1000) {
    currentFps = fpsCounter
    fpsCounter = 0
    lastFpsCheck = now

    // 自适应调整
    if (currentFps < 20 && frameQueue.length > 5) {
      // FPS过低且队列积压，丢弃一些非关键帧
      const originalLength = frameQueue.length
      frameQueue = frameQueue.filter((frame, index) => {
        return frame.isKeyFrame || index % 2 === 0 // 保留关键帧和一半的P帧
      })
      const dropped = originalLength - frameQueue.length
      frameDropCount += dropped

      if (dropped > 0) {
        addLog('WARNING', `性能优化：丢弃 ${dropped} 个P帧，当前FPS: ${currentFps}`)
      }
    }

    // 更新性能统计
    performanceStats.totalFrames = frameCount
    performanceStats.droppedFrames = frameDropCount

    // 每10秒输出详细统计
    if (frameCount > 0 && frameCount % 300 === 0) {
      const dropRate = (frameDropCount / frameCount * 100).toFixed(1)
      addLog('INFO', `性能统计 - FPS: ${currentFps}, 总帧数: ${frameCount}, 丢帧率: ${dropRate}%, 队列: ${frameQueue.length}`)
    }
  }
}

// 优化解码器配置
function getOptimalDecoderConfig() {
  // 根据性能动态选择配置
  const configs = [
    {
      codec: 'avc1.42001E', // H264 Baseline Profile Level 3.0
      optimizeForLatency: true,
      hardwareAcceleration: 'prefer-hardware'
    },
    {
      codec: 'avc1.42001F', // H264 Baseline Profile Level 3.1
      optimizeForLatency: true,
      hardwareAcceleration: 'prefer-hardware'
    },
    {
      codec: 'avc1.420028', // H264 Baseline Profile Level 4.0
      optimizeForLatency: true,
      hardwareAcceleration: 'prefer-software'
    }
  ]

  // 如果性能较差，优先使用软件解码
  if (currentFps < 15 && frameDropCount > frameCount * 0.1) {
    return configs.reverse() // 优先软件解码
  }

  return configs
}

// 初始化AAC音频解码器
function initAudioDecoder() {
  if (!window.AudioDecoder) {
    addLog('ERROR', '浏览器不支持WebCodecs AudioDecoder API')
    return false
  }

  try {
    // 如果已有解码器，先关闭它
    if (audioDecoder) {
      try {
        if (audioDecoder.state !== 'closed') {
          audioDecoder.close()
        }
      } catch (e) {
        console.warn('关闭旧音频解码器时出错:', e)
      }
    }

    // 初始化音频上下文
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)()
      addLog('INFO', `音频上下文已创建 (采样率: ${audioContext.sampleRate}Hz)`)

      // 尝试启动音频上下文（需要用户交互）
      if (audioContext.state === 'suspended') {
        addLog('INFO', '音频上下文需要用户交互来启动，请点击页面任意位置')
        // 添加点击事件监听器来启动音频
        const startAudio = () => {
          audioContext.resume().then(() => {
            addLog('SUCCESS', '音频上下文已启动')
            document.removeEventListener('click', startAudio)
          }).catch(err => {
            addLog('ERROR', `启动音频上下文失败: ${err.message}`)
          })
        }
        document.addEventListener('click', startAudio, { once: true })
      }
    }

    // 创建音频解码器
    audioDecoder = new AudioDecoder({
      output: (audioData) => {
        try {
          // 播放音频帧
          playAudioFrame(audioData)
        } catch (error) {
          addLog('ERROR', `音频播放失败: ${error.message}`)
        }
      },
      error: (error) => {
        addLog('ERROR', `音频解码器错误: ${error.message}`)
        isAudioDecoderConfigured = false

        // 统计解码错误
        audioDecodeErrors++
        if (audioDecodeErrors > 10) {
          addLog('WARNING', `解码错误过多(${audioDecodeErrors})，可能需要重新初始化`)
          if (audioDecodeErrors > 50) {
            addLog('INFO', '尝试重新初始化音频解码器...')
            setTimeout(() => {
              audioDecodeErrors = 0
              initAudioDecoder()
            }, 2000)
          }
        }
      }
    })

    // 尝试多种AAC配置，优化ADTS兼容性
    const configs = [
      // 优先使用最常见的配置
      { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 2, description: undefined }, // AAC-LC 44.1kHz 立体声
      { codec: 'mp4a.40.2', sampleRate: 48000, numberOfChannels: 2, description: undefined }, // AAC-LC 48kHz 立体声
      { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 1, description: undefined }, // AAC-LC 44.1kHz 单声道
      { codec: 'mp4a.40.2', sampleRate: 22050, numberOfChannels: 2, description: undefined }, // AAC-LC 22.05kHz
      { codec: 'mp4a.40.2', sampleRate: 32000, numberOfChannels: 2, description: undefined }, // AAC-LC 32kHz
    ]

    let configSuccess = false
    for (const config of configs) {
      try {
        audioDecoder.configure(config)
        configSuccess = true
        addLog('SUCCESS', `AAC解码器配置成功: ${config.codec} ${config.sampleRate}Hz ${config.numberOfChannels}声道`)

        // 保存配置信息供后续使用
        window.audioDecoderConfig = config
        break
      } catch (e) {
        addLog('DEBUG', `配置 ${config.sampleRate}Hz/${config.numberOfChannels}ch 失败: ${e.message}`)
      }
    }

    if (!configSuccess) {
      throw new Error('所有AAC解码器配置都失败，请检查浏览器WebCodecs支持')
    }

    isAudioDecoderConfigured = true
    addLog('SUCCESS', 'AAC音频解码器初始化成功')
    return true
  } catch (error) {
    addLog('ERROR', `AAC音频解码器初始化失败: ${error.message}`)
    console.error('Audio Decoder Init Error:', error)
    isAudioDecoderConfigured = false
    return false
  }
}

// 使用MediaSource处理音频流
let mediaSource = null
let sourceBuffer = null
let audioElement = null

// 初始化MediaSource音频播放
function initMediaSourceAudio() {
  try {
    // 创建audio元素
    if (!audioElement) {
      audioElement = document.createElement('audio')
      audioElement.controls = false
      audioElement.autoplay = true
      document.body.appendChild(audioElement)
    }

    // 创建MediaSource
    mediaSource = new MediaSource()
    audioElement.src = URL.createObjectURL(mediaSource)

    mediaSource.addEventListener('sourceopen', () => {
      try {
        // 添加SourceBuffer for AAC
        sourceBuffer = mediaSource.addSourceBuffer('audio/aac')
        sourceBuffer.mode = 'sequence'
        addLog('SUCCESS', 'MediaSource音频初始化成功')
      } catch (e) {
        addLog('ERROR', `SourceBuffer创建失败: ${e.message}`)
      }
    })

    return true
  } catch (error) {
    addLog('ERROR', `MediaSource初始化失败: ${error.message}`)
    return false
  }
}

// 音频时间戳管理
let audioStartTime = null
let audioFrameDuration = 23.22 // AAC帧持续时间（毫秒）

// 处理接收到的音频数据
function handleAudioData(data) {
  // 首次接收音频数据的调试信息
  if (audioFrameCount === 0) {
    addLog('SUCCESS', `首次接收音频数据: ${data.byteLength} 字节`)
    audioStartTime = performance.now()

    // 确保音频解码器已初始化
    if (!audioDecoder || !isAudioDecoderConfigured) {
      addLog('INFO', '首次接收音频数据，初始化AAC解码器...')
      if (!initAudioDecoder()) {
        addLog('ERROR', '无法初始化AAC解码器')
        return
      }
    }
  }

  try {
    // 检查解码器状态
    if (!audioDecoder || audioDecoder.state !== 'configured') {
      if (audioFrameCount < 10) { // 只在前10帧报告状态问题
        addLog('WARNING', `音频解码器状态异常: ${audioDecoder?.state || 'null'}，跳过此帧`)
      }
      return
    }

    const uint8Data = new Uint8Array(data)
    if (uint8Data.length < 7) { // ADTS最小长度为7字节
      addLog('WARNING', `音频数据太短: ${uint8Data.length} 字节，跳过`)
      return
    }

    // 检查ADTS格式
    const isADTS = uint8Data[0] === 0xFF && (uint8Data[1] & 0xF0) === 0xF0
    if (!isADTS) {
      if (audioFrameCount < 5) {
        addLog('WARNING', '非ADTS格式音频数据，可能导致解码问题')
      }
    }

    // 解析ADTS头部获取准确的帧信息
    let sampleRate = 44100
    let channels = 2
    let frameLength = uint8Data.length

    if (isADTS && uint8Data.length >= 7) {
      const freqIndex = (uint8Data[2] >> 2) & 0x0F
      const channelConfig = ((uint8Data[2] & 0x01) << 2) | ((uint8Data[3] >> 6) & 0x03)
      frameLength = ((uint8Data[3] & 0x03) << 11) | (uint8Data[4] << 3) | ((uint8Data[5] & 0xE0) >> 5)

      // 频率映射表
      const sampleRates = [96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350]
      if (freqIndex < sampleRates.length) {
        sampleRate = sampleRates[freqIndex]
      }
      channels = channelConfig || 2

      // 计算准确的帧持续时间
      audioFrameDuration = (1024 * 1000) / sampleRate // AAC每帧1024个样本

      if (audioFrameCount < 3) {
        addLog('DEBUG', `ADTS解析: ${sampleRate}Hz, ${channels}声道, 帧长度=${frameLength}, 持续时间=${audioFrameDuration.toFixed(2)}ms`)
      }
    }

    // 验证帧长度
    if (isADTS && frameLength !== uint8Data.length) {
      if (audioFrameCount < 10) {
        addLog('WARNING', `ADTS帧长度不匹配: 头部=${frameLength}, 实际=${uint8Data.length}`)
      }
    }

    // 创建音频块，使用基于实际时间的时间戳
    const currentTime = performance.now()
    const elapsedTime = audioStartTime ? (currentTime - audioStartTime) : 0
    const expectedTimestamp = audioFrameCount * audioFrameDuration

    // 使用更准确的时间戳（微秒）
    const timestamp = Math.round(expectedTimestamp * 1000)

    const chunk = new EncodedAudioChunk({
      type: 'key',
      timestamp: timestamp,
      data: uint8Data
    })

    // 控制解码器队列大小
    if (audioDecoder.decodeQueueSize > 5) {
      if (audioFrameCount % 50 === 0) { // 减少警告频率
        addLog('WARNING', `解码队列过长: ${audioDecoder.decodeQueueSize}，跳过此帧`)
      }
      return
    }

    // 解码音频
    audioDecoder.decode(chunk)
    audioFrameCount++

    // 定期输出统计信息（减少频率）
    if (audioFrameCount % 200 === 0) {
      const formatStr = isADTS ? 'ADTS-AAC' : 'Raw-AAC'
      const avgSize = Math.round(uint8Data.length)
      addLog('INFO', `音频解码正常: ${audioFrameCount} 帧, ${avgSize}字节/帧, ${formatStr}, ${sampleRate}Hz`)
    }

  } catch (error) {
    addLog('ERROR', `AAC音频解码失败: ${error.message}`)
    console.error('Audio Decode Error:', error, {
      frameCount: audioFrameCount,
      dataLength: data.byteLength,
      decoderState: audioDecoder?.state
    })

    // 错误处理策略
    audioDecodeErrors++
    if (audioDecodeErrors > 10 && audioDecodeErrors % 10 === 0) {
      addLog('WARNING', `累计解码错误: ${audioDecodeErrors} 次`)

      if (audioDecodeErrors > 50) {
        addLog('INFO', '解码错误过多，重新初始化音频解码器...')
        setTimeout(() => {
          audioDecodeErrors = 0
          audioFrameCount = 0
          audioStartTime = null
          initAudioDecoder()
        }, 1000)
      }
    }
  }
}

// 处理PCM音频数据
function handlePCMAudio(pcmData) {
  try {
    if (!audioContext) {
      addLog('ERROR', '音频上下文未初始化')
      return
    }

    // 确保音频上下文处于运行状态
    if (audioContext.state === 'suspended') {
      audioContext.resume()
    }

    // 假设PCM格式：16位立体声，44100Hz
    const sampleRate = 44100
    const numberOfChannels = 2
    const bytesPerSample = 2
    const numberOfFrames = Math.floor(pcmData.length / (numberOfChannels * bytesPerSample))

    if (numberOfFrames <= 0) {
      return
    }

    // 创建音频缓冲区
    const audioBuffer = audioContext.createBuffer(numberOfChannels, numberOfFrames, sampleRate)

    // 转换PCM数据到浮点数
    const leftChannel = audioBuffer.getChannelData(0)
    const rightChannel = numberOfChannels > 1 ? audioBuffer.getChannelData(1) : leftChannel

    for (let i = 0; i < numberOfFrames; i++) {
      const offset = i * numberOfChannels * bytesPerSample

      // 读取16位有符号整数并转换为浮点数 (-1.0 到 1.0)
      const leftSample = ((pcmData[offset] | (pcmData[offset + 1] << 8)) << 16 >> 16) / 32768.0
      leftChannel[i] = leftSample

      if (numberOfChannels > 1 && offset + 3 < pcmData.length) {
        const rightSample = ((pcmData[offset + 2] | (pcmData[offset + 3] << 8)) << 16 >> 16) / 32768.0
        rightChannel[i] = rightSample
      }
    }

    // 创建音频源并播放
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)
    source.start()

    // 更新播放统计
    audioFramesPlayed++
    if (audioFramesPlayed % 50 === 0) {
      addLog('INFO', `PCM音频播放正常，已播放 ${audioFramesPlayed} 帧 (${numberOfFrames} samples)`)
    }

  } catch (error) {
    addLog('ERROR', `PCM音频播放失败: ${error.message}`)
  }
}

// 音频播放队列管理
let audioPlaybackQueue = []
let nextPlayTime = 0
let isPlayingAudio = false

// 播放音频帧
function playAudioFrame(audioData) {
  if (!audioContext) {
    audioData.close()
    addLog('ERROR', '音频上下文未初始化')
    return
  }

  try {
    // 确保音频上下文已启动
    if (audioContext.state === 'suspended') {
      audioContext.resume().then(() => {
        if (audioFramesPlayed === 0) {
          addLog('SUCCESS', '音频上下文已启动')
        }
      }).catch(err => {
        addLog('ERROR', `恢复音频上下文失败: ${err.message}`)
      })
      // 暂停状态下不播放
      audioData.close()
      return
    }

    // 验证音频数据
    if (!audioData || audioData.numberOfFrames === 0) {
      addLog('WARNING', '音频数据无效')
      audioData?.close()
      return
    }

    // 创建音频缓冲区
    const audioBuffer = audioContext.createBuffer(
      audioData.numberOfChannels,
      audioData.numberOfFrames,
      audioData.sampleRate
    )

    // 复制音频数据到缓冲区
    for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
      const channelData = new Float32Array(audioData.numberOfFrames)
      audioData.copyTo(channelData, { planeIndex: channel })
      audioBuffer.copyToChannel(channelData, channel)
    }

    // 创建音频源
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)

    // 计算播放时间，确保连续播放
    const currentTime = audioContext.currentTime
    const bufferDuration = audioData.numberOfFrames / audioData.sampleRate

    if (nextPlayTime <= currentTime) {
      // 如果预定时间已过，立即播放
      nextPlayTime = currentTime
    }

    // 播放音频
    source.start(nextPlayTime)

    // 更新下次播放时间
    nextPlayTime += bufferDuration

    // 防止时间戳漂移
    if (nextPlayTime - currentTime > 0.5) {
      // 如果预定时间超前太多，重置为当前时间
      nextPlayTime = currentTime + bufferDuration
    }

    // 更新播放统计
    audioFramesPlayed++

    // 关闭音频数据
    audioData.close()

    // 记录播放状态
    if (audioFramesPlayed === 1) {
      addLog('SUCCESS', `音频播放开始: ${audioData.sampleRate}Hz, ${audioData.numberOfChannels}声道`)
      isPlayingAudio = true
    } else if (audioFramesPlayed % 200 === 0) {
      // 减少日志频率
      addLog('INFO', `音频播放正常: ${audioFramesPlayed} 帧, ${audioData.sampleRate}Hz, 延迟=${(nextPlayTime - currentTime).toFixed(3)}s`)
    }

    // 错误恢复：如果播放队列过长，清理
    if (nextPlayTime - currentTime > 1.0) {
      addLog('WARNING', '音频播放队列过长，重置时间戳')
      nextPlayTime = currentTime
    }

  } catch (error) {
    addLog('ERROR', `音频播放失败: ${error.message}`)
    console.error('Audio playback error:', error, {
      sampleRate: audioData?.sampleRate,
      channels: audioData?.numberOfChannels,
      frames: audioData?.numberOfFrames,
      contextState: audioContext?.state
    })
    audioData?.close()
  }
}

// 初始化canvas
function initCanvas() {
  const canvasElem = document.querySelector('#videoCanvas')
  if (canvasElem && !canvas) {
    canvas = canvasElem
    canvasCtx = canvas.getContext('2d')
    canvas.style.display = 'block'
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    canvas.style.objectFit = 'contain'
    canvas.style.backgroundColor = 'black' // 临时背景色，便于调试
    addLog('INFO', `Canvas显示模式已激活 (${canvas.offsetWidth}x${canvas.offsetHeight})`)
    console.log('Canvas元素:', canvas)
  } else if (!canvasElem) {
    addLog('ERROR', 'Canvas元素未找到')
  }
}

// 连接服务器
async function connect() {
  if (connecting.value) return

  connecting.value = true
  connectionStatus.value = '连接中...'

  try {
    // 初始化H264解码器
    if (!initH264Decoder()) {
      throw new Error('H264解码器初始化失败')
    }

    // 初始化Canvas
    initCanvas()

    // 创建RTCPeerConnection
    pc.value = new RTCPeerConnection({
      iceServers: [{ urls: 'stun:stun.l.google.com:19302' }]
    })

    // 创建视频数据通道（使用input作为标签名）
    const dataChannel = pc.value.createDataChannel('input', {
      ordered: true
    })

    // 创建音频数据通道
    const audioDataChannel = pc.value.createDataChannel('audio', {
      ordered: true
    })

    // 设置数据通道事件处理
    dataChannel.onopen = () => {
      dc.value = dataChannel
      dataChannelOpen.value = true
      addLog('SUCCESS', '数据通道已打开')
      ElMessage.success('数据通道已打开，可以开始传输')
    }

    dataChannel.onmessage = (event) => {
      try {
        if (event.data instanceof ArrayBuffer) {
          // 确保解码器已初始化
          if (!h264Decoder || !isDecoderConfigured) {
            addLog('INFO', '首次接收数据，初始化H264解码器...')
            if (!initH264Decoder()) {
              addLog('ERROR', '无法初始化H264解码器')
              return
            }
          }

          handleH264Data(event.data)
          if (!videoReceived.value) {
            videoReceived.value = true
            addLog('SUCCESS', '开始接收H264视频流，隐藏占位符')
            // 确保Canvas可见
            nextTick(() => {
              const canvasElem = document.querySelector('#videoCanvas')
              if (canvasElem) {
                canvasElem.style.display = 'block'
                canvasElem.style.zIndex = '10'
                console.log('Canvas强制显示:', canvasElem.style.display)
              }
            })
          }
        } else if (typeof event.data === 'string') {
          // 处理文本消息（用于调试或控制）
          addLog('INFO', `收到文本消息: ${event.data}`)
        }
      } catch (error) {
        addLog('ERROR', `处理数据通道消息失败: ${error.message}`)
      }
    }

    // 设置音频数据通道事件处理
    audioDataChannel.onopen = () => {
      audioChannel.value = audioDataChannel
      addLog('SUCCESS', '音频数据通道已打开')
      initAudioDecoder()
    }

    audioDataChannel.onmessage = (event) => {
      try {
        if (event.data instanceof ArrayBuffer) {
          const dataSize = event.data.byteLength

          // 检查数据大小，允许PCM数据（可能较大）
          if (dataSize > 100000) {
            addLog('WARNING', `音频数据过大 (${dataSize} 字节)，可能是视频数据，跳过`)
            return
          }

          // 调试大数据包
          if (dataSize > 10000) {
            addLog('DEBUG', `接收到大音频包: ${dataSize} 字节，可能是PCM数据`)
          }

          // 首次接收音频数据的详细信息
          if (audioFrameCount === 0) {
            addLog('SUCCESS', `首次接收音频数据: ${dataSize} 字节`)
            const uint8Array = new Uint8Array(event.data)
            const firstBytes = Array.from(uint8Array.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')
            addLog('DEBUG', `音频数据前8字节: ${firstBytes}`)
          }

          // 确保音频解码器已初始化
          if (!audioDecoder || !isAudioDecoderConfigured) {
            addLog('INFO', '首次接收音频数据，初始化AAC解码器...')
            if (!initAudioDecoder()) {
              addLog('ERROR', '无法初始化AAC解码器')
              return
            }
          }

          handleAudioData(event.data)
          audioFrameCount++
          if (audioFrameCount % 50 === 0) {
            addLog('INFO', `已接收 ${audioFrameCount} 个音频帧 (${dataSize} 字节)`)
          }
        }
      } catch (error) {
        addLog('ERROR', `处理音频数据失败: ${error.message}`)
      }
    }

    audioDataChannel.onerror = (error) => {
      addLog('ERROR', `音频数据通道错误: ${error}`)
    }

    audioDataChannel.onclose = () => {
      addLog('INFO', '音频数据通道已关闭')
      audioChannel.value = null
    }

    // 设置ICE候选处理
    pc.value.onicecandidate = async (event) => {
      if (event.candidate) {
        try {
          const response = await fetch(`${serverUrl.value}/ice`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              ice: event.candidate.candidate,
              sdpMid: event.candidate.sdpMid,
              sdpMLineIndex: event.candidate.sdpMLineIndex
            })
          })
          if (response.ok) {
            addLog('INFO', 'ICE候选已发送')
          } else {
            // ICE错误不影响功能，只记录警告
            addLog('WARNING', `ICE候选发送失败: ${response.status}`)
          }
        } catch (error) {
          addLog('WARNING', `发送ICE候选失败: ${error.message}`)
        }
      }
    }

    // 设置连接状态变化处理
    pc.value.onconnectionstatechange = () => {
      const state = pc.value.connectionState
      connectionStatus.value = state
      addLog('INFO', `连接状态: ${state}`)

      if (state === 'connected') {
        connected.value = true
        ElMessage.success('WebRTC连接已建立')
      } else if (state === 'failed' || state === 'disconnected') {
        connected.value = false
        ElMessage.error('WebRTC连接失败或断开')
      }
    }

    // 设置连接状态变化处理
    pc.value.onconnectionstatechange = () => {
      const state = pc.value.connectionState
      connectionStatus.value = state
      addLog('INFO', `连接状态: ${state}`)

      if (state === 'connected') {
        connected.value = true
        ElMessage.success('WebRTC连接已建立')
      } else if (state === 'failed' || state === 'disconnected') {
        connected.value = false
        ElMessage.error('WebRTC连接失败或断开')
      }
    }

    // 创建offer
    const offer = await pc.value.createOffer()
    await pc.value.setLocalDescription(offer)

    // 等待ICE gathering完成或超时
    await new Promise((resolve) => {
      const timeout = setTimeout(resolve, 3000) // 3秒超时

      if (pc.value.iceGatheringState === 'complete') {
        clearTimeout(timeout)
        resolve()
      } else {
        const handler = () => {
          if (pc.value.iceGatheringState === 'complete') {
            clearTimeout(timeout)
            pc.value.removeEventListener('icegatheringstatechange', handler)
            resolve()
          }
        }
        pc.value.addEventListener('icegatheringstatechange', handler)
      }
    })

    // 发送offer到服务器
    const offerResponse = await fetch(`${serverUrl.value}/offer`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'offer',
        sdp: pc.value.localDescription.sdp
      })
    })

    if (!offerResponse.ok) {
      const errorText = await offerResponse.text()
      throw new Error(`发送offer失败: ${offerResponse.status} - ${errorText}`)
    }

    const answer = await offerResponse.json()
    await pc.value.setRemoteDescription({
      type: 'answer',
      sdp: answer.sdp
    })

    addLog('SUCCESS', 'SDP协商完成')

  } catch (error) {
    addLog('ERROR', `连接失败: ${error.message}`)
    ElMessage.error(`连接失败: ${error.message}`)
    connectionStatus.value = '连接失败'
  } finally {
    connecting.value = false
  }
}

// 断开连接
function disconnect() {
  addLog('INFO', '正在断开连接...')

  // 清理H264解码器
  if (h264Decoder) {
    try {
      if (h264Decoder.state !== 'closed') {
        h264Decoder.close()
      }
    } catch (error) {
      console.error('关闭H264解码器失败:', error)
    }
    h264Decoder = null
    isDecoderConfigured = false
  }

  // 清理音频解码器
  if (audioDecoder) {
    try {
      if (audioDecoder.state !== 'closed') {
        audioDecoder.close()
      }
    } catch (error) {
      console.error('关闭音频解码器失败:', error)
    }
    audioDecoder = null
    isAudioDecoderConfigured = false
  }

  // 重置计数器和状态
  frameCount = 0
  audioFrameCount = 0
  audioFramesPlayed = 0
  audioDecodeErrors = 0
  audioStartTime = null
  nextPlayTime = 0
  isPlayingAudio = false

  // 清空canvas
  if (canvasCtx && canvas) {
    canvasCtx.clearRect(0, 0, canvas.width, canvas.height)
  }
  canvas = null
  canvasCtx = null

  // 清理WebRTC连接
  if (pc.value) {
    try {
      pc.value.close()
    } catch (error) {
      console.error('关闭PeerConnection失败:', error)
    }
    pc.value = null
  }
  if (dc.value) {
    try {
      dc.value.close()
    } catch (error) {
      console.error('关闭DataChannel失败:', error)
    }
    dc.value = null
  }
  if (audioChannel.value) {
    try {
      audioChannel.value.close()
    } catch (error) {
      console.error('关闭AudioChannel失败:', error)
    }
    audioChannel.value = null
  }

  // 重置状态
  connected.value = false
  dataChannelOpen.value = false
  videoReceived.value = false
  connectionStatus.value = '未连接'

  addLog('INFO', '已断开连接并清理所有资源')
  ElMessage.info('已断开连接')
}

// 发送鼠标/键盘事件
function sendInputEvent(eventData) {
  if (!mouseControlEnabled.value || !dc.value || dc.value.readyState !== 'open') {
    return
  }

  try {
    dc.value.send(JSON.stringify(eventData))
    // 添加键鼠事件日志
    if (eventData.type === 'mousemove') {
      // 鼠标移动事件太频繁，只记录部分
      if (Math.random() < 0.01) { // 1%的概率记录
        addLog('INPUT', `鼠标移动: (${eventData.x}, ${eventData.y})`)
      }
    } else if (eventData.type === 'mouseclick') {
      addLog('INPUT', `鼠标点击: (${eventData.x}, ${eventData.y}) 按钮${eventData.button}`)
    } else if (eventData.type === 'mousedown') {
      addLog('INPUT', `鼠标按下: (${eventData.x}, ${eventData.y}) 按钮${eventData.button}`)
    } else if (eventData.type === 'mouseup') {
      addLog('INPUT', `鼠标释放: (${eventData.x}, ${eventData.y}) 按钮${eventData.button}`)
    } else if (eventData.type === 'mousedblclick') {
      addLog('INPUT', `鼠标双击: (${eventData.x}, ${eventData.y})`)
    } else if (eventData.type === 'mousewheel') {
      addLog('INPUT', `鼠标滚轮: (${eventData.x}, ${eventData.y}) deltaY=${eventData.deltaY}`)
    } else if (eventData.type === 'keydown') {
      addLog('INPUT', `按键按下: ${eventData.key} (${eventData.code})`)
    } else if (eventData.type === 'keyup') {
      addLog('INPUT', `按键释放: ${eventData.key} (${eventData.code})`)
    }
  } catch (error) {
    addLog('ERROR', `发送输入事件失败: ${error.message}`)
  }
}

// 鼠标事件处理
function handleMouseMove(event) {
  if (!mouseControlEnabled.value) return
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'mousemove',
    x: x,
    y: y
  })
}

function handleMouseDown(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'mousedown',
    x: x,
    y: y,
    button: event.button,
    buttons: event.buttons
  })
}

function handleMouseUp(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'mouseup',
    x: x,
    y: y,
    button: event.button,
    buttons: event.buttons
  })
}

function handleMouseClick(event) {
  // 确保视频容器获得焦点以接收键盘事件
  if (videoContainer.value) {
    videoContainer.value.focus()
  }

  if (!mouseControlEnabled.value) return
  event.preventDefault()

  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)

  sendInputEvent({
    type: 'mouseclick',
    x: x,
    y: y,
    button: event.button
  })
}

function handleMouseDoubleClick(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'mousedblclick',
    x: x,
    y: y,
    button: event.button
  })
}

function handleMouseWheel(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'mousewheel',
    x: x,
    y: y,
    deltaX: event.deltaX,
    deltaY: event.deltaY,
    deltaZ: event.deltaZ,
    deltaMode: event.deltaMode
  })
}

function handleContextMenu(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  sendInputEvent({
    type: 'contextmenu',
    x: x,
    y: y
  })
}

// 键盘事件处理
function handleKeyDown(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  sendInputEvent({
    type: 'keydown',
    key: event.key,
    code: event.code,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    repeat: event.repeat
  })
}

function handleKeyUp(event) {
  if (!mouseControlEnabled.value) return
  event.preventDefault()
  
  sendInputEvent({
    type: 'keyup',
    key: event.key,
    code: event.code,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey
  })
}

// 组件挂载时的处理
onMounted(() => {
  addLog('INFO', 'WebRTC H264远程控制系统已启动')

  // 检测GPU信息
  const canvas = document.createElement('canvas')
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl')
  if (gl) {
    const renderer = gl.getParameter(gl.RENDERER)
    const vendor = gl.getParameter(gl.VENDOR)
    addLog('INFO', `GPU信息: ${vendor} ${renderer}`)
  } else {
    addLog('WARNING', 'WebGL不可用，可能影响GPU加速性能')
  }

  // 确保视频容器可以获得焦点
  nextTick(() => {
    if (videoContainer.value) {
      videoContainer.value.focus()
      addLog('INFO', '视频容器已获得焦点，可以接收键盘事件')
    }
  })

  // 添加全局键盘事件监听作为备用
  document.addEventListener('keydown', (event) => {
    if (mouseControlEnabled.value && dataChannelOpen.value) {
      // 只有当视频容器有焦点或者鼠标在视频区域时才处理
      if (document.activeElement === videoContainer.value ||
          videoContainer.value?.contains(document.activeElement)) {
        handleKeyDown(event)
      }
    }
  })

  document.addEventListener('keyup', (event) => {
    if (mouseControlEnabled.value && dataChannelOpen.value) {
      if (document.activeElement === videoContainer.value ||
          videoContainer.value?.contains(document.activeElement)) {
        handleKeyUp(event)
      }
    }
  })
})

// 组件卸载时的清理
onUnmounted(() => {
  disconnect()
})
</script>

<style scoped>
* {
  box-sizing: border-box;
}

#app {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  background: #f8fafc;
  margin: 0;
  padding: 0;
  color: #1e293b;
}

/* 顶部导航栏 */
.app-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  color: #3b82f6;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
}

.header-right {
  display: flex;
  align-items: center;
}

.connection-status .el-tag {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 6px;
}

/* 主要内容区域 */
.app-main {
  height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  padding: 0;
}

/* 顶部工具栏 */
.toolbar {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 12px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 24px;
}

.toolbar-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-group label {
  font-size: 12px;
  color: #6b7280;
  font-weight: 500;
  white-space: nowrap;
}

.server-input {
  width: 200px;
}

.status-indicators {
  display: flex;
  align-items: center;
  gap: 16px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #6b7280;
}

.status-indicator .el-icon {
  font-size: 14px;
}

/* 主要内容区域 */
.content-area {
  flex: 1;
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 0;
  overflow: hidden;
}

/* 视频区域 */
.video-section {
  background: #1f2937;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.video-container {
  width: 100%;
  height: 100%;
  background: #1f2937;
  outline: none;
  cursor: crosshair;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.video-container:focus {
  box-shadow: inset 0 0 0 2px #3b82f6;
}

.video-stream {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  display: block !important;
  position: relative;
  z-index: 10;
}

.video-placeholder {
  text-align: center;
  color: #9ca3af;
  padding: 40px;
}

.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.placeholder-icon {
  font-size: 64px;
  color: #6b7280;
}

.video-placeholder h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #d1d5db;
}

.video-placeholder p {
  margin: 0;
  font-size: 14px;
  color: #9ca3af;
  max-width: 400px;
  line-height: 1.5;
}

.control-hint {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 6px;
}

/* 日志区域 */
.log-section {
  background: white;
  border-left: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.log-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  background: #f8fafc;
}

.log-header .el-icon {
  font-size: 16px;
  color: #6b7280;
}

.log-container {
  flex: 1;
  overflow-y: auto;
  background: #1f2937;
  padding: 12px;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  font-size: 11px;
}

/* 按钮样式 */
.connect-btn, .disconnect-btn {
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.connect-btn {
  background: #3b82f6;
  border-color: #3b82f6;
}

.connect-btn:hover {
  background: #2563eb;
  border-color: #2563eb;
}

.disconnect-btn {
  background: #ef4444;
  border-color: #ef4444;
}

.disconnect-btn:hover {
  background: #dc2626;
  border-color: #dc2626;
}

/* 状态指示器 */
.status-success {
  color: #10b981;
}

.status-warning {
  color: #f59e0b;
}

.status-default {
  color: #6b7280;
}

/* 日志条目样式 */
.log-entry {
  margin-bottom: 3px;
  line-height: 1.3;
  padding: 1px 0;
}

.log-time {
  color: #9ca3af;
  margin-right: 8px;
  font-weight: 400;
}

.log-type {
  margin-right: 8px;
  font-weight: 600;
  font-size: 10px;
}

.log-message {
  color: #e5e7eb;
}

.log-success .log-type {
  color: #10b981;
}

.log-info .log-type {
  color: #3b82f6;
}

.log-warning .log-type {
  color: #f59e0b;
}

.log-error .log-type {
  color: #ef4444;
}

.log-input .log-type {
  color: #6b7280;
}

.log-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  gap: 8px;
}

.log-empty .el-icon {
  font-size: 24px;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* 日志条目样式 */
.log-entry {
  margin-bottom: 4px;
  line-height: 1.4;
  padding: 2px 0;
}

.log-time {
  color: #9ca3af;
  margin-right: 8px;
  font-weight: 400;
}

.log-type {
  margin-right: 8px;
  font-weight: 600;
  font-size: 11px;
}

.log-message {
  color: #e5e7eb;
}

.log-success .log-type {
  color: #10b981;
}

.log-info .log-type {
  color: #3b82f6;
}

.log-warning .log-type {
  color: #f59e0b;
}

.log-error .log-type {
  color: #ef4444;
}

.log-input .log-type {
  color: #6b7280;
}

/* 删除重复的视频占位符样式 */

.placeholder-commands {
  display: flex;
  gap: 8px;
  margin: 8px 0;
}

.control-hint {
  position: absolute;
  top: 16px;
  right: 16px;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 13px;
  pointer-events: none;
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 日志面板样式重写 */
.log-container {
  height: 200px;
  overflow-y: auto;
  background: #1f2937;
  border-radius: 8px;
  padding: 12px;
  font-family: 'SF Mono', 'Monaco', 'Cascadia Code', monospace;
  font-size: 12px;
  border: 1px solid #374151;
}

.log-entry {
  margin-bottom: 6px;
  line-height: 1.5;
  padding: 4px 8px;
  border-left: 3px solid transparent;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.log-entry:hover {
  background: rgba(255, 255, 255, 0.05);
}

.log-message {
  color: #f8f9fa;
}

.log-success {
  border-left-color: #40c057;
}

.log-success .log-type {
  color: #40c057;
  background: rgba(64, 192, 87, 0.1);
}

.log-info {
  border-left-color: #339af0;
}

.log-info .log-type {
  color: #339af0;
  background: rgba(51, 154, 240, 0.1);
}

.log-warning {
  border-left-color: #fab005;
}

.log-warning .log-type {
  color: #fab005;
  background: rgba(250, 176, 5, 0.1);
}

.log-error {
  border-left-color: #fa5252;
}

.log-error .log-type {
  color: #fa5252;
  background: rgba(250, 82, 82, 0.1);
}

.log-input {
  border-left-color: #868e96;
}

.log-input .log-type {
  color: #868e96;
  background: rgba(134, 142, 150, 0.1);
}

.log-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6c757d;
  gap: 8px;
}

.log-empty .el-icon {
  font-size: 32px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-grid {
    grid-template-columns: 320px 1fr;
    gap: 20px;
  }

  .app-main {
    padding: 20px;
  }
}

@media (max-width: 768px) {
  .main-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .app-main {
    padding: 16px;
  }

  .header-content {
    padding: 0 16px;
    height: 60px;
  }

  .header-left h1 {
    font-size: 20px;
  }

  .toolbar {
    padding: 8px 16px;
  }

  .server-input {
    width: 150px;
  }

  .status-indicators {
    flex-direction: column;
    gap: 8px;
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .content-area {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr 200px;
  }

  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-section {
    justify-content: space-between;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #374151;
}

::-webkit-scrollbar-thumb {
  background: #6b7280;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>


