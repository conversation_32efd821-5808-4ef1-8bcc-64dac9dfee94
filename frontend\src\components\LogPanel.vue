<template>
  <el-card class="logs-card">
    <template #header>
      <div class="card-header">
        <el-icon><Document /></el-icon>
        连接日志
        <div class="header-actions">
          <el-button size="small" @click="clearLogs">清空</el-button>
          <el-button size="small" @click="exportLogs">导出</el-button>
        </div>
      </div>
    </template>

    <div class="logs-container" ref="logsContainer">
      <div
        v-for="(log, index) in filteredLogs"
        :key="index"
        :class="['log-entry', `log-${(log.type || 'info').toLowerCase()}`]">
        <span class="log-time">{{ log.time || '' }}</span>
        <span class="log-type">{{ log.type || 'INFO' }}</span>
        <span class="log-message">{{ log.message || '' }}</span>
      </div>

      <div v-if="filteredLogs.length === 0" class="empty-logs">
        暂无日志
      </div>
    </div>

    <!-- 日志过滤器 -->
    <div class="log-filters">
      <el-checkbox-group v-model="visibleLogTypes" size="small">
        <el-checkbox label="INFO">信息</el-checkbox>
        <el-checkbox label="SUCCESS">成功</el-checkbox>
        <el-checkbox label="WARNING">警告</el-checkbox>
        <el-checkbox label="ERROR">错误</el-checkbox>
      </el-checkbox-group>
      
      <div class="filter-actions">
        <el-button size="small" @click="selectAllTypes">全选</el-button>
        <el-button size="small" @click="clearAllTypes">清空</el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { Document } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  logs: {
    type: Array,
    default: () => []
  },
  maxLogs: {
    type: Number,
    default: 1000
  },
  autoScroll: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['clear', 'export'])

// 响应式数据
const logsContainer = ref(null)
const visibleLogTypes = ref(['INFO', 'SUCCESS', 'WARNING', 'ERROR'])

// 计算属性 - 过滤后的日志
const filteredLogs = computed(() => {
  return props.logs.filter(log => visibleLogTypes.value.includes(log.type))
})

// 监听日志变化，自动滚动到底部
watch(() => props.logs.length, async () => {
  if (props.autoScroll) {
    await nextTick()
    scrollToBottom()
  }
})

// 方法
function clearLogs() {
  emit('clear')
}

function exportLogs() {
  const logText = props.logs.map(log => 
    `[${log.time}] ${log.type}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `webrtc-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  
  emit('export')
}

function scrollToBottom() {
  if (logsContainer.value) {
    logsContainer.value.scrollTop = logsContainer.value.scrollHeight
  }
}

function selectAllTypes() {
  visibleLogTypes.value = ['INFO', 'SUCCESS', 'WARNING', 'ERROR']
}

function clearAllTypes() {
  visibleLogTypes.value = []
}

// 暴露方法给父组件
defineExpose({
  scrollToBottom,
  clearLogs,
  exportLogs
})
</script>

<style scoped>
.logs-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  width: 100%;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.logs-container {
  height: 400px;
  overflow-y: auto;
  background: rgba(249, 250, 251, 0.8);
  border-radius: 6px;
  padding: 12px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-entry {
  display: flex;
  gap: 12px;
  padding: 4px 0;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #6b7280;
  font-size: 12px;
  min-width: 80px;
  flex-shrink: 0;
}

.log-type {
  font-weight: 600;
  min-width: 70px;
  flex-shrink: 0;
}

.log-message {
  color: #374151;
  word-break: break-word;
  flex: 1;
}

/* 日志类型颜色 */
.log-info .log-type {
  color: #3b82f6;
}

.log-success .log-type {
  color: #10b981;
}

.log-warning .log-type {
  color: #f59e0b;
}

.log-error .log-type {
  color: #ef4444;
}

.empty-logs {
  text-align: center;
  color: #9ca3af;
  padding: 40px 0;
  font-style: italic;
}

.log-filters {
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

/* 滚动条样式 */
.logs-container::-webkit-scrollbar {
  width: 6px;
}

.logs-container::-webkit-scrollbar-track {
  background: rgba(229, 231, 235, 0.3);
  border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb {
  background: rgba(156, 163, 175, 0.5);
  border-radius: 3px;
}

.logs-container::-webkit-scrollbar-thumb:hover {
  background: rgba(156, 163, 175, 0.7);
}

/* 深色主题适配 */
:global(.dark) .logs-card {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(51, 65, 85, 0.8);
}

:global(.dark) .card-header {
  color: #f1f5f9;
}

:global(.dark) .logs-container {
  background: rgba(15, 23, 42, 0.8);
}

:global(.dark) .log-message {
  color: #f1f5f9;
}

:global(.dark) .log-filters {
  border-top-color: rgba(51, 65, 85, 0.6);
}

:global(.dark) .empty-logs {
  color: #64748b;
}
</style>
