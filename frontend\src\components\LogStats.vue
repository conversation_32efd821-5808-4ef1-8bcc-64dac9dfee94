<template>
  <div class="log-stats">
    <div class="stats-header">
      <div class="header-title">
        <el-icon><Document /></el-icon>
        <span>日志统计</span>
      </div>
      <div class="header-actions">
        <el-button
          :icon="Refresh"
          size="small"
          text
          @click="refreshStats"
          title="刷新统计" />
        <el-button
          :icon="Download"
          size="small"
          text
          @click="exportLogs"
          title="导出日志" />
        <el-button
          :icon="Delete"
          size="small"
          text
          type="danger"
          @click="clearLogs"
          title="清空日志" />
      </div>
    </div>

    <!-- 紧凑统计行 -->
    <div class="stats-compact">
      <div class="compact-stat total">
        <span class="stat-number">{{ logsStore.logStats.total }}</span>
        <span class="stat-text">总计</span>
      </div>
      <div class="compact-stat error">
        <span class="stat-number">{{ logsStore.logStats.error }}</span>
        <span class="stat-text">错误</span>
      </div>
      <div class="compact-stat warning">
        <span class="stat-number">{{ logsStore.logStats.warning }}</span>
        <span class="stat-text">警告</span>
      </div>
      <div class="compact-stat success">
        <span class="stat-number">{{ logsStore.logStats.success }}</span>
        <span class="stat-text">成功</span>
      </div>
    </div>

    <!-- 最近日志预览 -->
    <div class="recent-logs">
      <div class="recent-header">
        <span>最近日志</span>
        <el-button
          size="small"
          text
          @click="$router.push('/logs')">
          查看全部
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <div class="recent-list">
        <div
          v-for="log in recentLogs"
          :key="log.id"
          :class="['recent-item', `log-${log.type.toLowerCase()}`]">
          <div class="log-time">{{ log.time }}</div>
          <div class="log-type">[{{ log.type }}]</div>
          <div class="log-message">{{ log.message }}</div>
        </div>
        
        <div v-if="recentLogs.length === 0" class="no-logs">
          <el-icon><Document /></el-icon>
          <span>暂无日志</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  DataAnalysis,
  CircleCloseFilled,
  WarningFilled,
  CircleCheckFilled,
  InfoFilled,
  Tools,
  Refresh,
  Download,
  Delete,
  ArrowRight
} from '@element-plus/icons-vue'

import { useLogsStore } from '../stores/logs'

const router = useRouter()
const logsStore = useLogsStore()

// 计算属性
const recentLogs = computed(() => {
  return logsStore.logs.slice(0, 12) // 显示最近12条日志
})

// 方法
function refreshStats() {
  // 强制更新统计信息（实际上是响应式的，这里主要是用户反馈）
  ElMessage.success('统计信息已刷新')
}

function exportLogs() {
  logsStore.exportLogs()
}

function clearLogs() {
  ElMessageBox.confirm(
    '确定要清空所有日志吗？此操作不可撤销。',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    logsStore.clearLogs()
    ElMessage.success('日志已清空')
  }).catch(() => {
    // 用户取消
  })
}
</script>

<style scoped>
.log-stats {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border-radius: 12px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.log-stats:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 13px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-title .el-icon {
  font-size: 20px;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-actions {
  display: flex;
  gap: 8px;
}

/* 紧凑统计行 */
.stats-compact {
  display: flex;
  justify-content: space-between;
  margin-bottom: 12px;
  padding: 8px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.compact-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.stat-number {
  font-size: 16px;
  font-weight: 700;
  line-height: 1;
}

.stat-text {
  font-size: 10px;
  font-weight: 500;
  opacity: 0.8;
  line-height: 1;
}

.compact-stat.total .stat-number {
  color: #6366f1;
}

.compact-stat.error .stat-number {
  color: #ef4444;
}

.compact-stat.warning .stat-number {
  color: #f59e0b;
}

.compact-stat.success .stat-number {
  color: #22c55e;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  border-radius: 16px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  background: linear-gradient(135deg, rgba(248, 250, 252, 1) 0%, rgba(241, 245, 249, 1) 100%);
}

.stat-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 12px;
  color: white;
  font-size: 18px;
}

.stat-card.total .stat-icon {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

.stat-card.error .stat-icon {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
}

.stat-card.warning .stat-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.stat-card.success .stat-icon {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.stat-card.info .stat-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
}

.stat-card.debug .stat-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.stat-card.info {
  border-left-color: var(--el-color-info);
}

.stat-card.debug {
  border-left-color: var(--el-color-purple);
}

.stat-icon {
  font-size: 20px;
  opacity: 0.8;
}

.stat-card.total .stat-icon {
  color: var(--el-color-primary);
}

.stat-card.error .stat-icon {
  color: var(--el-color-danger);
}

.stat-card.warning .stat-icon {
  color: var(--el-color-warning);
}

.stat-card.success .stat-icon {
  color: var(--el-color-success);
}

.stat-card.info .stat-icon {
  color: var(--el-color-info);
}

.stat-card.debug .stat-icon {
  color: var(--el-color-purple);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 2px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.recent-logs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.recent-list {
  flex: 1;
  overflow-y: auto;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.recent-item {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 6px 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  transition: background-color 0.2s;
}

.recent-item:hover {
  background-color: var(--el-fill-color-lighter);
}

.log-time {
  color: var(--el-text-color-secondary);
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 60px;
}

.log-type {
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 50px;
}

.log-message {
  flex: 1;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 不同日志类型的颜色 */
.log-success .log-type {
  color: var(--el-color-success);
}

.log-error .log-type {
  color: var(--el-color-danger);
}

.log-warning .log-type {
  color: var(--el-color-warning);
}

.log-info .log-type {
  color: var(--el-color-info);
}

.log-debug .log-type {
  color: var(--el-color-purple);
}

.log-input .log-type {
  color: var(--el-color-primary);
}

.no-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: var(--el-text-color-placeholder);
  gap: 8px;
}

/* 滚动条样式 */
.recent-list::-webkit-scrollbar {
  width: 4px;
}

.recent-list::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.recent-list::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 2px;
}

.recent-list::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 深色主题适配 */
:global(.dark) .log-stats {
  background: var(--el-bg-color-page);
}
</style>
