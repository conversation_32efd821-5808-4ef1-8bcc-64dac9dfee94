<template>
  <div class="log-stats">
    <div class="stats-header">
      <div class="header-title">
        <el-icon><Document /></el-icon>
        <span>日志统计</span>
      </div>
      <div class="header-actions">
        <el-button
          :icon="Refresh"
          size="small"
          text
          @click="refreshStats"
          title="刷新统计" />
        <el-button
          :icon="Delete"
          size="small"
          text
          type="danger"
          @click="clearLogs"
          title="清空日志" />
      </div>
    </div>

    <!-- 紧凑统计行 -->
    <div class="stats-compact">
      <div class="compact-stat total">
        <span class="stat-number">{{ logsStore.logStats.total }}</span>
        <span class="stat-text">总计</span>
      </div>
      <div class="compact-stat error">
        <span class="stat-number">{{ logsStore.logStats.error }}</span>
        <span class="stat-text">错误</span>
      </div>
      <div class="compact-stat warning">
        <span class="stat-number">{{ logsStore.logStats.warning }}</span>
        <span class="stat-text">警告</span>
      </div>
      <div class="compact-stat success">
        <span class="stat-number">{{ logsStore.logStats.success }}</span>
        <span class="stat-text">成功</span>
      </div>
    </div>

    <!-- 最近日志预览 -->
    <div class="recent-logs">
      <div class="recent-header">
        <span>最近日志</span>
        <el-button
          size="small"
          text
          @click="$router.push('/logs')">
          查看全部
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </div>
      
      <div class="recent-list">
        <div
          v-for="log in recentLogs"
          :key="log.id"
          :class="['recent-item', `log-${log.type.toLowerCase()}`]">
          <div class="log-time">{{ log.time }}</div>
          <div class="log-type">[{{ log.type }}]</div>
          <div class="log-message">{{ log.message }}</div>
        </div>
        
        <div v-if="recentLogs.length === 0" class="no-logs">
          <el-icon><Document /></el-icon>
          <span>暂无日志</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Document,
  Refresh,
  Delete,
  ArrowRight
} from '@element-plus/icons-vue'

import { useLogsStore } from '../stores/logs'

const router = useRouter()
const logsStore = useLogsStore()

// 计算属性
const recentLogs = computed(() => {
  return logsStore.logs.slice(0, 15) // 显示最近15条日志
})

// 方法
function refreshStats() {
  ElMessage.success('统计已刷新')
}

function clearLogs() {
  ElMessageBox.confirm(
    '确定要清空所有日志吗？此操作不可恢复。',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    logsStore.clearLogs()
    ElMessage.success('日志已清空')
  }).catch(() => {
    // 用户取消
  })
}
</script>

<style scoped>
.log-stats {
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border-radius: 8px;
  padding: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(226, 232, 240, 0.6);
  transition: all 0.3s ease;
}

.log-stats:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  padding-bottom: 6px;
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
}

.header-title {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 700;
  font-size: 12px;
  background: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-title .el-icon {
  font-size: 16px;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.header-actions {
  display: flex;
  gap: 4px;
}

/* 紧凑统计行 */
.stats-compact {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 6px;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.9) 0%, rgba(241, 245, 249, 0.9) 100%);
  border-radius: 6px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.compact-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1px;
  flex: 1;
}

.stat-number {
  font-size: 14px;
  font-weight: 700;
  line-height: 1;
}

.stat-text {
  font-size: 9px;
  font-weight: 500;
  opacity: 0.8;
  line-height: 1;
}

.compact-stat.total .stat-number {
  color: #6366f1;
}

.compact-stat.error .stat-number {
  color: #ef4444;
}

.compact-stat.warning .stat-number {
  color: #f59e0b;
}

.compact-stat.success .stat-number {
  color: #22c55e;
}

/* 最近日志 */
.recent-logs {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.recent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
  font-size: 11px;
  font-weight: 600;
  color: #64748b;
}

.recent-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.recent-item {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  padding: 4px 6px;
  margin-bottom: 3px;
  border-radius: 4px;
  font-size: 10px;
  line-height: 1.2;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background: rgba(248, 250, 252, 0.8);
}

.log-time {
  color: #94a3b8;
  font-weight: 500;
  white-space: nowrap;
  width: 40px;
  flex-shrink: 0;
}

.log-type {
  font-weight: 600;
  white-space: nowrap;
  width: 35px;
  flex-shrink: 0;
}

.log-message {
  flex: 1;
  min-width: 0;
  word-wrap: break-word;
  overflow-wrap: break-word;
  white-space: normal;
  line-height: 1.2;
}

.log-error .log-type { color: #ef4444; }
.log-warning .log-type { color: #f59e0b; }
.log-success .log-type { color: #22c55e; }
.log-info .log-type { color: #3b82f6; }
.log-debug .log-type { color: #8b5cf6; }
.log-input .log-type { color: #ec4899; }

.no-logs {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #94a3b8;
  font-size: 11px;
  gap: 8px;
}

.no-logs .el-icon {
  font-size: 24px;
  opacity: 0.5;
}

/* 深色主题适配 */
:global(.dark) .log-stats {
  background: var(--el-bg-color-page);
}
</style>
