<template>
  <el-card class="status-card">
    <template #header>
      <span class="card-header">
        <el-icon><Connection /></el-icon>
        连接状态
      </span>
    </template>

    <div class="status-content">
      <!-- 连接状态 -->
      <div class="status-item">
        <span class="status-label">连接状态:</span>
        <el-tag :type="connectionStatusType" size="small">
          {{ connectionStatus }}
        </el-tag>
      </div>

      <!-- 数据接收状态 -->
      <div class="status-item">
        <span class="status-label">视频流:</span>
        <el-tag :type="isReceivingVideo ? 'success' : 'info'" size="small">
          {{ isReceivingVideo ? '✅ 接收中' : '⏸️ 未接收' }}
        </el-tag>
      </div>

      <div class="status-item">
        <span class="status-label">音频流:</span>
        <el-tag :type="isReceivingAudio ? 'success' : 'info'" size="small">
          {{ isReceivingAudio ? '✅ 接收中' : '⏸️ 未接收' }}
        </el-tag>
      </div>

      <!-- 解码器状态 -->
      <div class="status-item">
        <span class="status-label">解码器:</span>
        <el-tag :type="useWebCodecs ? 'success' : 'info'" size="small">
          {{ currentDecoder }}
        </el-tag>
      </div>

      <!-- 统计信息 -->
      <div v-if="stats" class="stats-section">
        <el-divider content-position="left">统计信息</el-divider>
        
        <!-- H.264统计 -->
        <div class="status-item">
          <span class="status-label">H.264帧:</span>
          <span class="status-value">
            {{ stats.h264?.frameCount || 0 }} 帧
            ({{ formatBytes(stats.h264?.totalBytes || 0) }})
          </span>
        </div>

        <!-- 音频统计 -->
        <div class="status-item">
          <span class="status-label">音频帧:</span>
          <span class="status-value">
            {{ stats.audio?.frameCount || 0 }} 帧
            ({{ formatBytes(stats.audio?.totalBytes || 0) }})
          </span>
        </div>

        <!-- WebCodecs统计 -->
        <div v-if="useWebCodecs && stats.webCodecs" class="status-item">
          <span class="status-label">WebCodecs FPS:</span>
          <span class="status-value">{{ stats.webCodecs.fps || 0 }}</span>
        </div>

        <!-- 缓冲区统计 -->
        <div v-if="bufferStats" class="status-item">
          <span class="status-label">缓冲区:</span>
          <span class="status-value">
            {{ bufferStats.pendingFrames }}/{{ bufferStats.maxPendingFrames }}
          </span>
        </div>

        <!-- 掉帧统计 -->
        <div v-if="frameStats" class="status-item">
          <span class="status-label">H.264掉帧:</span>
          <span class="status-value">
            {{ frameStats.h264?.dropped || 0 }}/{{ frameStats.h264?.received || 0 }}
          </span>
        </div>

        <div v-if="frameStats" class="status-item">
          <span class="status-label">音频掉帧:</span>
          <span class="status-value">
            {{ frameStats.audio?.dropped || 0 }}/{{ frameStats.audio?.received || 0 }}
          </span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button size="small" @click="$emit('refresh')">
          刷新状态
        </el-button>
        <el-button size="small" @click="$emit('resetStats')">
          重置统计
        </el-button>
      </div>
    </div>
  </el-card>
</template>

<script setup>
import { computed } from 'vue'
import { Connection } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  connectionStatus: String,
  isReceivingVideo: Boolean,
  isReceivingAudio: Boolean,
  useWebCodecs: Boolean,
  currentDecoder: String,
  stats: Object,
  frameStats: Object,
  bufferStats: Object
})

// Emits
const emit = defineEmits(['refresh', 'resetStats'])

// 计算属性
const connectionStatusType = computed(() => {
  if (props.connectionStatus === '已连接') return 'success'
  if (props.connectionStatus === '连接中...') return 'warning'
  if (props.connectionStatus === '连接失败') return 'danger'
  return 'info'
})

// 工具函数
function formatBytes(bytes) {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>

<style scoped>
.status-card {
  height: fit-content;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 0;
}

.status-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
}

.status-value {
  font-size: 14px;
  color: #374151;
  font-weight: 600;
}

.stats-section {
  margin-top: 8px;
}

.action-buttons {
  display: flex;
  gap: 8px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #e5e7eb;
}

/* 深色主题适配 */
:global(.dark) .status-card {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(51, 65, 85, 0.8);
}

:global(.dark) .card-header {
  color: #f1f5f9;
}

:global(.dark) .status-label {
  color: #94a3b8;
}

:global(.dark) .status-value {
  color: #f1f5f9;
}

:global(.dark) .action-buttons {
  border-top-color: rgba(51, 65, 85, 0.6);
}
</style>
