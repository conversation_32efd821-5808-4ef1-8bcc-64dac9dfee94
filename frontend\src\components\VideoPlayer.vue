<template>
  <div class="video-section" ref="videoSection">
    <!-- 视频信息覆盖层 - 暂时隐藏以避免双重显示问题 -->
    <!-- <div v-if="videoReceived && showVideoInfo" class="video-info-overlay">
      <div class="video-info">
        <div class="info-item">
          <el-icon><Timer /></el-icon>
          <span>播放时间: {{ formatPlayTime(playTime) }}</span>
        </div>
        <div class="info-item">
          <el-icon><Monitor /></el-icon>
          <span>{{ videoResolution }}</span>
        </div>
        <div class="info-item">
          <el-icon><DataAnalysis /></el-icon>
          <span>{{ currentFPS }} FPS</span>
        </div>
      </div>
    </div> -->

    <!-- 全屏控制提示 -->
    <div v-if="videoReceived" class="fullscreen-hint">
      <el-icon><FullScreen /></el-icon>
      <span v-if="!isFullscreen">双击全屏</span>
      <span v-else>按ESC退出全屏</span>
    </div>

    <!-- 全屏按钮 -->
    <div v-if="videoReceived" class="fullscreen-button" @click="toggleFullscreen">
      <el-button :icon="isFullscreen ? Aim : FullScreen" circle size="small" />
    </div>

    <div
      class="video-container"
      ref="videoContainer"
      tabindex="0"
      @mousemove="handleMouseMove"
      @mousedown="handleMouseDown"
      @mouseup="handleMouseUp"
      @click="handleMouseClick"
      @dblclick="handleMouseDoubleClick"
      @wheel="handleMouseWheel"
      @contextmenu="handleContextMenu"
      @keydown="handleKeyDown"
      @keyup="handleKeyUp">

      <canvas id="videoCanvas" class="video-stream"></canvas>

      <div v-if="!videoReceived" class="video-placeholder">
        <div class="placeholder-content">
          <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
          <h3>等待 H264 视频流</h3>
          <p>请先连接服务器，然后使用 FFmpeg 推送视频流到端口 5000</p>
          <el-button type="primary" size="small" @click="showStreamingHelp">
            <el-icon><QuestionFilled /></el-icon>
            查看推流帮助
          </el-button>
          <br><br>
          <el-button size="small" @click="$emit('testCanvas')">测试Canvas</el-button>
          <el-button size="small" @click="$emit('testAudio')">测试音频</el-button>
          <el-button size="small" @click="$emit('forceShowVideo')">强制显示视频</el-button>
          <el-button size="small" @click="$emit('debugStatus')">调试状态</el-button>
          <el-button size="small" @click="$emit('reinitAudio')" type="warning">重置音频</el-button>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, QuestionFilled, Timer, Monitor, DataAnalysis, FullScreen, Aim } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  videoReceived: {
    type: Boolean,
    default: false
  },
  mouseControlEnabled: {
    type: Boolean,
    default: true
  },
  dataChannelOpen: {
    type: Boolean,
    default: false
  },
  videoStats: {
    type: Object,
    default: () => ({
      resolution: '等待视频流...',
      fps: 0
    })
  }
})

// Emits
const emit = defineEmits([
  'inputEvent',
  'testCanvas',
  'testAudio',
  'forceShowVideo',
  'debugStatus',
  'reinitAudio'
])



// Template refs
const videoContainer = ref(null)
const videoSection = ref(null)

// 全屏和视频信息状态
const isFullscreen = ref(false)
const showVideoInfo = ref(true)
const playTime = ref(0)

// 使用props中的视频统计信息
const videoResolution = computed(() => props.videoStats.resolution)
const currentFPS = computed(() => props.videoStats.fps)

// 定时器
let playTimeTimer = null
let infoHideTimer = null

// 鼠标移动节流
let lastMouseMoveTime = 0
const MOUSE_MOVE_THROTTLE = 16 // 16ms = ~60fps

// 显示推流帮助
function showStreamingHelp() {
  ElMessage.info({
    message: '使用命令: ffmpeg -re -i input.mp4 -c:v libx264 -crf 18 -f h264 udp://localhost:5000',
    duration: 5000,
    showClose: true
  })
}

// 发送输入事件（添加时间戳）
function sendInputEvent(eventData) {
  if (!props.mouseControlEnabled || !props.dataChannelOpen) {
    return
  }

  // 添加高精度时间戳
  const enhancedEventData = {
    ...eventData,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

// 格式化播放时间
function formatPlayTime(seconds) {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  } else {
    return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
}

// 全屏功能
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    // 进入全屏
    if (videoSection.value.requestFullscreen) {
      videoSection.value.requestFullscreen()
    } else if (videoSection.value.webkitRequestFullscreen) {
      videoSection.value.webkitRequestFullscreen()
    } else if (videoSection.value.msRequestFullscreen) {
      videoSection.value.msRequestFullscreen()
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
  }
}

// 监听全屏状态变化
function handleFullscreenChange() {
  isFullscreen.value = !!document.fullscreenElement

  if (isFullscreen.value) {
    ElMessage.success('已进入全屏模式，按ESC退出')
  } else {
    ElMessage.info('已退出全屏模式')
  }
}

// 显示/隐藏视频信息
function toggleVideoInfo() {
  showVideoInfo.value = !showVideoInfo.value

  if (showVideoInfo.value) {
    // 5秒后自动隐藏
    if (infoHideTimer) {
      clearTimeout(infoHideTimer)
    }
    infoHideTimer = setTimeout(() => {
      showVideoInfo.value = false
    }, 5000)
  }
}

// 视频统计信息现在通过props传递，不需要单独的更新方法

// 鼠标事件处理
function handleMouseMove(event) {
  if (!props.mouseControlEnabled) return

  // 节流处理，避免发送过多事件
  const now = performance.now()
  if (now - lastMouseMoveTime < MOUSE_MOVE_THROTTLE) {
    return
  }
  lastMouseMoveTime = now

  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)

  const enhancedEventData = {
    type: 'mousemove',
    x: x,
    y: y,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleMouseDown(event) {
  if (!props.mouseControlEnabled) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  const enhancedEventData = {
    type: 'mousedown',
    x: x,
    y: y,
    button: event.button,
    buttons: event.buttons,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleMouseUp(event) {
  if (!props.mouseControlEnabled) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  const enhancedEventData = {
    type: 'mouseup',
    x: x,
    y: y,
    button: event.button,
    buttons: event.buttons,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleMouseClick(event) {
  // 确保视频容器获得焦点以接收键盘事件
  if (videoContainer.value) {
    videoContainer.value.focus()
  }

  if (!props.mouseControlEnabled) return
  event.preventDefault()

  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)

  const enhancedEventData = {
    type: 'mouseclick',
    x: x,
    y: y,
    button: event.button,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleMouseDoubleClick(event) {
  event.preventDefault()

  // 双击切换全屏
  toggleFullscreen()

  if (!props.mouseControlEnabled) return

  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)

  const enhancedEventData = {
    type: 'mousedblclick',
    x: x,
    y: y,
    button: event.button,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleMouseWheel(event) {
  if (!props.mouseControlEnabled) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  const enhancedEventData = {
    type: 'mousewheel',
    x: x,
    y: y,
    deltaX: event.deltaX,
    deltaY: event.deltaY,
    deltaZ: event.deltaZ,
    deltaMode: event.deltaMode,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleContextMenu(event) {
  if (!props.mouseControlEnabled) return
  event.preventDefault()
  
  const rect = event.target.getBoundingClientRect()
  const x = Math.round(event.clientX - rect.left)
  const y = Math.round(event.clientY - rect.top)
  
  const enhancedEventData = {
    type: 'contextmenu',
    x: x,
    y: y,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

// 键盘事件处理
function handleKeyDown(event) {
  // 处理特殊键
  if (event.key === 'Escape') {
    if (isFullscreen.value) {
      toggleFullscreen()
      event.preventDefault()
      return
    }
  } else if (event.key === 'F11') {
    toggleFullscreen()
    event.preventDefault()
    return
  } else if (event.key === 'i' || event.key === 'I') {
    if (event.ctrlKey) {
      toggleVideoInfo()
      event.preventDefault()
      return
    }
  }

  if (!props.mouseControlEnabled) return
  event.preventDefault()

  const enhancedEventData = {
    type: 'keydown',
    key: event.key,
    code: event.code,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    repeat: event.repeat,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

function handleKeyUp(event) {
  if (!props.mouseControlEnabled) return
  event.preventDefault()
  
  const enhancedEventData = {
    type: 'keyup',
    key: event.key,
    code: event.code,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    timestamp: Date.now(),
    highResTimestamp: performance.now(),
    clientTime: new Date().toISOString()
  }

  emit('inputEvent', enhancedEventData)
}

// 恢复最初的Canvas设置，让视频自然渲染
function resizeCanvas() {
  const canvas = document.getElementById('videoCanvas')
  const container = videoContainer.value

  if (canvas && container) {
    // 只设置CSS尺寸，不强制修改像素尺寸
    canvas.style.width = '100%'
    canvas.style.height = '100%'

    console.log(`Canvas display set to 100% (natural resolution)`)
  }
}



// 监听视频接收状态变化
watch(() => props.videoReceived, (newValue) => {
  if (newValue) {
    nextTick(() => {
      resizeCanvas()
    })
  }
})

// 组件挂载时初始化
onMounted(() => {
  if (videoContainer.value) {
    videoContainer.value.focus()
  }

  // 初始化Canvas尺寸
  nextTick(() => {
    resizeCanvas()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', resizeCanvas)

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', handleFullscreenChange)
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.addEventListener('msfullscreenchange', handleFullscreenChange)

  // 启动播放时间计时器
  playTimeTimer = setInterval(() => {
    if (props.videoReceived) {
      playTime.value += 1
    }
  }, 1000)

  // 初始显示视频信息，5秒后隐藏
  if (infoHideTimer) {
    clearTimeout(infoHideTimer)
  }
  infoHideTimer = setTimeout(() => {
    showVideoInfo.value = false
  }, 5000)
})

// 组件卸载时清理
onUnmounted(() => {
  // 清理事件监听器
  document.removeEventListener('fullscreenchange', handleFullscreenChange)
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange)
  document.removeEventListener('msfullscreenchange', handleFullscreenChange)
  window.removeEventListener('resize', resizeCanvas)

  // 清理定时器
  if (playTimeTimer) {
    clearInterval(playTimeTimer)
  }
  if (infoHideTimer) {
    clearTimeout(infoHideTimer)
  }
})

// 暴露方法给父组件
defineExpose({
  videoContainer,
  toggleFullscreen,
  toggleVideoInfo
})
</script>

<style scoped>
.video-section {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  display: block;
  position: relative;
  overflow: hidden;
  border-radius: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  width: 100%;
  height: 100%;
}

.video-container {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  outline: none;
  cursor: default;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border-radius: 0;
  overflow: hidden;
  transition: all 0.3s ease;
}

.video-container:focus {
  box-shadow: inset 0 0 0 3px rgba(99, 102, 241, 0.6);
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
}

.video-container:hover {
  background: linear-gradient(135deg, #334155 0%, #475569 100%);
}

.video-stream {
  width: 100%;
  height: 100%;
  display: block !important;
  position: relative;
  z-index: 10;
  border-radius: 0;
}

/* 全屏时的视频样式 */
:global(.video-section:fullscreen) .video-stream {
  width: 100vw;
  height: 100vh;
  object-fit: contain;
  border-radius: 0;
}

:global(.video-section:fullscreen) {
  border-radius: 0;
}

.video-stream:hover {
  transform: scale(1.01);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  z-index: 5;
  border-radius: 20px;
}

.placeholder-content {
  text-align: center;
  color: #94a3b8;
  max-width: 480px;
  padding: 3rem;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.placeholder-content:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-4px);
}

.placeholder-icon {
  font-size: 5rem;
  background: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 4px 8px rgba(99, 102, 241, 0.3));
}

.placeholder-content h3 {
  color: #f1f5f9;
  margin-bottom: 1.5rem;
  font-size: 1.75rem;
  font-weight: 700;
  background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.025em;
}

.placeholder-content p {
  margin-bottom: 2rem;
  line-height: 1.7;
  font-size: 1.1rem;
  font-weight: 500;
  color: #cbd5e1;
}



/* 视频信息覆盖层 */
.video-info-overlay {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 25;
  pointer-events: none;
}

.video-info {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  color: white;
  padding: 16px 20px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.video-info:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 8px;
  padding: 4px 0;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item .el-icon {
  font-size: 14px;
  opacity: 0.8;
}

.info-item .el-icon {
  font-size: 14px;
  color: #3b82f6;
}

/* 全屏提示 */
.fullscreen-hint {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 11px;
  z-index: 20;
  opacity: 0.7;
  transition: opacity 0.3s;
}

.fullscreen-hint:hover {
  opacity: 1;
}

/* 全屏按钮 */
.fullscreen-button {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 25;
  opacity: 0;
  transition: all 0.3s ease;
}

.video-container:hover .fullscreen-button {
  opacity: 1;
}

.fullscreen-button :deep(.el-button) {
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.fullscreen-button :deep(.el-button:hover) {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.1);
}

/* 全屏模式样式调整 */
.video-section:fullscreen {
  background: black;
}

.video-section:fullscreen .video-container {
  width: 100vw;
  height: 100vh;
}

.video-section:fullscreen .video-info-overlay {
  top: 32px;
  left: 32px;
}



.video-section:fullscreen .fullscreen-hint {
  bottom: 32px;
  right: 32px;
}
</style>
