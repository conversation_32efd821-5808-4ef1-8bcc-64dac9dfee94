/**
 * 高级H264视频播放器
 * 支持硬件解码、防掉帧、花屏处理
 */

import { ref, nextTick } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const frameCount = ref(0)
const droppedFrames = ref(0)
const lastFrameTime = ref(0)

// 播放器组件
let canvas = null
let canvasContext = null
let decoder = null
let frameBuffer = []
let isProcessing = false

// 性能监控
let performanceStats = {
  totalFrames: 0,
  droppedFrames: 0,
  lastFpsCheck: Date.now(),
  framesSinceLastCheck: 0,
  currentFps: 0,
  avgDecodeTime: 0,
  decodeTimeSum: 0
}

// 日志回调
let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化高级视频播放器
 */
export async function initAdvancedVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element is required')
    }

    canvas = canvasElement
    canvasContext = canvas.getContext('2d')
    
    // 设置默认尺寸
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080
    
    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs VideoDecoder not supported')
    }

    // 创建硬件加速解码器
    decoder = new VideoDecoder({
      output: handleDecodedFrame,
      error: handleDecoderError
    })

    // 配置H264解码器（优化配置）
    const config = {
      codec: 'avc1.42e01f', // H.264 Baseline Profile Level 3.1
      codedWidth: options.width || 1920,
      codedHeight: options.height || 1080,
      hardwareAcceleration: 'prefer-hardware', // 优先硬件解码
      optimizeForLatency: true // 优化延迟
    }

    await decoder.configure(config)
    
    isInitialized.value = true
    log('SUCCESS', '🎬 高级H264播放器初始化成功 (硬件加速)')
    
    return true
  } catch (error) {
    log('ERROR', `❌ 高级播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 处理解码后的视频帧
 */
function handleDecodedFrame(frame) {
  try {
    const decodeStartTime = performance.now()
    
    // 检查帧是否有效
    if (!frame || frame.codedWidth === 0 || frame.codedHeight === 0) {
      frame?.close()
      droppedFrames.value++
      performanceStats.droppedFrames++
      return
    }

    // 更新canvas尺寸（如果需要）
    if (canvas.width !== frame.codedWidth || canvas.height !== frame.codedHeight) {
      canvas.width = frame.codedWidth
      canvas.height = frame.codedHeight
      log('INFO', `📐 视频尺寸更新: ${frame.codedWidth}x${frame.codedHeight}`)
    }

    // 渲染帧到canvas
    canvasContext.drawImage(frame, 0, 0, canvas.width, canvas.height)
    
    // 更新统计信息
    frameCount.value++
    performanceStats.totalFrames++
    performanceStats.framesSinceLastCheck++
    
    const decodeTime = performance.now() - decodeStartTime
    performanceStats.decodeTimeSum += decodeTime
    performanceStats.avgDecodeTime = performanceStats.decodeTimeSum / performanceStats.totalFrames
    
    // 计算FPS
    const now = Date.now()
    if (now - performanceStats.lastFpsCheck >= 1000) {
      performanceStats.currentFps = performanceStats.framesSinceLastCheck
      performanceStats.framesSinceLastCheck = 0
      performanceStats.lastFpsCheck = now
      
      // 每秒记录一次性能
      if (performanceStats.totalFrames % 30 === 0) {
        log('INFO', `📊 FPS: ${performanceStats.currentFps}, 解码: ${performanceStats.avgDecodeTime.toFixed(2)}ms, 掉帧: ${performanceStats.droppedFrames}`)
      }
    }
    
    lastFrameTime.value = now
    isPlaying.value = true
    
    // 释放帧资源
    frame.close()
    
  } catch (error) {
    log('ERROR', `❌ 帧渲染失败: ${error.message}`)
    frame?.close()
    droppedFrames.value++
    performanceStats.droppedFrames++
  }
}

/**
 * 处理解码器错误
 */
function handleDecoderError(error) {
  log('ERROR', `❌ 解码器错误: ${error.message}`)
  
  // 尝试重置解码器
  if (decoder && decoder.state !== 'closed') {
    try {
      decoder.reset()
      log('INFO', '🔄 解码器已重置')
    } catch (resetError) {
      log('ERROR', `❌ 解码器重置失败: ${resetError.message}`)
    }
  }
}

/**
 * 处理H264数据（优化版本）
 */
export async function handleH264Data(data) {
  if (!decoder || decoder.state !== 'configured') {
    log('WARNING', '⚠️ 解码器未就绪，跳过帧')
    return false
  }

  try {
    // 防止处理队列过长（防掉帧）
    if (decoder.decodeQueueSize > 5) {
      log('WARNING', '⚠️ 解码队列过长，跳过帧防止延迟')
      droppedFrames.value++
      performanceStats.droppedFrames++
      return false
    }

    // 检查H264数据有效性
    if (!data || data.length < 4) {
      log('WARNING', '⚠️ 无效H264数据')
      return false
    }

    // 检查NAL单元头
    const hasStartCode = data[0] === 0x00 && data[1] === 0x00 && 
                        data[2] === 0x00 && data[3] === 0x01
    
    if (!hasStartCode) {
      log('WARNING', '⚠️ H264数据缺少起始码')
      return false
    }

    // 创建编码帧
    const chunk = new EncodedVideoChunk({
      type: isKeyFrame(data) ? 'key' : 'delta',
      timestamp: performance.now() * 1000, // 微秒
      data: data
    })

    // 解码帧
    decoder.decode(chunk)
    return true
    
  } catch (error) {
    log('ERROR', `❌ H264解码失败: ${error.message}`)
    return false
  }
}

/**
 * 检查是否为关键帧
 */
function isKeyFrame(data) {
  // 查找NAL单元类型
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i+1] === 0x00 && 
        data[i+2] === 0x00 && data[i+3] === 0x01) {
      const nalType = data[i+4] & 0x1F
      // IDR帧 (5) 或 SPS (7) 或 PPS (8)
      if (nalType === 5 || nalType === 7 || nalType === 8) {
        return true
      }
    }
  }
  return false
}

/**
 * 获取播放器统计信息
 */
export function getPlayerStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    frameCount: frameCount.value,
    droppedFrames: droppedFrames.value,
    fps: performanceStats.currentFps,
    avgDecodeTime: performanceStats.avgDecodeTime,
    queueSize: decoder?.decodeQueueSize || 0
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }
    
    decoder = null
    canvas = null
    canvasContext = null
    frameBuffer = []
    
    isInitialized.value = false
    isPlaying.value = false
    frameCount.value = 0
    droppedFrames.value = 0
    
    log('INFO', '🧹 高级播放器资源已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.reset()
    }
    
    frameBuffer = []
    performanceStats = {
      totalFrames: 0,
      droppedFrames: 0,
      lastFpsCheck: Date.now(),
      framesSinceLastCheck: 0,
      currentFps: 0,
      avgDecodeTime: 0,
      decodeTimeSum: 0
    }
    
    frameCount.value = 0
    droppedFrames.value = 0
    isPlaying.value = false
    
    log('INFO', '🔄 播放器已重置')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
