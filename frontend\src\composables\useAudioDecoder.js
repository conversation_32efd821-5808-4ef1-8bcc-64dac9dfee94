import { ref } from 'vue'

export function useAudioDecoder() {
  // 音频解码相关
  let audioDecoder = null
  let audioContext = null
  let isAudioDecoderConfigured = false
  let audioFrameCount = 0
  let audioDecodeErrors = 0
  let audioFramesPlayed = 0

  // 音频时间戳管理
  let audioStartTime = null
  let audioFrameDuration = 23.22 // AAC帧持续时间（毫秒）

  // 音频播放队列管理
  let nextPlayTime = 0
  let isPlayingAudio = false

  // 日志回调
  let addLog = null

  // 设置日志回调
  function setLogCallback(callback) {
    addLog = callback
  }

  // 初始化AAC音频解码器
  function initAudioDecoder() {
    if (!window.AudioDecoder) {
      if (addLog) {
        addLog('ERROR', '浏览器不支持WebCodecs AudioDecoder API')
      }
      return false
    }

    try {
      // 如果已有解码器，先关闭它
      if (audioDecoder) {
        try {
          if (audioDecoder.state !== 'closed') {
            audioDecoder.close()
          }
        } catch (e) {
          console.warn('关闭旧音频解码器时出错:', e)
        }
      }

      // 初始化音频上下文
      if (!audioContext) {
        audioContext = new (window.AudioContext || window.webkitAudioContext)()
        if (addLog) {
          addLog('INFO', `音频上下文已创建 (采样率: ${audioContext.sampleRate}Hz)`)
        }

        // 尝试启动音频上下文（需要用户交互）
        if (audioContext.state === 'suspended') {
          if (addLog) {
            addLog('INFO', '音频上下文需要用户交互来启动，请点击页面任意位置')
          }
          // 添加点击事件监听器来启动音频
          const startAudio = () => {
            audioContext.resume().then(() => {
              if (addLog) {
                addLog('SUCCESS', '音频上下文已启动')
              }
              document.removeEventListener('click', startAudio)
            }).catch(err => {
              if (addLog) {
                addLog('ERROR', `启动音频上下文失败: ${err.message}`)
              }
            })
          }
          document.addEventListener('click', startAudio, { once: true })
        }
      }

      // 创建音频解码器
      audioDecoder = new AudioDecoder({
        output: (audioData) => {
          try {
            // 播放音频帧
            playAudioFrame(audioData)
          } catch (error) {
            if (addLog) {
              addLog('ERROR', `音频播放失败: ${error.message}`)
            }
          }
        },
        error: (error) => {
          if (addLog) {
            addLog('ERROR', `音频解码器错误: ${error.message}`)
          }
          isAudioDecoderConfigured = false

          // 统计解码错误
          audioDecodeErrors++
          if (audioDecodeErrors > 10) {
            if (addLog) {
              addLog('WARNING', `解码错误过多(${audioDecodeErrors})，可能需要重新初始化`)
            }
            if (audioDecodeErrors > 50) {
              if (addLog) {
                addLog('INFO', '尝试重新初始化音频解码器...')
              }
              setTimeout(() => {
                audioDecodeErrors = 0
                initAudioDecoder()
              }, 2000)
            }
          }
        }
      })

      // 尝试多种AAC配置，优化ADTS兼容性
      const configs = [
        // 优先使用最常见的配置
        { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 2, description: undefined }, // AAC-LC 44.1kHz 立体声
        { codec: 'mp4a.40.2', sampleRate: 48000, numberOfChannels: 2, description: undefined }, // AAC-LC 48kHz 立体声
        { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 1, description: undefined }, // AAC-LC 44.1kHz 单声道
        { codec: 'mp4a.40.2', sampleRate: 22050, numberOfChannels: 2, description: undefined }, // AAC-LC 22.05kHz
        { codec: 'mp4a.40.2', sampleRate: 32000, numberOfChannels: 2, description: undefined }, // AAC-LC 32kHz
      ]

      let configSuccess = false
      for (const config of configs) {
        try {
          audioDecoder.configure(config)
          configSuccess = true
          if (addLog) {
            addLog('SUCCESS', `AAC解码器配置成功: ${config.codec} ${config.sampleRate}Hz ${config.numberOfChannels}声道`)
          }
          
          // 保存配置信息供后续使用
          window.audioDecoderConfig = config
          break
        } catch (e) {
          if (addLog) {
            addLog('DEBUG', `配置 ${config.sampleRate}Hz/${config.numberOfChannels}ch 失败: ${e.message}`)
          }
        }
      }

      if (!configSuccess) {
        throw new Error('所有AAC解码器配置都失败，请检查浏览器WebCodecs支持')
      }

      isAudioDecoderConfigured = true
      if (addLog) {
        addLog('SUCCESS', 'AAC音频解码器初始化成功')
      }
      return true
    } catch (error) {
      if (addLog) {
        addLog('ERROR', `AAC音频解码器初始化失败: ${error.message}`)
      }
      console.error('Audio Decoder Init Error:', error)
      isAudioDecoderConfigured = false
      return false
    }
  }

  // 处理接收到的音频数据
  function handleAudioData(data) {
    // 首次接收音频数据的调试信息
    if (audioFrameCount === 0) {
      if (addLog) {
        addLog('SUCCESS', `首次接收音频数据: ${data.byteLength} 字节`)
      }
      audioStartTime = performance.now()
    }

    // 检查音频解码器状态，确保其可用
    if (!audioDecoder || audioDecoder.state === 'closed' || !isAudioDecoderConfigured) {
      if (!initAudioDecoder()) {
        return
      }
      // 初始化后等待一帧再处理数据
      setTimeout(() => handleAudioData(data), 50)
      return
    }

    try {
      // 检查解码器状态
      if (!audioDecoder || audioDecoder.state !== 'configured') {
        if (audioFrameCount < 10) { // 只在前10帧报告状态问题
          if (addLog) {
            addLog('WARNING', `音频解码器状态异常: ${audioDecoder?.state || 'null'}，跳过此帧`)
          }
        }
        return
      }

      const uint8Data = new Uint8Array(data)
      if (uint8Data.length < 7) { // ADTS最小长度为7字节
        if (addLog) {
          addLog('WARNING', `音频数据太短: ${uint8Data.length} 字节，跳过`)
        }
        return
      }

      // 检查ADTS格式
      const isADTS = uint8Data[0] === 0xFF && (uint8Data[1] & 0xF0) === 0xF0
      if (!isADTS) {
        if (audioFrameCount < 3) { // 减少警告频率
          if (addLog) {
            addLog('DEBUG', '检测到非ADTS格式音频数据，尝试直接解码', 'audio')
          }
        }
      }

      // 解析ADTS头部获取准确的帧信息
      let sampleRate = 44100
      let channels = 2
      let frameLength = uint8Data.length

      if (isADTS && uint8Data.length >= 7) {
        const freqIndex = (uint8Data[2] >> 2) & 0x0F
        const channelConfig = ((uint8Data[2] & 0x01) << 2) | ((uint8Data[3] >> 6) & 0x03)
        frameLength = ((uint8Data[3] & 0x03) << 11) | (uint8Data[4] << 3) | ((uint8Data[5] & 0xE0) >> 5)
        
        // 频率映射表
        const sampleRates = [96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350]
        if (freqIndex < sampleRates.length) {
          sampleRate = sampleRates[freqIndex]
        }
        channels = channelConfig || 2

        // 计算准确的帧持续时间
        audioFrameDuration = (1024 * 1000) / sampleRate // AAC每帧1024个样本

        if (audioFrameCount < 3) {
          if (addLog) {
            addLog('DEBUG', `ADTS解析: ${sampleRate}Hz, ${channels}声道, 帧长度=${frameLength}, 持续时间=${audioFrameDuration.toFixed(2)}ms`)
          }
        }
      }

      // 验证帧长度
      if (isADTS && frameLength !== uint8Data.length) {
        if (audioFrameCount < 10) {
          if (addLog) {
            addLog('WARNING', `ADTS帧长度不匹配: 头部=${frameLength}, 实际=${uint8Data.length}`)
          }
        }
      }

      // 创建音频块，使用基于实际时间的时间戳
      const currentTime = performance.now()
      const elapsedTime = audioStartTime ? (currentTime - audioStartTime) : 0
      const expectedTimestamp = audioFrameCount * audioFrameDuration
      
      // 使用更准确的时间戳（微秒）
      const timestamp = Math.round(expectedTimestamp * 1000)
      
      const chunk = new EncodedAudioChunk({
        type: 'key',
        timestamp: timestamp,
        data: uint8Data
      })

      // 控制解码器队列大小
      if (audioDecoder.decodeQueueSize > 5) {
        if (audioFrameCount % 50 === 0) { // 减少警告频率
          if (addLog) {
            addLog('WARNING', `解码队列过长: ${audioDecoder.decodeQueueSize}，跳过此帧`)
          }
        }
        return
      }

      // 解码音频
      audioDecoder.decode(chunk)
      audioFrameCount++

      // 定期输出统计信息（减少频率）
      if (audioFrameCount % 200 === 0) {
        const formatStr = isADTS ? 'ADTS-AAC' : 'Raw-AAC'
        const avgSize = Math.round(uint8Data.length)
        if (addLog) {
          addLog('INFO', `音频解码正常: ${audioFrameCount} 帧, ${avgSize}字节/帧, ${formatStr}, ${sampleRate}Hz`)
        }
      }

    } catch (error) {
      audioDecodeErrors++

      // 根据错误频率调整日志级别
      if (audioDecodeErrors <= 5) {
        if (addLog) {
          addLog('ERROR', `音频解码器错误: ${error.message}`, 'audio')
        }
      } else if (audioDecodeErrors % 10 === 0) {
        if (addLog) {
          addLog('WARNING', `累计音频解码错误: ${audioDecodeErrors} 次`, 'audio')
        }
      }

      console.error('Audio Decode Error:', error, {
        frameCount: audioFrameCount,
        dataLength: data.byteLength,
        decoderState: audioDecoder?.state,
        errorCount: audioDecodeErrors
      })

      // 智能错误恢复策略
      if (audioDecodeErrors > 20 && audioDecodeErrors % 20 === 0) {
        if (addLog) {
          addLog('INFO', '尝试重新初始化音频解码器...', 'audio')
        }

        // 重新初始化解码器
        setTimeout(() => {
          try {
            if (audioDecoder) {
              audioDecoder.close()
            }
            audioDecoder = null
            audioDecodeErrors = Math.floor(audioDecodeErrors / 2) // 减半错误计数
            initAudioDecoder()
          } catch (reinitError) {
            console.error('音频解码器重新初始化失败:', reinitError)
          }
        }, 500)
      }
    }
  }

  // 播放音频帧
  function playAudioFrame(audioData) {
    if (!audioContext) {
      audioData.close()
      if (addLog) {
        addLog('ERROR', '音频上下文未初始化')
      }
      return
    }

    try {
      // 确保音频上下文已启动
      if (audioContext.state === 'suspended') {
        audioContext.resume().then(() => {
          if (audioFramesPlayed === 0) {
            if (addLog) {
              addLog('SUCCESS', '音频上下文已启动')
            }
          }
        }).catch(err => {
          if (addLog) {
            addLog('ERROR', `恢复音频上下文失败: ${err.message}`)
          }
        })
        // 暂停状态下不播放
        audioData.close()
        return
      }

      // 验证音频数据
      if (!audioData || audioData.numberOfFrames === 0) {
        if (addLog) {
          addLog('WARNING', '音频数据无效')
        }
        audioData?.close()
        return
      }

      // 创建音频缓冲区
      const audioBuffer = audioContext.createBuffer(
        audioData.numberOfChannels,
        audioData.numberOfFrames,
        audioData.sampleRate
      )

      // 复制音频数据到缓冲区
      for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
        const channelData = new Float32Array(audioData.numberOfFrames)
        audioData.copyTo(channelData, { planeIndex: channel })
        audioBuffer.copyToChannel(channelData, channel)
      }

      // 创建音频源
      const source = audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(audioContext.destination)

      // 计算播放时间，确保连续播放
      const currentTime = audioContext.currentTime
      const bufferDuration = audioData.numberOfFrames / audioData.sampleRate

      if (nextPlayTime <= currentTime) {
        // 如果预定时间已过，立即播放
        nextPlayTime = currentTime
      }

      // 播放音频
      source.start(nextPlayTime)
      
      // 更新下次播放时间
      nextPlayTime += bufferDuration

      // 防止时间戳漂移
      if (nextPlayTime - currentTime > 0.5) {
        // 如果预定时间超前太多，重置为当前时间
        nextPlayTime = currentTime + bufferDuration
      }

      // 更新播放统计
      audioFramesPlayed++

      // 关闭音频数据
      audioData.close()

      // 记录播放状态
      if (audioFramesPlayed === 1) {
        if (addLog) {
          addLog('SUCCESS', `音频播放开始: ${audioData.sampleRate}Hz, ${audioData.numberOfChannels}声道`)
        }
        isPlayingAudio = true
      } else if (audioFramesPlayed % 200 === 0) {
        // 减少日志频率
        if (addLog) {
          addLog('INFO', `音频播放正常: ${audioFramesPlayed} 帧, ${audioData.sampleRate}Hz, 延迟=${(nextPlayTime - currentTime).toFixed(3)}s`)
        }
      }

      // 错误恢复：如果播放队列过长，清理
      if (nextPlayTime - currentTime > 1.0) {
        if (addLog) {
          addLog('WARNING', '音频播放队列过长，重置时间戳')
        }
        nextPlayTime = currentTime
      }

    } catch (error) {
      if (addLog) {
        addLog('ERROR', `音频播放失败: ${error.message}`)
      }
      console.error('Audio playback error:', error, {
        sampleRate: audioData?.sampleRate,
        channels: audioData?.numberOfChannels,
        frames: audioData?.numberOfFrames,
        contextState: audioContext?.state
      })
      audioData?.close()
    }
  }

  // 测试音频
  function testAudio() {
    if (!audioContext) {
      if (addLog) {
        addLog('ERROR', '音频上下文未初始化，尝试重新初始化...')
      }
      initAudioDecoder()
      if (!audioContext) {
        if (addLog) {
          addLog('ERROR', '音频上下文初始化失败')
        }
        return
      }
    }

    try {
      // 确保音频上下文启动
      if (audioContext.state === 'suspended') {
        audioContext.resume().then(() => {
          if (addLog) {
            addLog('INFO', '音频上下文已恢复')
          }
          playTestTone()
        })
      } else {
        playTestTone()
      }
    } catch (error) {
      if (addLog) {
        addLog('ERROR', `音频测试失败: ${error.message}`)
      }
    }
  }

  // 播放测试音调
  function playTestTone() {
    try {
      const duration = 1 // 1秒
      const frequency = 440 // 440Hz
      const sampleRate = audioContext.sampleRate
      const frames = sampleRate * duration

      const audioBuffer = audioContext.createBuffer(1, frames, sampleRate)
      const channelData = audioBuffer.getChannelData(0)

      // 生成正弦波
      for (let i = 0; i < frames; i++) {
        channelData[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.3
      }

      const source = audioContext.createBufferSource()
      source.buffer = audioBuffer
      source.connect(audioContext.destination)
      source.start()

      if (addLog) {
        addLog('SUCCESS', '测试音调已播放 (440Hz, 1秒)')
      }
    } catch (error) {
      if (addLog) {
        addLog('ERROR', `播放测试音调失败: ${error.message}`)
      }
    }
  }

  // 清理资源
  function cleanup() {
    // 清理音频解码器
    if (audioDecoder) {
      try {
        if (audioDecoder.state !== 'closed') {
          audioDecoder.close()
        }
      } catch (error) {
        console.error('关闭音频解码器失败:', error)
      }
      audioDecoder = null
      isAudioDecoderConfigured = false
    }

    // 重置计数器和状态
    audioFrameCount = 0
    audioFramesPlayed = 0
    audioDecodeErrors = 0
    audioStartTime = null
    nextPlayTime = 0
    isPlayingAudio = false
  }

  return {
    // 方法
    initAudioDecoder,
    handleAudioData,
    testAudio,
    cleanup,
    setLogCallback
  }
}
