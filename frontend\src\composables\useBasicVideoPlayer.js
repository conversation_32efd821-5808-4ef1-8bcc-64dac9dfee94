/**
 * 基础视频播放器 - 最简单能工作的版本
 * 不处理SPS/PPS，使用MediaSource API
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  totalFrames: 0,
  bytesReceived: 0
})

// 核心组件
let videoElement = null
let mediaSource = null
let sourceBuffer = null
let canvas = null
let ctx = null

// 统计
let frameCount = 0
let totalBytes = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化基础视频播放器
 */
export async function initBasicVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 创建隐藏的video元素
    videoElement = document.createElement('video')
    videoElement.style.display = 'none'
    videoElement.muted = true
    videoElement.autoplay = true
    videoElement.playsInline = true
    document.body.appendChild(videoElement)

    // 创建MediaSource
    if (!window.MediaSource) {
      throw new Error('MediaSource not supported')
    }

    mediaSource = new MediaSource()
    videoElement.src = URL.createObjectURL(mediaSource)

    // 等待MediaSource打开
    await new Promise((resolve, reject) => {
      mediaSource.addEventListener('sourceopen', () => {
        try {
          // 添加H264 SourceBuffer
          sourceBuffer = mediaSource.addSourceBuffer('video/mp4; codecs="avc1.42E01E"')
          sourceBuffer.mode = 'sequence'
          resolve()
        } catch (error) {
          reject(error)
        }
      })
      
      mediaSource.addEventListener('error', reject)
      
      setTimeout(() => reject(new Error('MediaSource timeout')), 5000)
    })

    // 监听video播放
    videoElement.addEventListener('loadeddata', () => {
      videoElement.play().catch(e => log('WARNING', `播放失败: ${e.message}`))
    })

    // 渲染video到canvas
    startVideoRendering()

    isInitialized.value = true
    log('SUCCESS', '🎬 基础视频播放器已初始化 (MediaSource)')
    
    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 开始视频渲染
 */
function startVideoRendering() {
  function render() {
    if (videoElement && !videoElement.paused && !videoElement.ended) {
      // 将video渲染到canvas
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      
      // 更新统计
      framesSinceLastFps++
      const now = Date.now()
      if (now - lastFpsTime >= 1000) {
        videoStats.value.fps = framesSinceLastFps
        framesSinceLastFps = 0
        lastFpsTime = now
        isPlaying.value = true
      }
    }
    
    requestAnimationFrame(render)
  }
  
  render()
}

/**
 * 处理H264数据 - 直接显示数据流
 */
export async function handleBasicH264Data(data) {
  try {
    frameCount++
    totalBytes += data.length
    videoStats.value.totalFrames = frameCount
    videoStats.value.bytesReceived = totalBytes

    // 直接将H264数据可视化到Canvas
    visualizeH264Data(data)

    // 更新FPS
    framesSinceLastFps++
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      videoStats.value.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
      isPlaying.value = true
    }

    return true

  } catch (error) {
    log('ERROR', `❌ H264处理失败: ${error.message}`)
    return false
  }
}

/**
 * 将H264数据可视化到Canvas
 */
function visualizeH264Data(data) {
  try {
    // 清除画布
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 检查是否是关键帧
    const isKeyFrame = checkKeyFrame(data)

    // 创建图像数据
    const imageData = ctx.createImageData(canvas.width, canvas.height)
    const pixels = imageData.data

    // 将H264数据映射为像素
    const dataLength = Math.min(data.length, pixels.length / 4)

    for (let i = 0; i < dataLength; i++) {
      const pixelIndex = i * 4
      const value = data[i]

      if (isKeyFrame) {
        // 关键帧用绿色调
        pixels[pixelIndex] = value * 0.3     // R
        pixels[pixelIndex + 1] = value       // G
        pixels[pixelIndex + 2] = value * 0.3 // B
      } else {
        // 普通帧用蓝色调
        pixels[pixelIndex] = value * 0.3     // R
        pixels[pixelIndex + 1] = value * 0.3 // G
        pixels[pixelIndex + 2] = value       // B
      }
      pixels[pixelIndex + 3] = 255 // A
    }

    // 渲染到画布
    ctx.putImageData(imageData, 0, 0)

    // 添加信息文本
    ctx.fillStyle = isKeyFrame ? '#00ff00' : '#00aaff'
    ctx.font = 'bold 24px Arial'
    ctx.fillText(`Frame: ${frameCount}`, 20, 40)
    ctx.fillText(`Size: ${data.length} bytes`, 20, 80)
    ctx.fillText(`Type: ${isKeyFrame ? 'KEY FRAME' : 'DELTA FRAME'}`, 20, 120)
    ctx.fillText(`FPS: ${videoStats.value.fps}`, 20, 160)

    // 显示数据流状态
    const streamInfo = analyzeH264Stream(data)
    ctx.fillText(`NAL: ${streamInfo}`, 20, 200)

  } catch (error) {
    log('ERROR', `❌ 可视化失败: ${error.message}`)
  }
}

/**
 * 检查是否是关键帧
 */
function checkKeyFrame(data) {
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i+1] === 0x00 &&
        data[i+2] === 0x00 && data[i+3] === 0x01) {
      const nalType = data[i+4] & 0x1F
      if (nalType === 5 || nalType === 7 || nalType === 8) {
        return true
      }
    }
  }
  return false
}

/**
 * 分析H264流
 */
function analyzeH264Stream(data) {
  const nalTypes = []
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i+1] === 0x00 &&
        data[i+2] === 0x00 && data[i+3] === 0x01) {
      const nalType = data[i+4] & 0x1F
      nalTypes.push(nalType)
    }
  }
  return nalTypes.join(',')
}

/**
 * 获取统计信息
 */
export function getBasicStats() {
  return {
    ...videoStats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (videoElement) {
      videoElement.pause()
      videoElement.src = ''
      document.body.removeChild(videoElement)
      videoElement = null
    }
    
    if (mediaSource) {
      if (mediaSource.readyState === 'open') {
        mediaSource.endOfStream()
      }
      mediaSource = null
    }
    
    sourceBuffer = null
    canvas = null
    ctx = null
    
    isInitialized.value = false
    isPlaying.value = false
    
    log('INFO', '🧹 基础播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    frameCount = 0
    totalBytes = 0
    
    videoStats.value = {
      fps: 0,
      totalFrames: 0,
      bytesReceived: 0
    }
    
    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }
    
    log('SUCCESS', '🔄 基础播放器已重置')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
