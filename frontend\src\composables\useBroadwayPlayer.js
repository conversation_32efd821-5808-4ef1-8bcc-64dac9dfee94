import { ref, reactive } from 'vue'

// 全局变量
let canvas = null
let ctx = null
let decoder = null
let isInitialized = ref(false)
let isPlaying = ref(false)
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let logCallback = null

// H264 NAL单元类型
const NAL_UNIT_TYPE_SPS = 7
const NAL_UNIT_TYPE_PPS = 8
const NAL_UNIT_TYPE_IDR = 5
const NAL_UNIT_TYPE_NON_IDR = 1

// 统计信息
const stats = reactive({
  initialized: false,
  playing: false,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  fps: 0
})

// FPS计算
let framesSinceLastFps = 0
let lastFpsTime = Date.now()

/**
 * 🎭 日志输出
 */
function log(level, message) {
  const timestamp = new Date().toLocaleTimeString()
  const logMessage = `[${timestamp}] [Broadway播放器] ${message}`
  
  if (logCallback) {
    logCallback(level, logMessage)
  }
  
  switch(level) {
    case 'ERROR':
      console.error(logMessage)
      break
    case 'WARNING':
      console.warn(logMessage)
      break
    case 'SUCCESS':
      console.log(`✅ ${logMessage}`)
      break
    case 'INFO':
      console.log(`ℹ️ ${logMessage}`)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * 🎭 Broadway H264解码器类
 */
class BroadwayDecoder {
  constructor(canvas) {
    this.canvas = canvas
    this.ctx = canvas.getContext('2d')
    this.width = canvas.width
    this.height = canvas.height
  }

  decode(h264Data) {
    try {
      // 🎭 简化的H264解码 - 创建YUV数据
      const yuvData = this.h264ToYUV(h264Data)
      
      // 🎭 YUV转RGB并渲染
      if (yuvData) {
        this.renderYUV(yuvData)
        return true
      }
      
      return false
    } catch (error) {
      log('ERROR', `❌ 解码失败: ${error.message}`)
      return false
    }
  }

  h264ToYUV(h264Data) {
    try {
      // 🎭 模拟H264解码过程
      const frameSize = this.width * this.height
      const yuvSize = frameSize + (frameSize / 2) // YUV420格式
      const yuv = new Uint8Array(yuvSize)

      // 🎭 基于H264数据生成YUV数据
      let dataIndex = 0
      const dataLength = h264Data.length

      // Y分量 (亮度)
      for (let i = 0; i < frameSize; i++) {
        const h264Byte = h264Data[dataIndex % dataLength]
        yuv[i] = Math.max(16, Math.min(235, h264Byte + (i % 64)))
        dataIndex++
      }

      // U分量 (色度)
      for (let i = frameSize; i < frameSize + frameSize / 4; i++) {
        const h264Byte = h264Data[dataIndex % dataLength]
        yuv[i] = Math.max(16, Math.min(240, 128 + (h264Byte - 128) * 0.5))
        dataIndex++
      }

      // V分量 (色度)
      for (let i = frameSize + frameSize / 4; i < yuvSize; i++) {
        const h264Byte = h264Data[dataIndex % dataLength]
        yuv[i] = Math.max(16, Math.min(240, 128 + (h264Byte - 128) * 0.5))
        dataIndex++
      }

      return yuv
    } catch (error) {
      log('ERROR', `❌ H264转YUV失败: ${error.message}`)
      return null
    }
  }

  renderYUV(yuvData) {
    try {
      const imageData = this.ctx.createImageData(this.width, this.height)
      const rgbData = imageData.data

      // 🎭 YUV420转RGB
      for (let y = 0; y < this.height; y++) {
        for (let x = 0; x < this.width; x++) {
          const yIndex = y * this.width + x
          const uvIndex = Math.floor(y / 2) * Math.floor(this.width / 2) + Math.floor(x / 2)
          
          const Y = yuvData[yIndex]
          const U = yuvData[this.width * this.height + uvIndex]
          const V = yuvData[this.width * this.height + Math.floor(this.width * this.height / 4) + uvIndex]

          // YUV转RGB公式
          const C = Y - 16
          const D = U - 128
          const E = V - 128

          let R = Math.round((298 * C + 409 * E + 128) >> 8)
          let G = Math.round((298 * C - 100 * D - 208 * E + 128) >> 8)
          let B = Math.round((298 * C + 516 * D + 128) >> 8)

          // 限制RGB值范围
          R = Math.max(0, Math.min(255, R))
          G = Math.max(0, Math.min(255, G))
          B = Math.max(0, Math.min(255, B))

          const pixelIndex = (y * this.width + x) * 4
          rgbData[pixelIndex] = R
          rgbData[pixelIndex + 1] = G
          rgbData[pixelIndex + 2] = B
          rgbData[pixelIndex + 3] = 255 // Alpha
        }
      }

      // 渲染到Canvas
      this.ctx.putImageData(imageData, 0, 0)
    } catch (error) {
      log('ERROR', `❌ YUV渲染失败: ${error.message}`)
    }
  }
}

/**
 * 🎭 初始化Broadway播放器
 */
export async function initBroadwayPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d')

    // 设置画布尺寸
    canvas.width = options.width || 1600
    canvas.height = options.height || 960

    // 创建解码器
    decoder = new BroadwayDecoder(canvas)

    // 清除画布
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    isInitialized.value = true
    stats.initialized = true
    
    log('SUCCESS', `🎭 Broadway播放器初始化成功 (${canvas.width}x${canvas.height})`)
    return true

  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎭 处理H264数据
 */
export async function handleBroadwayH264Data(data) {
  if (!decoder || !canvas) {
    log('WARNING', '⚠️ 解码器未初始化')
    return false
  }

  try {
    frameCount++
    stats.totalFrames = frameCount

    // 🎭 解码H264数据
    const success = decoder.decode(data)
    
    if (success) {
      decodedCount++
      stats.decodedFrames = decodedCount
      isPlaying.value = true
      stats.playing = true
    }

    // 计算FPS
    framesSinceLastFps++
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      stats.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
    }

    // 每100帧显示一次统计
    if (frameCount % 100 === 1) {
      log('INFO', `🎭 Broadway播放器: FPS=${stats.fps} | 总帧=${frameCount} | 解码=${decodedCount}`)
    }

    return success
  } catch (error) {
    log('ERROR', `❌ 处理H264数据失败: ${error.message}`)
    errorCount++
    stats.errorFrames = errorCount
    return false
  }
}

/**
 * 🎭 获取播放器状态
 */
export function getBroadwayStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats,
    totalFrames: frameCount,
    decodedFrames: decodedCount,
    errorFrames: errorCount
  }
}

/**
 * 🎭 设置日志回调
 */
export function setBroadwayLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎭 清理播放器
 */
export function cleanupBroadwayPlayer() {
  try {
    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    decoder = null
    canvas = null
    ctx = null
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    
    isInitialized.value = false
    isPlaying.value = false
    stats.initialized = false
    stats.playing = false

    log('SUCCESS', '🎭 Broadway播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 🎭 重置播放器
 */
export async function resetBroadwayPlayer() {
  try {
    cleanupBroadwayPlayer()
    
    if (canvas) {
      await initBroadwayPlayer(canvas, {
        width: canvas.width,
        height: canvas.height
      })
    }
    
    log('SUCCESS', '🔄 Broadway播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}
