/**
 * Canvas2D播放器 - 绕过WebCodecs问题
 * 使用MediaSource API + Video元素 + Canvas渲染
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  totalFrames: 0,
  bytesReceived: 0
})

// 核心组件
let canvas = null
let ctx = null
let videoElement = null
let mediaSource = null
let sourceBuffer = null

// 数据缓冲
let h264Buffer = []
let isSourceBufferReady = false

// 统计
let frameCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0
let totalBytes = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化Canvas2D播放器
 */
export async function initCanvas2DPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 创建隐藏的video元素
    videoElement = document.createElement('video')
    videoElement.style.display = 'none'
    videoElement.muted = true
    videoElement.autoplay = true
    videoElement.playsInline = true
    document.body.appendChild(videoElement)

    // 检查MediaSource支持
    if (!window.MediaSource) {
      throw new Error('MediaSource not supported')
    }

    // 创建MediaSource
    mediaSource = new MediaSource()
    videoElement.src = URL.createObjectURL(mediaSource)

    // 等待MediaSource打开
    await new Promise((resolve, reject) => {
      mediaSource.addEventListener('sourceopen', () => {
        try {
          // 创建SourceBuffer
          sourceBuffer = mediaSource.addSourceBuffer('video/mp4; codecs="avc1.42E01E"')
          
          sourceBuffer.addEventListener('updateend', () => {
            isSourceBufferReady = true
            processBufferedData()
          })
          
          sourceBuffer.addEventListener('error', (e) => {
            log('ERROR', `❌ SourceBuffer错误: ${e}`)
          })
          
          isSourceBufferReady = true
          resolve()
        } catch (error) {
          reject(error)
        }
      })
      
      mediaSource.addEventListener('error', reject)
      
      // 超时处理
      setTimeout(() => reject(new Error('MediaSource打开超时')), 5000)
    })

    // 监听video播放
    videoElement.addEventListener('loadeddata', () => {
      log('SUCCESS', '📺 视频数据已加载')
    })

    videoElement.addEventListener('playing', () => {
      isPlaying.value = true
      startRendering()
      log('SUCCESS', '▶️ 视频开始播放')
    })

    videoElement.addEventListener('error', (e) => {
      log('ERROR', `❌ 视频播放错误: ${e.message}`)
    })

    isInitialized.value = true
    log('SUCCESS', '🎨 Canvas2D播放器已初始化')
    
    return true
  } catch (error) {
    log('ERROR', `❌ Canvas2D播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 开始渲染循环
 */
function startRendering() {
  function render() {
    if (!videoElement || !canvas || !ctx) return
    
    try {
      if (videoElement.readyState >= 2) { // HAVE_CURRENT_DATA
        // 调整画布尺寸
        if (videoElement.videoWidth > 0 && videoElement.videoHeight > 0) {
          if (canvas.width !== videoElement.videoWidth || canvas.height !== videoElement.videoHeight) {
            canvas.width = videoElement.videoWidth
            canvas.height = videoElement.videoHeight
            log('INFO', `📐 分辨率: ${canvas.width}x${canvas.height}`)
          }
        }

        // 清除并渲染
        ctx.fillStyle = '#000000'
        ctx.fillRect(0, 0, canvas.width, canvas.height)
        ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)

        // 更新统计
        frameCount++
        videoStats.value.totalFrames++
        framesSinceLastFps++
        
        // 计算FPS
        const now = Date.now()
        if (now - lastFpsTime >= 1000) {
          videoStats.value.fps = framesSinceLastFps
          framesSinceLastFps = 0
          lastFpsTime = now
        }
      }
    } catch (error) {
      log('ERROR', `❌ 渲染失败: ${error.message}`)
    }
    
    if (isPlaying.value) {
      requestAnimationFrame(render)
    }
  }
  
  render()
}

/**
 * 处理H264数据
 */
export async function handleCanvas2DH264Data(data) {
  if (!sourceBuffer) {
    return false
  }

  try {
    totalBytes += data.length
    videoStats.value.bytesReceived = totalBytes

    // 将数据添加到缓冲区
    h264Buffer.push(data)
    
    // 处理缓冲的数据
    processBufferedData()

    return true
    
  } catch (error) {
    log('ERROR', `❌ H264数据处理失败: ${error.message}`)
    return false
  }
}

/**
 * 处理缓冲的数据
 */
function processBufferedData() {
  if (!isSourceBufferReady || !sourceBuffer || sourceBuffer.updating || h264Buffer.length === 0) {
    return
  }

  try {
    // 合并缓冲的数据
    const totalLength = h264Buffer.reduce((sum, chunk) => sum + chunk.length, 0)
    const combinedData = new Uint8Array(totalLength)
    let offset = 0
    
    for (const chunk of h264Buffer) {
      combinedData.set(chunk, offset)
      offset += chunk.length
    }
    
    h264Buffer = []

    // 尝试转换为MP4格式（简化版）
    const mp4Data = convertH264ToMP4(combinedData)
    
    if (mp4Data && mp4Data.length > 0) {
      isSourceBufferReady = false
      sourceBuffer.appendBuffer(mp4Data)
      
      // 开始播放
      if (videoElement.paused) {
        videoElement.play().catch(e => {
          log('WARNING', `⚠️ 自动播放失败: ${e.message}`)
        })
      }
    }
    
  } catch (error) {
    log('ERROR', `❌ 数据处理失败: ${error.message}`)
    isSourceBufferReady = true
  }
}

/**
 * 简化的H264到MP4转换
 */
function convertH264ToMP4(h264Data) {
  try {
    // 这是一个简化的实现
    // 实际应用中需要完整的MP4封装
    
    // 创建基本的MP4头部
    const mp4Header = createMP4Header()
    
    // 合并头部和数据
    const mp4Data = new Uint8Array(mp4Header.length + h264Data.length)
    mp4Data.set(mp4Header, 0)
    mp4Data.set(h264Data, mp4Header.length)
    
    return mp4Data
    
  } catch (error) {
    log('ERROR', `❌ MP4转换失败: ${error.message}`)
    return null
  }
}

/**
 * 创建基本的MP4头部
 */
function createMP4Header() {
  // 这是一个非常简化的MP4头部
  // 实际应用需要完整的ftyp, moov, mdat等box
  return new Uint8Array([
    // ftyp box (简化)
    0x00, 0x00, 0x00, 0x20, // box size
    0x66, 0x74, 0x79, 0x70, // 'ftyp'
    0x69, 0x73, 0x6F, 0x6D, // major brand 'isom'
    0x00, 0x00, 0x02, 0x00, // minor version
    0x69, 0x73, 0x6F, 0x6D, // compatible brand 'isom'
    0x69, 0x73, 0x6F, 0x32, // compatible brand 'iso2'
    0x61, 0x76, 0x63, 0x31, // compatible brand 'avc1'
    0x6D, 0x70, 0x34, 0x31  // compatible brand 'mp41'
  ])
}

/**
 * 获取统计信息
 */
export function getCanvas2DStats() {
  return {
    ...videoStats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    isPlaying.value = false
    
    if (videoElement) {
      videoElement.pause()
      videoElement.src = ''
      if (videoElement.parentNode) {
        videoElement.parentNode.removeChild(videoElement)
      }
      videoElement = null
    }
    
    if (mediaSource && mediaSource.readyState === 'open') {
      mediaSource.endOfStream()
    }
    mediaSource = null
    sourceBuffer = null
    
    canvas = null
    ctx = null
    h264Buffer = []
    
    isInitialized.value = false
    
    log('INFO', '🧹 Canvas2D播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    h264Buffer = []
    frameCount = 0
    totalBytes = 0
    
    videoStats.value = {
      fps: 0,
      totalFrames: 0,
      bytesReceived: 0
    }
    
    if (videoElement) {
      videoElement.currentTime = 0
    }
    
    log('SUCCESS', '🔄 Canvas2D播放器已重置')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
