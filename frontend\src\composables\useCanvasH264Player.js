/**
 * Canvas + H264解码播放器
 * 使用WebCodecs API解码H264，然后在Canvas上播放
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0
})

// 核心组件
let canvas = null
let ctx = null
let decoder = null
let isDecoderReady = false

// 统计
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化Canvas H264播放器
 */
export async function initCanvasH264Player(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布大小
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs VideoDecoder not supported')
    }

    // 创建H264解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 将解码后的帧绘制到Canvas
          ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
          frame.close() // 释放帧资源
          
          // 更新统计
          decodedCount++
          videoStats.value.decodedFrames = decodedCount
          
          // 计算FPS
          framesSinceLastFps++
          const now = Date.now()
          if (now - lastFpsTime >= 1000) {
            videoStats.value.fps = framesSinceLastFps
            framesSinceLastFps = 0
            lastFpsTime = now
          }
          
          isPlaying.value = true
          
        } catch (error) {
          log('ERROR', `❌ 帧渲染失败: ${error.message}`)
          errorCount++
          videoStats.value.errorFrames = errorCount
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码器错误: ${error.message}`)
        errorCount++
        videoStats.value.errorFrames = errorCount
        
        // 尝试重新配置解码器
        setTimeout(() => {
          tryReconfigureDecoder()
        }, 1000)
      }
    })

    // 配置H264解码器
    await configureDecoder()

    // 测试解码器是否真的可用
    await testDecoder()

    isInitialized.value = true
    log('SUCCESS', '🎬 Canvas H264播放器已初始化')

    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 配置解码器
 */
async function configureDecoder() {
  try {
    // 先尝试最兼容的配置
    const config = {
      codec: 'avc1.42e01e', // H.264 Baseline Profile Level 3.0
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-software', // 先用软件解码确保兼容性
      optimizeForLatency: true
    }

    await decoder.configure(config)
    isDecoderReady = true
    log('SUCCESS', '🔧 H264解码器配置成功 (软件解码)')

  } catch (error) {
    log('WARNING', `⚠️ 软件解码配置失败: ${error.message}`)

    // 尝试其他配置
    await tryAlternativeConfigs()
  }
}

/**
 * 尝试其他解码器配置
 */
async function tryAlternativeConfigs() {
  const configs = [
    { codec: 'avc1.42e01f', hw: 'prefer-software' }, // Baseline Level 3.1 软件
    { codec: 'avc1.42e01e', hw: 'prefer-hardware' }, // Baseline Level 3.0 硬件
    { codec: 'avc1.42e01f', hw: 'prefer-hardware' }, // Baseline Level 3.1 硬件
    { codec: 'avc1.42e020', hw: 'prefer-software' }, // Baseline Level 3.2 软件
    { codec: 'avc1.64001e', hw: 'prefer-software' }, // High Level 3.0 软件
  ]

  for (const { codec, hw } of configs) {
    try {
      const config = {
        codec: codec,
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: hw,
        optimizeForLatency: true
      }

      await decoder.configure(config)
      isDecoderReady = true
      log('SUCCESS', `🔧 解码器配置成功 (${codec}, ${hw})`)
      return

    } catch (error) {
      log('WARNING', `⚠️ 配置 ${codec} (${hw}) 失败，尝试下一个`)
    }
  }

  // 最后尝试最基础的配置
  try {
    const basicConfig = {
      codec: 'avc1.42e01e',
      codedWidth: 640,
      codedHeight: 480,
      hardwareAcceleration: 'no-preference',
      optimizeForLatency: false
    }

    await decoder.configure(basicConfig)
    isDecoderReady = true
    log('SUCCESS', '🔧 解码器配置成功 (基础配置 640x480)')

  } catch (error) {
    log('ERROR', '❌ 所有解码器配置都失败了')
    isDecoderReady = false
  }
}

/**
 * 处理H264数据
 */
export async function handleCanvasH264Data(data) {
  // 检查解码器状态
  if (!decoder || decoder.state === 'closed') {
    log('WARNING', '⚠️ 解码器已关闭，尝试重新创建')
    await tryReconfigureDecoder()
    return false
  }

  if (!isDecoderReady) {
    log('WARNING', '⚠️ 解码器未就绪')
    return false
  }

  try {
    frameCount++
    videoStats.value.totalFrames = frameCount

    // 检测帧类型
    const isKeyFrame = detectKeyFrame(data)

    // 创建编码视频块
    const chunk = new EncodedVideoChunk({
      type: isKeyFrame ? 'key' : 'delta',
      timestamp: performance.now() * 1000, // 微秒
      data: data
    })

    // 再次检查解码器状态
    if (decoder.state === 'closed') {
      log('WARNING', '⚠️ 解码器在解码前被关闭')
      return false
    }

    // 解码
    decoder.decode(chunk)

    if (isKeyFrame) {
      log('INFO', '🔑 关键帧已解码')
    }

    return true

  } catch (error) {
    log('ERROR', `❌ H264解码失败: ${error.message}`)
    errorCount++
    videoStats.value.errorFrames = errorCount

    // 如果是解码器关闭错误，尝试重新配置
    if (error.message.includes('closed codec')) {
      log('INFO', '🔄 检测到解码器关闭，尝试重新配置')
      await tryReconfigureDecoder()
    }

    return false
  }
}

/**
 * 检测关键帧
 */
function detectKeyFrame(data) {
  // 查找NAL单元
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i + 1] === 0x00 &&
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      const nalType = data[i + 4] & 0x1F
      
      // IDR帧(5)、SPS(7)、PPS(8)都算关键帧
      if (nalType === 5 || nalType === 7 || nalType === 8) {
        return true
      }
    }
  }
  return false
}

/**
 * 测试解码器
 */
async function testDecoder() {
  try {
    log('INFO', '🧪 测试解码器状态...')

    if (!decoder) {
      log('ERROR', '❌ 解码器不存在')
      return false
    }

    if (decoder.state === 'closed') {
      log('ERROR', '❌ 解码器已关闭')
      return false
    }

    if (decoder.state === 'unconfigured') {
      log('ERROR', '❌ 解码器未配置')
      return false
    }

    log('SUCCESS', `✅ 解码器状态: ${decoder.state}`)
    log('SUCCESS', `✅ 解码器就绪: ${isDecoderReady}`)

    return true

  } catch (error) {
    log('ERROR', `❌ 解码器测试失败: ${error.message}`)
    return false
  }
}

/**
 * 尝试重新配置解码器
 */
async function tryReconfigureDecoder() {
  try {
    isDecoderReady = false

    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }

    // 重新创建解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
          frame.close()
          decodedCount++
          videoStats.value.decodedFrames = decodedCount

          // 计算FPS
          framesSinceLastFps++
          const now = Date.now()
          if (now - lastFpsTime >= 1000) {
            videoStats.value.fps = framesSinceLastFps
            framesSinceLastFps = 0
            lastFpsTime = now
          }

          isPlaying.value = true
        } catch (error) {
          log('ERROR', `❌ 帧渲染失败: ${error.message}`)
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码器错误: ${error.message}`)
        isDecoderReady = false

        // 延迟重试
        setTimeout(() => {
          tryReconfigureDecoder()
        }, 2000)
      }
    })

    await configureDecoder()
    log('SUCCESS', '🔄 解码器重新配置成功')

  } catch (error) {
    log('ERROR', `❌ 解码器重新配置失败: ${error.message}`)
    isDecoderReady = false
  }
}

/**
 * 获取统计信息
 */
export function getCanvasStats() {
  return {
    ...videoStats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value,
    decoderReady: isDecoderReady
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }
    
    decoder = null
    canvas = null
    ctx = null
    isDecoderReady = false
    
    isInitialized.value = false
    isPlaying.value = false
    
    log('INFO', '🧹 Canvas播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    
    videoStats.value = {
      fps: 0,
      totalFrames: 0,
      decodedFrames: 0,
      errorFrames: 0
    }
    
    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }
    
    log('SUCCESS', '🔄 Canvas播放器已重置')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
