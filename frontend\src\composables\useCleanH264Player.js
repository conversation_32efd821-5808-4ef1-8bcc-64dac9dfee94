/**
 * 🎮 精简GPU加速H264播放器
 * 专注于：GPU硬件加速 + 简单稳定 + 高画质
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const stats = ref({
  fps: 0,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  initialized: false,
  playing: false
})

// 核心组件
let canvas = null
let ctx = null
let decoder = null
let isDecoderConfigured = false

// 统计
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

// 🎵 音频相关
let audioDecoder = null
let audioContext = null
let audioFrameCount = 0
let audioErrorCount = 0

// 🕐 时间戳修复 - 解决MP4播放速度问题
let realStartTime = null
let baseTimestamp = 0

// 🎮 简单的播放器状态
const playerState = {
  initialized: false,
  playing: false
}

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}



/**
 * 🎮 初始化GPU加速H264播放器
 */
export async function initCleanH264Player(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    // 🎮 GPU优化的Canvas上下文
    ctx = canvas.getContext('2d', {
      alpha: false,           // 不需要透明度，提升性能
      desynchronized: true,   // 异步渲染，减少阻塞
      willReadFrequently: false // 不频繁读取，优化GPU
    })

    // 设置画布尺寸
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs not supported')
    }

    // 🎮 创建GPU加速解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 🎮 优化GPU渲染 - 平滑处理
          ctx.imageSmoothingEnabled = true
          ctx.imageSmoothingQuality = 'high'

          // 清除画布避免残影
          ctx.clearRect(0, 0, canvas.width, canvas.height)

          // 高质量渲染
          ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
          frame.close()

          decodedCount++
          stats.value.decodedFrames = decodedCount

          // 计算FPS
          framesSinceLastFps++
          const now = Date.now()
          if (now - lastFpsTime >= 1000) {
            stats.value.fps = framesSinceLastFps
            framesSinceLastFps = 0
            lastFpsTime = now
          }

          isPlaying.value = true
          stats.value.playing = true

        } catch (error) {
          log('ERROR', `❌ 渲染失败: ${error.message}`)
          frame.close()
          errorCount++
          stats.value.errorFrames = errorCount
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码错误: ${error.message}`)
        errorCount++
        stats.value.errorFrames = errorCount

        // 🔥 关键修复：WebCodecs解码器错误后会自动关闭，需要立即重新创建
        if (decoder && decoder.state === 'closed') {
          log('WARNING', '🔄 解码器已关闭，立即重新初始化...')

          // 异步重新初始化，避免阻塞
          setTimeout(async () => {
            try {
              if (canvas) {
                await initCleanH264Player(canvas, { width: canvas.width, height: canvas.height })
                log('SUCCESS', '✅ 解码器重新初始化成功')
              }
            } catch (reinitError) {
              log('ERROR', `❌ 解码器重新初始化失败: ${reinitError.message}`)
            }
          }, 100) // 100ms后重试
        }
      }
    })

    // 🎮 尝试多种解码器配置 - 兼容不同编码参数
    const configs = [
      // 屏幕捕获常用配置
      { codec: 'avc1.42e01e', hardwareAcceleration: 'prefer-hardware', optimizeForLatency: true },  // Baseline 3.0
      { codec: 'avc1.42e01f', hardwareAcceleration: 'prefer-hardware', optimizeForLatency: true },  // Baseline 3.1
      { codec: 'avc1.42e020', hardwareAcceleration: 'prefer-hardware', optimizeForLatency: true },  // Baseline 3.2
      // MP4文件常用配置
      { codec: 'avc1.64001f', hardwareAcceleration: 'prefer-hardware', optimizeForLatency: false }, // High 3.1
      { codec: 'avc1.640020', hardwareAcceleration: 'prefer-hardware', optimizeForLatency: false }, // High 3.2
      // 兜底配置
      { codec: 'avc1.42e01e', hardwareAcceleration: 'prefer-software', optimizeForLatency: true },  // 软件解码
      { codec: 'avc1.42e01f', hardwareAcceleration: 'no-preference', optimizeForLatency: true }     // 无偏好
    ]

    let configSuccess = false
    for (const configBase of configs) {
      try {
        const config = {
          ...configBase,
          codedWidth: canvas.width,
          codedHeight: canvas.height
        }

        await decoder.configure(config)
        configSuccess = true
        isDecoderConfigured = true
        log('SUCCESS', `🎮 解码器配置成功: ${config.codec} (${config.hardwareAcceleration})`)
        break
      } catch (e) {
        log('DEBUG', `配置 ${configBase.codec} 失败: ${e.message}`)
      }
    }

    if (!configSuccess) {
      throw new Error('所有解码器配置都失败，请检查H264编码参数')
    }

    // 🎵 初始化音频解码器
    await initAudioDecoder()

    isInitialized.value = true
    stats.value.initialized = true
    log('SUCCESS', '🎮 GPU加速H264播放器已初始化')

    return true
  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🔍 检查H264数据格式
 */
function analyzeH264Data(data) {
  const uint8Data = new Uint8Array(data)
  const analysis = {
    size: uint8Data.length,
    hasStartCode: false,
    nalUnits: [],
    format: 'unknown'
  }

  // 检查Annex-B起始码
  if (uint8Data.length >= 4) {
    if (uint8Data[0] === 0x00 && uint8Data[1] === 0x00 && uint8Data[2] === 0x00 && uint8Data[3] === 0x01) {
      analysis.hasStartCode = true
      analysis.format = 'Annex-B'
    } else if (uint8Data[0] === 0x00 && uint8Data[1] === 0x00 && uint8Data[2] === 0x01) {
      analysis.hasStartCode = true
      analysis.format = 'Annex-B (3-byte)'
    }
  }

  // 分析NAL单元
  let offset = 0
  while (offset < uint8Data.length - 4) {
    // 查找起始码
    if (uint8Data[offset] === 0x00 && uint8Data[offset + 1] === 0x00) {
      if (uint8Data[offset + 2] === 0x00 && uint8Data[offset + 3] === 0x01) {
        // 4字节起始码
        if (offset + 4 < uint8Data.length) {
          const nalType = uint8Data[offset + 4] & 0x1F
          analysis.nalUnits.push({ type: nalType, offset: offset + 4 })
        }
        offset += 4
      } else if (uint8Data[offset + 2] === 0x01) {
        // 3字节起始码
        if (offset + 3 < uint8Data.length) {
          const nalType = uint8Data[offset + 3] & 0x1F
          analysis.nalUnits.push({ type: nalType, offset: offset + 3 })
        }
        offset += 3
      } else {
        offset++
      }
    } else {
      offset++
    }
  }

  return analysis
}

/**
 * 🎮 处理H264数据 - 精简版本，增强诊断
 */
export async function handleCleanH264Data(data) {

  if (!decoder || decoder.state === 'closed') {
    // 提供更详细的诊断信息
    const decoderState = decoder ? decoder.state : 'null'
    log('WARNING', `⚠️ 解码器不可用 (状态: ${decoderState})`)

    // 尝试重新初始化
    if (canvas && frameCount % 30 === 0) { // 每30帧尝试一次，避免频繁重试
      log('INFO', '🔄 尝试重新初始化解码器...')
      try {
        await initCleanH264Player(canvas, { width: canvas.width, height: canvas.height })
      } catch (error) {
        log('ERROR', `❌ 重新初始化失败: ${error.message}`)
      }
    }
    return false
  }

  if (!isDecoderConfigured) {
    log('WARNING', `⚠️ 解码器未配置 (状态: ${decoder.state})`)
    return false
  }

  // 📊 定期报告状态
  if (frameCount % 100 === 1) {
    log('INFO', `📊 FPS=${stats.value.fps} | 总帧=${frameCount} | 解码=${decodedCount} | 错误=${errorCount}`)
  }

  // 🔍 分析H264数据格式 (仅前几帧)
  if (frameCount < 5) {
    const analysis = analyzeH264Data(data)
    log('DEBUG', `🔍 H264数据分析: ${analysis.size}字节, ${analysis.format}, NAL单元: ${analysis.nalUnits.length}`)

    if (!analysis.hasStartCode) {
      log('WARNING', '⚠️ 未检测到Annex-B起始码，可能导致解码失败')
    }

    if (analysis.nalUnits.length > 0) {
      const nalTypes = analysis.nalUnits.map(nal => nal.type).join(',')
      log('DEBUG', `🔍 NAL单元类型: [${nalTypes}]`)
    }
  }

  try {
    frameCount++
    stats.value.totalFrames = frameCount

    // 🎯 实时流优化 - 使用简单递增时间戳避免延迟累积
    let timestamp
    if (!realStartTime) {
      realStartTime = performance.now() * 1000 // 转换为微秒
      baseTimestamp = 0 // 从0开始
    }

    // 对于实时流，使用简单的递增时间戳（假设30fps）
    timestamp = frameCount * 33333 // 33.333ms per frame (30fps)

    const chunk = new EncodedVideoChunk({
      type: isKeyFrame(data) ? 'key' : 'delta',
      timestamp: Math.round(timestamp),
      data: data
    })

    decoder.decode(chunk)
    
    // 每100帧显示一次统计
    if (frameCount % 100 === 1) {
      log('INFO', `📊 FPS=${stats.value.fps} | 总帧=${frameCount} | 解码=${decodedCount} | 错误=${errorCount}`)
    }

    return true
  } catch (error) {
    log('ERROR', `❌ 解码失败: ${error.message}`)
    errorCount++
    stats.value.errorFrames = errorCount
    return false
  }
}

/**
 * 🎵 初始化音频解码器 - 使用成熟的AAC处理逻辑
 */
async function initAudioDecoder() {
  try {
    // 检查WebCodecs音频支持
    if (!window.AudioDecoder) {
      throw new Error('AudioDecoder not supported')
    }

    // 如果已有解码器，先关闭它
    if (audioDecoder) {
      try {
        if (audioDecoder.state !== 'closed') {
          audioDecoder.close()
        }
      } catch (e) {
        log('WARNING', `关闭旧音频解码器: ${e.message}`)
      }
    }

    // 初始化音频上下文
    if (!audioContext) {
      audioContext = new (window.AudioContext || window.webkitAudioContext)()
      log('INFO', `🎵 音频上下文已创建 (采样率: ${audioContext.sampleRate}Hz)`)

      // 尝试启动音频上下文（需要用户交互）
      if (audioContext.state === 'suspended') {
        log('INFO', '🎵 音频上下文需要用户交互来启动，请点击页面任意位置')
        // 添加点击事件监听器来启动音频
        const startAudio = () => {
          audioContext.resume().then(() => {
            log('SUCCESS', '🎵 音频上下文已启动')
            document.removeEventListener('click', startAudio)
          }).catch(err => {
            log('ERROR', `❌ 启动音频上下文失败: ${err.message}`)
          })
        }
        document.addEventListener('click', startAudio, { once: true })
      }
    }

    // 创建音频解码器
    audioDecoder = new AudioDecoder({
      output: (audioData) => {
        try {
          // 播放音频数据
          playAudioData(audioData)
          audioFrameCount++
        } catch (error) {
          log('ERROR', `❌ 音频播放失败: ${error.message}`)
          audioErrorCount++
        }
      },
      error: (error) => {
        log('ERROR', `❌ 音频解码错误: ${error.message}`)
        audioErrorCount++

        // 智能错误恢复
        if (audioErrorCount > 10 && audioErrorCount % 10 === 0) {
          log('WARNING', `累计音频解码错误: ${audioErrorCount} 次`)
          if (audioErrorCount > 50) {
            log('INFO', '🔄 尝试重新初始化音频解码器...')
            setTimeout(() => {
              audioErrorCount = Math.floor(audioErrorCount / 2)
              initAudioDecoder()
            }, 2000)
          }
        }
      }
    })

    // 🎵 尝试多种AAC配置，优化ADTS兼容性
    const configs = [
      { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 2 }, // AAC-LC 44.1kHz 立体声
      { codec: 'mp4a.40.2', sampleRate: 48000, numberOfChannels: 2 }, // AAC-LC 48kHz 立体声
      { codec: 'mp4a.40.2', sampleRate: 44100, numberOfChannels: 1 }, // AAC-LC 44.1kHz 单声道
      { codec: 'mp4a.40.2', sampleRate: 22050, numberOfChannels: 2 }, // AAC-LC 22.05kHz
      { codec: 'mp4a.40.2', sampleRate: 32000, numberOfChannels: 2 }  // AAC-LC 32kHz
    ]

    let configSuccess = false
    for (const config of configs) {
      try {
        audioDecoder.configure(config)
        configSuccess = true
        log('SUCCESS', `🎵 AAC解码器配置成功: ${config.codec} ${config.sampleRate}Hz ${config.numberOfChannels}声道`)
        break
      } catch (e) {
        log('DEBUG', `配置 ${config.sampleRate}Hz/${config.numberOfChannels}ch 失败: ${e.message}`)
      }
    }

    if (!configSuccess) {
      throw new Error('所有AAC解码器配置都失败，请检查浏览器WebCodecs支持')
    }

    log('SUCCESS', '🎵 音频解码器已初始化')
    return true

  } catch (error) {
    log('ERROR', `❌ 音频解码器初始化失败: ${error.message}`)
    return false
  }
}

// 🎵 音频播放管理
let nextPlayTime = 0
let audioFramesPlayed = 0

/**
 * 🎵 播放音频数据 - 使用成熟的连续播放逻辑
 */
function playAudioData(audioData) {
  if (!audioContext) {
    audioData.close()
    log('ERROR', '🎵 音频上下文未初始化')
    return
  }

  try {
    // 确保音频上下文已启动
    if (audioContext.state === 'suspended') {
      audioContext.resume().then(() => {
        if (audioFramesPlayed === 0) {
          log('SUCCESS', '🎵 音频上下文已启动')
        }
      }).catch(err => {
        log('ERROR', `❌ 恢复音频上下文失败: ${err.message}`)
      })
      // 暂停状态下不播放
      audioData.close()
      return
    }

    // 验证音频数据
    if (!audioData || audioData.numberOfFrames === 0) {
      log('WARNING', '🎵 音频数据无效')
      audioData?.close()
      return
    }

    // 创建音频缓冲区
    const audioBuffer = audioContext.createBuffer(
      audioData.numberOfChannels,
      audioData.numberOfFrames,
      audioData.sampleRate
    )

    // 复制音频数据到缓冲区
    for (let channel = 0; channel < audioData.numberOfChannels; channel++) {
      const channelData = new Float32Array(audioData.numberOfFrames)
      audioData.copyTo(channelData, { planeIndex: channel })
      audioBuffer.copyToChannel(channelData, channel)
    }

    // 创建音频源
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)

    // 计算播放时间，确保连续播放
    const currentTime = audioContext.currentTime
    const bufferDuration = audioData.numberOfFrames / audioData.sampleRate

    if (nextPlayTime <= currentTime) {
      // 如果预定时间已过，立即播放
      nextPlayTime = currentTime
    }

    // 播放音频
    source.start(nextPlayTime)

    // 更新下次播放时间
    nextPlayTime += bufferDuration

    // 防止时间戳漂移
    if (nextPlayTime - currentTime > 0.5) {
      // 如果预定时间超前太多，重置为当前时间
      nextPlayTime = currentTime + bufferDuration
    }

    // 更新播放统计
    audioFramesPlayed++

    // 关闭音频数据
    audioData.close()

    // 记录播放状态
    if (audioFramesPlayed === 1) {
      log('SUCCESS', `🎵 音频播放开始: ${audioData.sampleRate}Hz, ${audioData.numberOfChannels}声道`)
    } else if (audioFramesPlayed % 200 === 0) {
      // 减少日志频率
      log('INFO', `🎵 音频播放正常: ${audioFramesPlayed} 帧, ${audioData.sampleRate}Hz, 延迟=${(nextPlayTime - currentTime).toFixed(3)}s`)
    }

    // 错误恢复：如果播放队列过长，清理
    if (nextPlayTime - currentTime > 1.0) {
      log('WARNING', '🎵 音频播放队列过长，重置时间戳')
      nextPlayTime = currentTime
    }

  } catch (error) {
    log('ERROR', `❌ 音频播放失败: ${error.message}`)
    audioData?.close()
  }
}

// 🎵 音频时间戳管理
let audioStartTime = null
let audioFrameDuration = 23.22 // AAC帧持续时间（毫秒）

/**
 * 🎵 处理音频数据 - 使用成熟的ADTS处理逻辑
 */
export async function handleCleanAudioData(data) {
  // 首次接收音频数据的调试信息
  if (audioFrameCount === 0) {
    log('SUCCESS', `🎵 首次接收音频数据: ${data.byteLength} 字节`)
    audioStartTime = performance.now()
  }

  // 检查音频解码器状态，确保其可用
  if (!audioDecoder || audioDecoder.state === 'closed') {
    if (!await initAudioDecoder()) {
      return false
    }
    // 初始化后等待一帧再处理数据
    setTimeout(() => handleCleanAudioData(data), 50)
    return false
  }

  try {
    // 检查解码器状态
    if (!audioDecoder || audioDecoder.state !== 'configured') {
      if (audioFrameCount < 10) { // 只在前10帧报告状态问题
        log('WARNING', `🎵 音频解码器状态异常: ${audioDecoder?.state || 'null'}，跳过此帧`)
      }
      return false
    }

    const uint8Data = new Uint8Array(data)
    if (uint8Data.length < 7) { // ADTS最小长度为7字节
      log('WARNING', `🎵 音频数据太短: ${uint8Data.length} 字节，跳过`)
      return false
    }

    // 检查ADTS格式
    const isADTS = uint8Data[0] === 0xFF && (uint8Data[1] & 0xF0) === 0xF0
    if (!isADTS) {
      if (audioFrameCount < 3) { // 减少警告频率
        log('DEBUG', '🎵 检测到非ADTS格式音频数据，尝试直接解码')
      }
    }

    // 解析ADTS头部获取准确的帧信息
    let sampleRate = 44100
    let channels = 2
    let frameLength = uint8Data.length

    if (isADTS && uint8Data.length >= 7) {
      const freqIndex = (uint8Data[2] >> 2) & 0x0F
      const channelConfig = ((uint8Data[2] & 0x01) << 2) | ((uint8Data[3] >> 6) & 0x03)
      frameLength = ((uint8Data[3] & 0x03) << 11) | (uint8Data[4] << 3) | ((uint8Data[5] & 0xE0) >> 5)

      // 频率映射表
      const sampleRates = [96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050, 16000, 12000, 11025, 8000, 7350]
      if (freqIndex < sampleRates.length) {
        sampleRate = sampleRates[freqIndex]
      }
      channels = channelConfig || 2

      // 计算准确的帧持续时间
      audioFrameDuration = (1024 * 1000) / sampleRate // AAC每帧1024个样本

      if (audioFrameCount < 3) {
        log('DEBUG', `🎵 ADTS解析: ${sampleRate}Hz, ${channels}声道, 帧长度=${frameLength}, 持续时间=${audioFrameDuration.toFixed(2)}ms`)
      }
    }

    // 验证帧长度
    if (isADTS && frameLength !== uint8Data.length) {
      if (audioFrameCount < 10) {
        log('WARNING', `🎵 ADTS帧长度不匹配: 头部=${frameLength}, 实际=${uint8Data.length}`)
      }
    }

    // 创建音频块，使用基于实际时间的时间戳
    const currentTime = performance.now()
    const elapsedTime = audioStartTime ? (currentTime - audioStartTime) : 0
    const expectedTimestamp = audioFrameCount * audioFrameDuration

    // 使用更准确的时间戳（微秒）
    const timestamp = Math.round(expectedTimestamp * 1000)

    const chunk = new EncodedAudioChunk({
      type: 'key',
      timestamp: timestamp,
      data: uint8Data
    })

    // 控制解码器队列大小
    if (audioDecoder.decodeQueueSize > 5) {
      if (audioFrameCount % 50 === 0) { // 减少警告频率
        log('WARNING', `🎵 解码队列过长: ${audioDecoder.decodeQueueSize}，跳过此帧`)
      }
      return false
    }

    // 解码音频
    audioDecoder.decode(chunk)
    audioFrameCount++

    // 定期输出统计信息（减少频率）
    if (audioFrameCount % 200 === 0) {
      const formatStr = isADTS ? 'ADTS-AAC' : 'Raw-AAC'
      const avgSize = Math.round(uint8Data.length)
      log('INFO', `🎵 音频解码正常: ${audioFrameCount} 帧, ${avgSize}字节/帧, ${formatStr}, ${sampleRate}Hz`)
    }

    return true

  } catch (error) {
    audioErrorCount++

    // 根据错误频率调整日志级别
    if (audioErrorCount <= 5) {
      log('ERROR', `❌ 音频解码器错误: ${error.message}`)
    } else if (audioErrorCount % 10 === 0) {
      log('WARNING', `🎵 累计音频解码错误: ${audioErrorCount} 次`)
    }

    // 智能错误恢复策略
    if (audioErrorCount > 20 && audioErrorCount % 20 === 0) {
      log('INFO', '🔄 尝试重新初始化音频解码器...')

      // 重新初始化解码器
      setTimeout(() => {
        try {
          if (audioDecoder) {
            audioDecoder.close()
          }
          audioDecoder = null
          audioErrorCount = Math.floor(audioErrorCount / 2) // 减半错误计数
          initAudioDecoder()
        } catch (reinitError) {
          log('ERROR', `❌ 音频解码器重新初始化失败: ${reinitError.message}`)
        }
      }, 500)
    }

    return false
  }
}

/**
 * 🎮 简单关键帧检测
 */
function isKeyFrame(data) {
  if (data.length < 5) return false

  // 检查NAL单元类型
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i + 1] === 0x00 &&
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      const nalType = data[i + 4] & 0x1F
      if (nalType === 5) return true // IDR帧
      if (nalType === 7) return true // SPS
      if (nalType === 8) return true // PPS
    }
  }
  return false
}

/**
 * 🎮 重置播放器
 */
export async function resetCleanPlayer() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }
    
    // 重新初始化
    if (canvas) {
      await initCleanH264Player(canvas, {
        width: canvas.width,
        height: canvas.height
      })
    }
    
    log('SUCCESS', '🔄 播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}

/**
 * 🎮 设置日志回调
 */
export function setCleanLogCallback(callback) {
  logCallback = callback
}

// 兼容旧函数名
export function setCleanPlayerLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎮 获取播放器状态
 */
export function getCleanStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats.value,
    totalFrames: frameCount,
    decodedFrames: decodedCount,
    errorFrames: errorCount,
    audioFrames: audioFrameCount,
    audioErrors: audioErrorCount
  }
}

// 兼容旧函数名
export function getCleanPlayerStats() {
  return getCleanStats()
}

/**
 * 🔍 诊断解码器状态
 */
export function diagnoseDecoder() {
  const diagnosis = {
    decoderExists: !!decoder,
    decoderState: decoder ? decoder.state : 'null',
    isConfigured: isDecoderConfigured,
    canvasExists: !!canvas,
    canvasSize: canvas ? `${canvas.width}x${canvas.height}` : 'null',
    frameCount: frameCount,
    decodedCount: decodedCount,
    errorCount: errorCount,
    webCodecsSupport: !!window.VideoDecoder,
    audioDecoderExists: !!audioDecoder,
    audioDecoderState: audioDecoder ? audioDecoder.state : 'null'
  }

  log('INFO', '🔍 解码器诊断信息:')
  log('INFO', `  📺 视频解码器: ${diagnosis.decoderExists ? '存在' : '不存在'} (状态: ${diagnosis.decoderState})`)
  log('INFO', `  🎮 配置状态: ${diagnosis.isConfigured ? '已配置' : '未配置'}`)
  log('INFO', `  🖼️ Canvas: ${diagnosis.canvasExists ? '存在' : '不存在'} (${diagnosis.canvasSize})`)
  log('INFO', `  📊 统计: 总帧=${diagnosis.frameCount}, 解码=${diagnosis.decodedCount}, 错误=${diagnosis.errorCount}`)
  log('INFO', `  🌐 WebCodecs支持: ${diagnosis.webCodecsSupport ? '是' : '否'}`)
  log('INFO', `  🎵 音频解码器: ${diagnosis.audioDecoderExists ? '存在' : '不存在'} (状态: ${diagnosis.audioDecoderState})`)

  return diagnosis
}

/**
 * 🎮 清理播放器
 */
export function cleanupCleanPlayer() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }

    if (audioDecoder && audioDecoder.state !== 'closed') {
      audioDecoder.close()
    }

    if (audioContext) {
      audioContext.close()
    }

    decoder = null
    audioDecoder = null
    audioContext = null
    canvas = null
    ctx = null
    isDecoderConfigured = false
    isInitialized.value = false
    isPlaying.value = false

    // 重置统计
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    audioFrameCount = 0
    audioErrorCount = 0
    audioFramesPlayed = 0
    realStartTime = null
    audioStartTime = null
    nextPlayTime = 0

    log('SUCCESS', '🎮 播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}
