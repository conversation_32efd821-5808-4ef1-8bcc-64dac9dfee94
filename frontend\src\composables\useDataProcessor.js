import { ref, reactive } from 'vue'

/**
 * 数据处理 Composable
 * 负责分片重组、数据解析和处理
 */
export function useDataProcessor() {
  // 分片数据重组
  const chunkBuffers = new Map() // frameId -> { chunks: Map, totalChunks: number, totalSize: number, dataType: string }

  // 掉帧统计
  const frameStats = reactive({
    h264: { received: 0, completed: 0, dropped: 0, lastFrameId: 0 },
    audio: { received: 0, completed: 0, dropped: 0, lastFrameId: 0 }
  })

  // 🚀 优化缓冲区管理 - 减少延迟
  const bufferManager = reactive({
    maxPendingFrames: 2, // 🚀 减少到2帧缓冲，降低延迟
    maxBufferAge: 1000,  // 🚀 1秒超时，快速清理过期帧
    lastCleanup: Date.now()
  })

  // 事件回调
  const onH264Data = ref(null)
  const onAudioData = ref(null)
  const onError = ref(null)

  /**
   * 🚀 优化缓冲区清理 - 更频繁的清理减少延迟
   */
  function cleanupOldBuffers() {
    const now = Date.now()

    // 🚀 提高清理频率到500ms
    if (now - bufferManager.lastCleanup < 500) {
      return
    }

    let cleanedCount = 0
    const framesToDelete = []

    for (const [frameId, frameBuffer] of chunkBuffers) {
      const age = now - frameBuffer.timestamp
      
      if (age > bufferManager.maxBufferAge) {
        framesToDelete.push(frameId)
        cleanedCount++
      }
    }

    // 删除过期帧
    framesToDelete.forEach(frameId => {
      const frameBuffer = chunkBuffers.get(frameId)
      if (frameBuffer) {
        const stats = frameStats[frameBuffer.dataType]
        if (stats) {
          stats.dropped++
        }
        chunkBuffers.delete(frameId)
      }
    })

    // 如果缓冲区太多，删除最旧的
    if (chunkBuffers.size > bufferManager.maxPendingFrames) {
      const sortedFrames = Array.from(chunkBuffers.entries())
        .sort((a, b) => a[1].timestamp - b[1].timestamp)
      
      const toDelete = sortedFrames.slice(0, chunkBuffers.size - bufferManager.maxPendingFrames)
      toDelete.forEach(([frameId, frameBuffer]) => {
        const stats = frameStats[frameBuffer.dataType]
        if (stats) {
          stats.dropped++
        }
        chunkBuffers.delete(frameId)
        cleanedCount++
      })
    }

    bufferManager.lastCleanup = now

    if (cleanedCount > 0) {
      console.log(`🔧 清理了 ${cleanedCount} 个过期/过多的帧缓冲区`)
    }
  }

  /**
   * 处理分片头部消息
   */
  function handleChunkHeader(message) {
    console.log(`🔧 收到分片头: frameId=${message.frameId}, type=${message.dataType}, chunks=${message.totalChunks}, size=${message.totalSize}`)

    // 清理旧缓冲区
    cleanupOldBuffers()

    // 更新统计信息
    const stats = frameStats[message.dataType]
    if (stats) {
      stats.received++

      // 检查是否有跳帧
      if (message.frameId > stats.lastFrameId + 1 && stats.lastFrameId > 0) {
        const skipped = message.frameId - stats.lastFrameId - 1
        stats.dropped += skipped
        console.warn(`🔧 检测到 ${message.dataType} 跳帧: ${skipped} 帧 (${stats.lastFrameId + 1} -> ${message.frameId})`)
      }
      stats.lastFrameId = message.frameId
    }

    // 创建新的帧缓冲区
    chunkBuffers.set(message.frameId, {
      chunks: new Map(),
      totalChunks: message.totalChunks,
      totalSize: message.totalSize || 0,
      dataType: message.dataType,
      timestamp: Date.now()
    })

    return { success: true }
  }

  /**
   * 处理分片数据
   */
  function handleChunkData(uint8Array) {
    try {
      const dataView = new DataView(uint8Array.buffer, uint8Array.byteOffset)
      const frameId = dataView.getUint32(0, true)
      const chunkIndex = dataView.getUint32(4, true)
      const chunkSize = dataView.getUint32(8, true)

      // 验证数据完整性
      if (uint8Array.length !== 12 + chunkSize) {
        console.error(`分片数据大小不匹配: 期望 ${12 + chunkSize}, 实际 ${uint8Array.length}`)
        return { success: false, error: '分片数据大小不匹配' }
      }

      const frameBuffer = chunkBuffers.get(frameId)
      if (!frameBuffer) {
        console.warn(`收到未知帧的分片: frameId=${frameId}, chunkIndex=${chunkIndex}`)
        return { success: false, error: '未知帧ID' }
      }

      // 提取实际数据
      const chunkData = uint8Array.slice(12)
      frameBuffer.chunks.set(chunkIndex, chunkData)

      console.log(`🔧 收到分片 ${chunkIndex}/${frameBuffer.totalChunks - 1} for frame ${frameId} (${chunkSize} bytes)`)

      // 检查是否收集完所有分片
      if (frameBuffer.chunks.size === frameBuffer.totalChunks) {
        const result = reassembleFrame(frameId, frameBuffer)
        chunkBuffers.delete(frameId) // 清理已完成的帧
        return result
      }

      return { success: true, partial: true }

    } catch (error) {
      console.error('🔧 处理分片数据失败:', error.message)
      return { success: false, error: error.message }
    }
  }

  /**
   * 重组完整帧
   */
  function reassembleFrame(frameId, frameBuffer) {
    const { chunks, dataType, totalChunks } = frameBuffer
    let { totalSize } = frameBuffer

    // 验证所有分片都已收到
    if (chunks.size !== totalChunks) {
      console.error(`分片不完整: ${chunks.size}/${totalChunks} for frame ${frameId}`)
      return { success: false, error: '分片不完整' }
    }

    // 如果总大小未知，计算实际大小
    if (totalSize === 0) {
      totalSize = 0
      for (let i = 0; i < totalChunks; i++) {
        const chunk = chunks.get(i)
        if (chunk) {
          totalSize += chunk.length
        }
      }
      console.log(`🔧 动态计算总大小: ${totalSize} 字节`)
    }

    try {
      // 重组数据
      const completeData = new Uint8Array(totalSize)
      let offset = 0

      for (let i = 0; i < totalChunks; i++) {
        const chunk = chunks.get(i)
        if (!chunk) {
          throw new Error(`缺少分片 ${i}`)
        }
        completeData.set(chunk, offset)
        offset += chunk.length
      }

      console.log(`🔧 重组完成: frame ${frameId}, type=${dataType}, size=${completeData.length}`)

      // 更新统计信息
      const stats = frameStats[dataType]
      if (stats) {
        stats.completed++
      }

      // 调用相应的处理函数
      if (dataType === 'h264' && onH264Data.value) {
        onH264Data.value(completeData)
      } else if (dataType === 'audio' && onAudioData.value) {
        onAudioData.value(completeData)
      }

      return { success: true, dataType, size: completeData.length }

    } catch (error) {
      console.error(`🔧 处理重组帧失败: ${error.message}`)
      if (onError.value) {
        onError.value(error)
      }
      return { success: false, error: error.message }
    }
  }

  /**
   * 处理DataChannel消息
   */
  function processDataChannelMessage(data) {
    try {
      // 尝试解析为JSON消息
      if (typeof data === 'string') {
        try {
          const message = JSON.parse(data)
          
          if (message.type === 'chunk_header') {
            return handleChunkHeader(message)
          } else {
            console.log('收到文本消息:', message)
            return { success: true, type: 'text', data: message }
          }
        } catch (e) {
          console.log('收到非JSON文本消息:', data)
          return { success: true, type: 'text', data }
        }
      }

      // 处理二进制数据
      const uint8Array = new Uint8Array(data)

      // 检查是否是新的二进制分片格式（至少12字节头部）
      if (uint8Array.length >= 12) {
        const dataView = new DataView(uint8Array.buffer, uint8Array.byteOffset)
        const possibleFrameId = dataView.getUint32(0, true)
        const possibleChunkIndex = dataView.getUint32(4, true)
        const possibleChunkSize = dataView.getUint32(8, true)

        // 验证是否是合理的分片数据格式
        if (possibleFrameId > 0 && possibleFrameId < 1000000 &&
            possibleChunkIndex < 100 && // 最多100个分片
            possibleChunkSize > 0 && possibleChunkSize <= 65000 && // 分片大小合理
            uint8Array.length === 12 + possibleChunkSize) { // 总大小匹配

          console.log(`🔧 检测到分片数据: frameId=${possibleFrameId}, chunkIndex=${possibleChunkIndex}, size=${possibleChunkSize}`)
          return handleChunkData(uint8Array)
        }
      }

      // 音频数据现在通过独立的AudioChannel传输，这里不再处理

      // 🚀 快速H264验证 - 减少验证开销
      if (!isValidH264Frame(uint8Array)) {
        // 🚀 减少无效数据的日志输出频率
        if (Math.random() < 0.1) { // 只有10%的概率输出日志
          console.log(`⚠️ 跳过无效数据: ${uint8Array.length} 字节`)
        }
        return { success: false, error: '无效数据格式' }
      }

      // 🚀 处理有效的H264数据 - 减少日志输出
      if (Math.random() < 0.05) { // 只有5%的概率输出日志，减少控制台开销
        console.log(`📺 收到有效H264数据: ${uint8Array.length} 字节`)
      }

      // 🚀 立即处理，不等待
      if (onH264Data.value) {
        onH264Data.value(uint8Array)
      }
      return { success: true, type: 'h264', size: uint8Array.length }

    } catch (error) {
      console.error('处理DataChannel消息失败:', error)
      if (onError.value) {
        onError.value(error)
      }
      return { success: false, error: error.message }
    }
  }

  /**
   * 检查是否为有效的H264帧（包含Annex-B起始码）
   */
  function isValidH264Frame(data) {
    if (data.length < 4) return false

    // 检查4字节起始码 0x00 0x00 0x00 0x01
    if (data[0] === 0x00 && data[1] === 0x00 && data[2] === 0x00 && data[3] === 0x01) {
      return true
    }

    // 检查3字节起始码 0x00 0x00 0x01
    if (data.length >= 3 && data[0] === 0x00 && data[1] === 0x00 && data[2] === 0x01) {
      return true
    }

    return false
  }

  /**
   * 获取统计信息
   */
  function getStats() {
    return {
      frameStats: { ...frameStats },
      bufferStats: {
        pendingFrames: chunkBuffers.size,
        maxPendingFrames: bufferManager.maxPendingFrames,
        lastCleanup: bufferManager.lastCleanup
      }
    }
  }

  /**
   * 重置统计信息
   */
  function resetStats() {
    frameStats.h264 = { received: 0, completed: 0, dropped: 0, lastFrameId: 0 }
    frameStats.audio = { received: 0, completed: 0, dropped: 0, lastFrameId: 0 }
    chunkBuffers.clear()
    bufferManager.lastCleanup = Date.now()
  }

  /**
   * 清理资源
   */
  function cleanup() {
    chunkBuffers.clear()
    resetStats()
  }

  return {
    // 状态
    frameStats,
    bufferManager,

    // 事件回调
    onH264Data,
    onAudioData,
    onError,

    // 方法
    processDataChannelMessage,
    getStats,
    resetStats,
    cleanup
  }
}
