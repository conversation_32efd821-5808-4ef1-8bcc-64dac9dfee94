import { ref, computed } from 'vue'
import { initCleanH264Player, handleCleanH264Data, cleanupCleanPlayer } from './useCleanH264Player'
import { initSimpleCanvasPlayer, handleSimpleH264Data, cleanupSimplePlayer } from './useSimpleCanvasPlayer'
import { initFFmpegPlayer, handleFFmpegH264Data, cleanupFFmpegPlayer } from './useFFmpegPlayer'
import { initBroadwayPlayer, handleBroadwayH264Data, cleanupBroadwayPlayer } from './useBroadwayPlayer'
import { initFixedCanvasPlayer, handleFixedCanvasH264Data, cleanupFixedCanvasPlayer } from './useFixedCanvasPlayer'

export function useDecoderManager() {
  const isInitialized = ref(false)
  const h264FrameCount = ref(0)
  const decoderType = ref('webcodecs') // 🚀 恢复使用WebCodecs播放器
  const currentDecoder = computed(() => {
    switch(decoderType.value) {
      case 'webcodecs': return 'WebCodecs H264播放器'
      case 'simple': return '简单Canvas播放器'
      case 'ffmpeg': return 'FFmpeg播放器'
      case 'broadway': return 'Broadway H264播放器'
      case 'fixed': return '修复Canvas播放器'
      default: return '修复Canvas播放器'
    }
  })
  let canvasElement = null

  const initDecoder = async (canvas, options = {}, logCallback) => {
    try {
      if (!canvas) throw new Error('Canvas required')

      // 保存Canvas引用
      canvasElement = canvas

      console.log(`🎬 初始化${currentDecoder.value}...`)

      let result = false

      // 🎬 根据解码器类型初始化
      switch(decoderType.value) {
        case 'webcodecs':
          result = await initCleanH264Player(canvas, options, logCallback)
          break
        case 'simple':
          result = await initSimpleCanvasPlayer(canvas, options, logCallback)
          break
        case 'ffmpeg':
          result = await initFFmpegPlayer(canvas, options, logCallback)
          break
        case 'broadway':
          result = await initBroadwayPlayer(canvas, options, logCallback)
          break
        case 'fixed':
          result = await initFixedCanvasPlayer(canvas, options, logCallback)
          break
        default:
          throw new Error(`未知的解码器类型: ${decoderType.value}`)
      }

      if (result) {
        isInitialized.value = true
        console.log(`✅ ${currentDecoder.value}初始化成功`)
        return true
      } else {
        throw new Error(`${currentDecoder.value}初始化失败`)
      }
    } catch (error) {
      console.error('❌ 播放器初始化失败:', error)
      return false
    }
  }

  const handleH264Data = async (data) => {
    if (!isInitialized.value) {
      return { success: false, error: '解码器未初始化' }
    }

    h264FrameCount.value++

    // 🎬 根据当前解码器类型处理数据
    try {
      let result

      switch(decoderType.value) {
        case 'webcodecs':
          result = await handleCleanH264Data(data)
          break
        case 'simple':
          result = await handleSimpleH264Data(data)
          break
        case 'ffmpeg':
          result = await handleFFmpegH264Data(data)
          break
        case 'broadway':
          result = await handleBroadwayH264Data(data)
          break
        case 'fixed':
          result = await handleFixedCanvasH264Data(data)
          break
        default:
          throw new Error(`未知的解码器类型: ${decoderType.value}`)
      }

      if (result) {
        // 🚀 减少日志输出频率，避免控制台卡顿
        if (h264FrameCount.value % 30 === 1) {
          console.log(`📺 ${currentDecoder.value}处理H264: ${data.length}字节 (帧${h264FrameCount.value})`)
        }
        return { success: true, decoder: currentDecoder.value }
      } else {
        return { success: false, error: 'H264解码失败', decoder: currentDecoder.value }
      }
    } catch (error) {
      console.error('H264解码错误:', error)
      return { success: false, error: error.message, decoder: currentDecoder.value }
    }
  }

  const handleAudioData = async (data) => {
    try {
      // 确保data是Uint8Array格式
      let audioData
      if (data instanceof ArrayBuffer) {
        audioData = new Uint8Array(data)
      } else if (data.buffer && data.buffer instanceof ArrayBuffer) {
        audioData = new Uint8Array(data.buffer, data.byteOffset, data.byteLength)
      } else {
        audioData = data
      }

      const dataSize = audioData.length || audioData.byteLength || 0
      console.log(`🎵 ${currentDecoder.value}处理音频: ${dataSize}字节`)

      // 根据解码器类型处理音频
      switch(decoderType.value) {
        case 'webcodecs':
          // 使用WebCodecs播放器的音频处理函数
          const { handleCleanAudioData } = await import('./useCleanH264Player')
          const result = await handleCleanAudioData(audioData)
          return { success: result, decoder: currentDecoder.value }

        default:
          // 其他播放器暂时只记录音频数据
          console.log(`🎵 ${currentDecoder.value}接收音频数据: ${dataSize}字节`)
          return { success: true, decoder: currentDecoder.value }
      }
    } catch (error) {
      console.error('音频处理错误:', error)
      return { success: false, error: error.message, decoder: currentDecoder.value }
    }
  }

  const cleanup = () => {
    try {
      switch(decoderType.value) {
        case 'webcodecs':
          cleanupCleanPlayer()
          break
        case 'simple':
          cleanupSimplePlayer()
          break
        case 'ffmpeg':
          cleanupFFmpegPlayer()
          break
        case 'broadway':
          cleanupBroadwayPlayer()
          break
        case 'fixed':
          cleanupFixedCanvasPlayer()
          break
      }
    } catch (error) {
      console.error('清理播放器失败:', error)
    }
    isInitialized.value = false
    h264FrameCount.value = 0
    console.log(`🎨 ${currentDecoder.value}已清理`)
  }

  const switchDecoder = async (targetType = null) => {
    const decoderTypes = ['fixed', 'broadway', 'ffmpeg', 'simple', 'webcodecs']
    const currentIndex = decoderTypes.indexOf(decoderType.value)

    // 如果指定了目标类型，使用指定类型；否则切换到下一个
    const newType = targetType || decoderTypes[(currentIndex + 1) % decoderTypes.length]

    console.log(`🔄 切换解码器: ${currentDecoder.value} -> ${newType}`)

    // 清理当前解码器
    cleanup()

    // 切换解码器类型
    decoderType.value = newType

    // 重新初始化
    if (canvasElement) {
      const result = await initDecoder(canvasElement, { width: 1600, height: 960, framerate: 30 })
      console.log(`🔄 解码器切换${result ? '成功' : '失败'}: ${currentDecoder.value}`)
      return result
    }

    return false
  }

  return {
    isInitialized,
    currentDecoder,
    decoderType,
    initDecoder,
    handleH264Data,
    handleAudioData,
    cleanup,
    switchDecoder
  }
}
