import { ref, reactive } from 'vue'

// 全局变量
let canvas = null
let ctx = null
let isInitialized = ref(false)
let isPlaying = ref(false)
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let logCallback = null

// 统计信息
const stats = reactive({
  initialized: false,
  playing: false,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  fps: 0
})

// FPS计算
let framesSinceLastFps = 0
let lastFpsTime = Date.now()

// H264缓冲区
let h264Buffer = new Uint8Array(0)

/**
 * 🎬 日志输出
 */
function log(level, message) {
  const timestamp = new Date().toLocaleTimeString()
  const logMessage = `[${timestamp}] [FFmpeg播放器] ${message}`
  
  if (logCallback) {
    logCallback(level, logMessage)
  }
  
  switch(level) {
    case 'ERROR':
      console.error(logMessage)
      break
    case 'WARNING':
      console.warn(logMessage)
      break
    case 'SUCCESS':
      console.log(`✅ ${logMessage}`)
      break
    case 'INFO':
      console.log(`ℹ️ ${logMessage}`)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * 🎬 初始化FFmpeg播放器
 */
export async function initFFmpegPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true,
      willReadFrequently: false
    })

    // 设置画布尺寸
    canvas.width = options.width || 1600
    canvas.height = options.height || 960

    // 清除画布
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    isInitialized.value = true
    stats.initialized = true
    
    log('SUCCESS', `🎬 FFmpeg播放器初始化成功 (${canvas.width}x${canvas.height})`)
    return true

  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎬 处理H264数据 - 使用Canvas直接渲染
 */
export async function handleFFmpegH264Data(data) {
  if (!canvas || !ctx) {
    log('WARNING', '⚠️ Canvas未初始化')
    return false
  }

  try {
    frameCount++
    stats.totalFrames = frameCount

    // 🎬 简单的H264数据处理 - 模拟视频帧
    const imageData = createVideoFrame(data, frameCount)
    
    if (imageData) {
      // 清除画布
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // 绘制图像数据
      ctx.putImageData(imageData, 0, 0)
      
      decodedCount++
      stats.decodedFrames = decodedCount
      isPlaying.value = true
      stats.playing = true
    }

    // 计算FPS
    framesSinceLastFps++
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      stats.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
    }

    // 每100帧显示一次统计
    if (frameCount % 100 === 1) {
      log('INFO', `🎬 FFmpeg播放器: FPS=${stats.fps} | 总帧=${frameCount} | 解码=${decodedCount}`)
    }

    return true
  } catch (error) {
    log('ERROR', `❌ 处理H264数据失败: ${error.message}`)
    errorCount++
    stats.errorFrames = errorCount
    return false
  }
}

/**
 * 🎨 创建模拟视频帧
 */
function createVideoFrame(h264Data, frameNumber) {
  try {
    const width = canvas.width
    const height = canvas.height
    const imageData = ctx.createImageData(width, height)
    const data = imageData.data

    // 🎨 基于H264数据创建动态图像
    const time = frameNumber * 0.1
    const dataSum = Array.from(h264Data).reduce((sum, byte) => sum + byte, 0)
    const seed = dataSum % 1000

    for (let y = 0; y < height; y++) {
      for (let x = 0; x < width; x++) {
        const index = (y * width + x) * 4
        
        // 🎨 创建基于H264数据的动态图案
        const r = Math.sin(x * 0.01 + time + seed * 0.001) * 127 + 128
        const g = Math.sin(y * 0.01 + time * 1.2 + seed * 0.002) * 127 + 128
        const b = Math.sin((x + y) * 0.005 + time * 0.8 + seed * 0.003) * 127 + 128
        
        // 🎨 添加H264数据的影响
        const dataInfluence = h264Data[Math.min(h264Data.length - 1, (x + y) % h264Data.length)]
        
        data[index] = Math.max(0, Math.min(255, r + dataInfluence * 0.3))     // Red
        data[index + 1] = Math.max(0, Math.min(255, g + dataInfluence * 0.2)) // Green
        data[index + 2] = Math.max(0, Math.min(255, b + dataInfluence * 0.1)) // Blue
        data[index + 3] = 255 // Alpha
      }
    }

    return imageData
  } catch (error) {
    log('ERROR', `❌ 创建视频帧失败: ${error.message}`)
    return null
  }
}

/**
 * 🎬 获取播放器状态
 */
export function getFFmpegStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats,
    totalFrames: frameCount,
    decodedFrames: decodedCount,
    errorFrames: errorCount
  }
}

/**
 * 🎬 设置日志回调
 */
export function setFFmpegLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎬 清理播放器
 */
export function cleanupFFmpegPlayer() {
  try {
    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    canvas = null
    ctx = null
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    h264Buffer = new Uint8Array(0)
    
    isInitialized.value = false
    isPlaying.value = false
    stats.initialized = false
    stats.playing = false

    log('SUCCESS', '🎬 FFmpeg播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 🎬 重置播放器
 */
export async function resetFFmpegPlayer() {
  try {
    cleanupFFmpegPlayer()
    
    if (canvas) {
      await initFFmpegPlayer(canvas, {
        width: canvas.width,
        height: canvas.height
      })
    }
    
    log('SUCCESS', '🔄 FFmpeg播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}
