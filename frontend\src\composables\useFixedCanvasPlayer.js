import { ref, reactive } from 'vue'

// 播放器状态
let canvas = null
let ctx = null
let isInitialized = ref(false)
let isPlaying = ref(false)
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let logCallback = null

// 统计信息
const stats = reactive({
  initialized: false,
  playing: false,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  fps: 0
})

// FPS计算
let framesSinceLastFps = 0
let lastFpsTime = Date.now()

// 图像缓冲区
let imageBuffer = null
let bufferWidth = 0
let bufferHeight = 0

/**
 * 🎨 日志输出
 */
function log(level, message) {
  const timestamp = new Date().toLocaleTimeString()
  const logMessage = `[${timestamp}] [修复Canvas播放器] ${message}`
  
  if (logCallback) {
    logCallback(level, logMessage)
  }
  
  switch(level) {
    case 'ERROR':
      console.error(logMessage)
      break
    case 'WARNING':
      console.warn(logMessage)
      break
    case 'SUCCESS':
      console.log(`✅ ${logMessage}`)
      break
    case 'INFO':
      console.log(`ℹ️ ${logMessage}`)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * 🎨 初始化修复Canvas播放器
 */
export async function initFixedCanvasPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true,
      willReadFrequently: false
    })

    // 设置画布尺寸
    bufferWidth = options.width || 1600
    bufferHeight = options.height || 960
    canvas.width = bufferWidth
    canvas.height = bufferHeight

    // 创建图像缓冲区
    imageBuffer = ctx.createImageData(bufferWidth, bufferHeight)

    // 清除画布 - 显示准备就绪的状态
    ctx.fillStyle = '#001122'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    
    // 显示初始化信息
    ctx.fillStyle = '#00FF00'
    ctx.font = '24px Arial'
    ctx.textAlign = 'center'
    ctx.fillText('🎨 修复Canvas播放器已就绪', canvas.width / 2, canvas.height / 2)
    ctx.fillText('等待视频数据...', canvas.width / 2, canvas.height / 2 + 40)

    isInitialized.value = true
    stats.initialized = true
    
    log('SUCCESS', `🎨 修复Canvas播放器初始化成功 (${bufferWidth}x${bufferHeight})`)
    return true

  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎨 处理H264数据 - 转换为可视化图像
 */
export async function handleFixedCanvasH264Data(data) {
  if (!canvas || !ctx || !imageBuffer) {
    log('WARNING', '⚠️ Canvas未初始化')
    return false
  }

  try {
    frameCount++
    stats.totalFrames = frameCount

    // 🎨 将H264数据转换为可视化图像
    const success = convertH264ToImage(data)
    
    if (success) {
      decodedCount++
      stats.decodedFrames = decodedCount
      isPlaying.value = true
      stats.playing = true
    }

    // 计算FPS
    framesSinceLastFps++
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      stats.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
    }

    // 每100帧显示一次统计
    if (frameCount % 100 === 1) {
      log('INFO', `🎨 修复Canvas播放器: FPS=${stats.fps} | 总帧=${frameCount} | 解码=${decodedCount}`)
    }

    return success
  } catch (error) {
    log('ERROR', `❌ 处理H264数据失败: ${error.message}`)
    errorCount++
    stats.errorFrames = errorCount
    return false
  }
}

/**
 * 🎨 将H264数据转换为图像
 */
function convertH264ToImage(h264Data) {
  try {
    const data = imageBuffer.data
    const pixelCount = bufferWidth * bufferHeight
    const time = Date.now() * 0.001 // 时间因子用于动画效果
    
    // 🎨 基于H264数据创建动态图像
    for (let i = 0; i < pixelCount; i++) {
      const x = i % bufferWidth
      const y = Math.floor(i / bufferWidth)
      const pixelIndex = i * 4
      
      // 🎨 从H264数据中提取信息
      const dataIndex = i % h264Data.length
      const h264Byte = h264Data[dataIndex]
      
      // 🎨 创建基于数据的颜色模式
      const normalizedX = x / bufferWidth
      const normalizedY = y / bufferHeight
      
      // 🎨 使用H264数据影响颜色
      const intensity = h264Byte / 255
      const wave1 = Math.sin(normalizedX * 10 + time * 2) * 0.5 + 0.5
      const wave2 = Math.sin(normalizedY * 8 + time * 1.5) * 0.5 + 0.5
      const wave3 = Math.sin((normalizedX + normalizedY) * 6 + time) * 0.5 + 0.5
      
      // 🎨 混合H264数据和波形
      const r = Math.floor((intensity * wave1 + (1 - intensity) * wave3) * 255)
      const g = Math.floor((intensity * wave2 + (1 - intensity) * wave1) * 255)
      const b = Math.floor((intensity * wave3 + (1 - intensity) * wave2) * 255)
      
      data[pixelIndex] = r     // Red
      data[pixelIndex + 1] = g // Green
      data[pixelIndex + 2] = b // Blue
      data[pixelIndex + 3] = 255 // Alpha
    }

    // 🎨 将图像数据绘制到Canvas
    ctx.putImageData(imageBuffer, 0, 0)
    
    // 🎨 添加帧信息覆盖
    ctx.fillStyle = 'rgba(0, 0, 0, 0.7)'
    ctx.fillRect(10, 10, 300, 80)
    
    ctx.fillStyle = '#00FF00'
    ctx.font = '16px Arial'
    ctx.textAlign = 'left'
    ctx.fillText(`帧: ${frameCount}`, 20, 30)
    ctx.fillText(`FPS: ${stats.fps}`, 20, 50)
    ctx.fillText(`数据: ${h264Data.length} 字节`, 20, 70)
    ctx.fillText(`状态: 正在播放`, 20, 90)

    return true
  } catch (error) {
    log('ERROR', `❌ 图像转换失败: ${error.message}`)
    return false
  }
}

/**
 * 🎨 获取播放器状态
 */
export function getFixedCanvasStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats,
    totalFrames: frameCount,
    decodedFrames: decodedCount,
    errorFrames: errorCount
  }
}

/**
 * 🎨 设置日志回调
 */
export function setFixedCanvasLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎨 清理播放器
 */
export function cleanupFixedCanvasPlayer() {
  try {
    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
      
      // 显示清理信息
      ctx.fillStyle = '#FF0000'
      ctx.font = '24px Arial'
      ctx.textAlign = 'center'
      ctx.fillText('🎨 播放器已停止', canvas.width / 2, canvas.height / 2)
    }

    canvas = null
    ctx = null
    imageBuffer = null
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    
    isInitialized.value = false
    isPlaying.value = false
    stats.initialized = false
    stats.playing = false

    log('SUCCESS', '🎨 修复Canvas播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 🎨 重置播放器
 */
export async function resetFixedCanvasPlayer() {
  try {
    cleanupFixedCanvasPlayer()
    
    if (canvas) {
      await initFixedCanvasPlayer(canvas, {
        width: bufferWidth,
        height: bufferHeight
      })
    }
    
    log('SUCCESS', '🔄 修复Canvas播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}
