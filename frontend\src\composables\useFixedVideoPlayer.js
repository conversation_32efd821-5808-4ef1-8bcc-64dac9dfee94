/**
 * 修复版视频播放器 - 解决AVC配置问题
 * 动态提取SPS/PPS，正确配置WebCodecs
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  totalFrames: 0,
  droppedFrames: 0,
  errorFrames: 0
})

// 核心组件
let canvas = null
let ctx = null
let decoder = null
let nalBuffer = new Uint8Array(0)

// SPS/PPS管理
let spsData = null
let ppsData = null
let isDecoderConfigured = false

// 统计
let frameCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化修复版播放器
 */
export async function initFixedVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs not supported')
    }

    // 创建解码器（暂不配置）
    decoder = new VideoDecoder({
      output: handleFrame,
      error: handleError
    })

    isInitialized.value = true
    log('SUCCESS', '🎬 修复版播放器已初始化，等待SPS/PPS')
    
    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 处理解码帧
 */
function handleFrame(frame) {
  try {
    if (!frame || frame.codedWidth === 0 || frame.codedHeight === 0) {
      frame?.close()
      videoStats.value.droppedFrames++
      return
    }

    // 调整画布尺寸
    if (canvas.width !== frame.codedWidth || canvas.height !== frame.codedHeight) {
      canvas.width = frame.codedWidth
      canvas.height = frame.codedHeight
      log('INFO', `📐 分辨率: ${frame.codedWidth}x${frame.codedHeight}`)
    }

    // 清除并渲染
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)
    ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)

    // 更新统计
    frameCount++
    videoStats.value.totalFrames++
    framesSinceLastFps++
    
    // 计算FPS
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      videoStats.value.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
    }
    
    isPlaying.value = true
    frame.close()
    
  } catch (error) {
    log('ERROR', `❌ 帧渲染失败: ${error.message}`)
    frame?.close()
    videoStats.value.errorFrames++
  }
}

/**
 * 处理解码器错误
 */
function handleError(error) {
  log('ERROR', `❌ 解码器错误: ${error.message}`)
  videoStats.value.errorFrames++
  
  // 重置配置状态
  isDecoderConfigured = false
  spsData = null
  ppsData = null
}

/**
 * 修复版H264数据处理
 */
export async function handleFixedH264Data(data) {
  if (!decoder) {
    return false
  }

  try {
    // 合并到NAL缓冲区
    const newBuffer = new Uint8Array(nalBuffer.length + data.length)
    newBuffer.set(nalBuffer)
    newBuffer.set(data, nalBuffer.length)
    nalBuffer = newBuffer

    // 提取NAL单元
    const nalUnits = extractNALUnits()
    
    for (const nal of nalUnits) {
      await processNAL(nal)
    }

    return true
    
  } catch (error) {
    log('ERROR', `❌ H264处理失败: ${error.message}`)
    return false
  }
}

/**
 * 提取NAL单元
 */
function extractNALUnits() {
  const nalUnits = []
  let start = 0
  
  for (let i = 0; i < nalBuffer.length - 3; i++) {
    if (nalBuffer[i] === 0x00 && nalBuffer[i+1] === 0x00 && 
        nalBuffer[i+2] === 0x00 && nalBuffer[i+3] === 0x01) {
      
      if (start > 0) {
        nalUnits.push(nalBuffer.slice(start, i))
      }
      start = i
    }
  }
  
  // 处理最后一个NAL
  if (start > 0 && start < nalBuffer.length) {
    nalUnits.push(nalBuffer.slice(start))
    nalBuffer = new Uint8Array(0)
  } else if (nalUnits.length > 0) {
    nalBuffer = nalBuffer.slice(start)
  }
  
  return nalUnits
}

/**
 * 处理单个NAL单元
 */
async function processNAL(nal) {
  if (nal.length < 5) return

  const nalType = nal[4] & 0x1F

  // 调试：记录NAL类型
  if (frameCount % 50 === 0) {
    log('INFO', `🔍 NAL类型: ${nalType} (${getNALTypeName(nalType)})`)
  }

  // 处理SPS (7) - 增强检测
  if (nalType === 7) {
    spsData = nal.slice()
    log('SUCCESS', `🔧 SPS已提取 (${nal.length} bytes)`)
    log('INFO', `🔍 SPS内容: ${Array.from(nal.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`)
    await tryConfigureDecoder()
    return
  }

  // 处理PPS (8) - 增强检测
  if (nalType === 8) {
    ppsData = nal.slice()
    log('SUCCESS', `🔧 PPS已提取 (${nal.length} bytes)`)
    log('INFO', `🔍 PPS内容: ${Array.from(nal.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')}`)
    await tryConfigureDecoder()
    return
  }

  // 处理视频帧 (1, 5)
  if ((nalType === 1 || nalType === 5)) {
    const isKeyFrame = nalType === 5

    // 如果没有配置解码器，强制配置
    if (!isDecoderConfigured) {
      log('WARNING', '⚠️ 解码器未配置，强制配置解码器')
      await tryDefaultConfiguration()

      // 如果还是没配置成功，等待更多数据
      if (!isDecoderConfigured) {
        log('WARNING', '⚠️ 强制配置失败，跳过此帧')
        return
      }
    }

    if (isDecoderConfigured) {
      try {
        const chunk = new EncodedVideoChunk({
          type: isKeyFrame ? 'key' : 'delta',
          timestamp: performance.now() * 1000,
          data: nal
        })

        if (decoder.state === 'configured') {
          decoder.decode(chunk)
          if (isKeyFrame) {
            log('SUCCESS', '🔑 关键帧解码成功')
          }
        }

      } catch (error) {
        log('ERROR', `❌ 帧解码失败: ${error.message}`)
        videoStats.value.errorFrames++
      }
    }
  }
}

/**
 * 获取NAL类型名称
 */
function getNALTypeName(nalType) {
  const types = {
    1: 'P帧', 5: 'IDR帧', 6: 'SEI', 7: 'SPS', 8: 'PPS', 9: 'AUD'
  }
  return types[nalType] || `未知(${nalType})`
}

/**
 * 尝试默认配置（无SPS/PPS时的备选方案）
 */
async function tryDefaultConfiguration() {
  if (isDecoderConfigured) return

  try {
    // 生成默认SPS/PPS并配置
    if (!spsData) {
      spsData = generateDefaultSPS()
    }

    if (!ppsData) {
      // 生成默认PPS
      ppsData = new Uint8Array([
        0x00, 0x00, 0x00, 0x01, // NAL start code
        0x68, 0xeb, 0xe3, 0xcb, 0x22, 0xc0
      ])
      log('SUCCESS', '🔧 已生成默认PPS')
    }

    // 使用生成的SPS/PPS配置
    await tryConfigureDecoder()

  } catch (error) {
    log('ERROR', `❌ 默认配置失败: ${error.message}`)
  }
}

/**
 * 尝试配置解码器
 */
async function tryConfigureDecoder() {
  // 如果没有SPS但有PPS，生成默认SPS
  if (!spsData && ppsData) {
    log('WARNING', '⚠️ 没有收到SPS，生成默认SPS')
    spsData = generateDefaultSPS()
  }

  if (!spsData || !ppsData || isDecoderConfigured) {
    return
  }

  try {
    // 创建AVC配置描述符
    const description = createAVCConfig(spsData, ppsData)

    const config = {
      codec: 'avc1.42e01f', // 使用Baseline Profile
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-hardware',
      optimizeForLatency: true,
      description: description
    }

    await decoder.configure(config)
    isDecoderConfigured = true

    log('SUCCESS', '🚀 解码器配置成功 (SPS/PPS)')

  } catch (error) {
    log('ERROR', `❌ 解码器配置失败: ${error.message}`)
    isDecoderConfigured = false
  }
}

/**
 * 生成默认SPS (当收不到SPS时使用)
 */
function generateDefaultSPS() {
  // 标准H.264 Baseline Profile SPS for 1920x1080
  const defaultSPS = new Uint8Array([
    0x00, 0x00, 0x00, 0x01, // NAL start code
    0x67, 0x42, 0xe0, 0x1f, // NAL header + profile_idc(66=Baseline) + level_idc(31)
    0xac, 0xd9, 0x40, 0x50, 0x05, 0xbb, 0x01, 0x6a,
    0x02, 0x02, 0x02, 0x80, 0x00, 0x00, 0x03, 0x00,
    0x80, 0x00, 0x00, 0x1e, 0x07, 0x8c, 0x18, 0x60
  ])

  log('SUCCESS', '🔧 已生成默认SPS (Baseline Profile)')
  return defaultSPS
}

/**
 * 创建AVC配置描述符
 */
function createAVCConfig(sps, pps) {
  // 去掉起始码
  const spsPayload = sps.slice(4)
  const ppsPayload = pps.slice(4)
  
  const configSize = 11 + spsPayload.length + ppsPayload.length
  const config = new Uint8Array(configSize)
  let offset = 0
  
  // AVCDecoderConfigurationRecord
  config[offset++] = 0x01 // configurationVersion
  config[offset++] = spsPayload[1] // AVCProfileIndication
  config[offset++] = spsPayload[2] // profile_compatibility
  config[offset++] = spsPayload[3] // AVCLevelIndication
  config[offset++] = 0xFF // lengthSizeMinusOne
  
  // SPS
  config[offset++] = 0xE1 // numOfSequenceParameterSets
  config[offset++] = (spsPayload.length >> 8) & 0xFF
  config[offset++] = spsPayload.length & 0xFF
  config.set(spsPayload, offset)
  offset += spsPayload.length
  
  // PPS
  config[offset++] = 0x01 // numOfPictureParameterSets
  config[offset++] = (ppsPayload.length >> 8) & 0xFF
  config[offset++] = ppsPayload.length & 0xFF
  config.set(ppsPayload, offset)
  
  return config
}

/**
 * 获取统计信息
 */
export function getFixedStats() {
  return {
    ...videoStats.value,
    configured: isDecoderConfigured,
    hasSPS: !!spsData,
    hasPPS: !!ppsData
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }
    
    decoder = null
    canvas = null
    ctx = null
    nalBuffer = new Uint8Array(0)
    spsData = null
    ppsData = null
    isDecoderConfigured = false
    
    isInitialized.value = false
    isPlaying.value = false
    
    log('INFO', '🧹 修复版播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.reset()
    }
    
    nalBuffer = new Uint8Array(0)
    spsData = null
    ppsData = null
    isDecoderConfigured = false
    frameCount = 0
    
    videoStats.value = {
      fps: 0,
      totalFrames: 0,
      droppedFrames: 0,
      errorFrames: 0
    }
    
    log('SUCCESS', '🔄 播放器已重置，等待新的SPS/PPS')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
