import { ref, computed } from 'vue'

/**
 * 日志管理 Composable
 * 提供统一的日志记录和管理功能
 */
export function useLogger(maxLogs = 1000) {
  // 日志数据
  const logs = ref([])

  // 计算属性
  const logCount = computed(() => logs.value.length)
  const errorCount = computed(() => logs.value.filter(log => log.type === 'ERROR').length)
  const warningCount = computed(() => logs.value.filter(log => log.type === 'WARNING').length)
  const successCount = computed(() => logs.value.filter(log => log.type === 'SUCCESS').length)
  const infoCount = computed(() => logs.value.filter(log => log.type === 'INFO').length)

  /**
   * 添加日志
   * @param {string} type - 日志类型: INFO, SUCCESS, WARNING, ERROR
   * @param {string} message - 日志消息
   * @param {Object} metadata - 额外的元数据
   */
  function addLog(type, message, metadata = {}) {
    const timestamp = new Date().toLocaleTimeString()
    const logEntry = {
      id: Date.now() + Math.random(),
      time: timestamp,
      type: type.toUpperCase(),
      message,
      metadata,
      timestamp: Date.now()
    }

    logs.value.unshift(logEntry)

    // 限制日志数量
    if (logs.value.length > maxLogs) {
      logs.value = logs.value.slice(0, maxLogs)
    }

    // 控制台输出
    const consoleMethod = getConsoleMethod(type)
    consoleMethod(`[${timestamp}] ${type}: ${message}`, metadata)

    return logEntry
  }

  /**
   * 获取对应的控制台方法
   */
  function getConsoleMethod(type) {
    switch (type.toUpperCase()) {
      case 'ERROR':
        return console.error
      case 'WARNING':
        return console.warn
      case 'SUCCESS':
        return console.log
      case 'INFO':
      default:
        return console.log
    }
  }

  /**
   * 便捷方法
   */
  const info = (message, metadata) => addLog('INFO', message, metadata)
  const success = (message, metadata) => addLog('SUCCESS', message, metadata)
  const warning = (message, metadata) => addLog('WARNING', message, metadata)
  const error = (message, metadata) => addLog('ERROR', message, metadata)

  /**
   * 清空日志
   */
  function clearLogs() {
    logs.value = []
    console.clear()
  }

  /**
   * 根据类型过滤日志
   */
  function getLogsByType(type) {
    return logs.value.filter(log => log.type === type.toUpperCase())
  }

  /**
   * 根据时间范围过滤日志
   */
  function getLogsByTimeRange(startTime, endTime) {
    return logs.value.filter(log => 
      log.timestamp >= startTime && log.timestamp <= endTime
    )
  }

  /**
   * 搜索日志
   */
  function searchLogs(query) {
    const lowerQuery = query.toLowerCase()
    return logs.value.filter(log => 
      log.message.toLowerCase().includes(lowerQuery) ||
      log.type.toLowerCase().includes(lowerQuery)
    )
  }

  /**
   * 导出日志为文本
   */
  function exportLogsAsText() {
    return logs.value.map(log => 
      `[${log.time}] ${log.type}: ${log.message}`
    ).join('\n')
  }

  /**
   * 导出日志为JSON
   */
  function exportLogsAsJSON() {
    return JSON.stringify(logs.value, null, 2)
  }

  /**
   * 获取日志统计信息
   */
  function getLogStats() {
    return {
      total: logCount.value,
      info: infoCount.value,
      success: successCount.value,
      warning: warningCount.value,
      error: errorCount.value,
      oldest: logs.value.length > 0 ? logs.value[logs.value.length - 1].timestamp : null,
      newest: logs.value.length > 0 ? logs.value[0].timestamp : null
    }
  }

  /**
   * 批量添加日志
   */
  function addBatchLogs(logEntries) {
    logEntries.forEach(entry => {
      addLog(entry.type, entry.message, entry.metadata)
    })
  }

  /**
   * 设置日志级别过滤
   */
  const logLevels = {
    ERROR: 0,
    WARNING: 1,
    SUCCESS: 2,
    INFO: 3
  }

  const currentLogLevel = ref('INFO')

  function setLogLevel(level) {
    currentLogLevel.value = level.toUpperCase()
  }

  function shouldLog(type) {
    const typeLevel = logLevels[type.toUpperCase()]
    const currentLevel = logLevels[currentLogLevel.value]
    return typeLevel <= currentLevel
  }

  /**
   * 带级别控制的日志方法
   */
  function logWithLevel(type, message, metadata = {}) {
    if (shouldLog(type)) {
      return addLog(type, message, metadata)
    }
    return null
  }

  /**
   * 性能日志
   */
  function logPerformance(operation, duration, metadata = {}) {
    const message = `${operation} 耗时: ${duration}ms`
    const type = duration > 1000 ? 'WARNING' : 'INFO'
    return addLog(type, message, { ...metadata, duration, operation })
  }

  /**
   * 网络请求日志
   */
  function logNetworkRequest(method, url, status, duration, metadata = {}) {
    const message = `${method} ${url} - ${status} (${duration}ms)`
    const type = status >= 400 ? 'ERROR' : status >= 300 ? 'WARNING' : 'SUCCESS'
    return addLog(type, message, { ...metadata, method, url, status, duration })
  }

  /**
   * WebRTC事件日志
   */
  function logWebRTCEvent(event, details, metadata = {}) {
    const message = `WebRTC ${event}: ${details}`
    const type = event.includes('error') || event.includes('failed') ? 'ERROR' : 
                 event.includes('warning') ? 'WARNING' :
                 event.includes('connected') || event.includes('success') ? 'SUCCESS' : 'INFO'
    return addLog(type, message, { ...metadata, event, details })
  }

  return {
    // 状态
    logs,
    logCount,
    errorCount,
    warningCount,
    successCount,
    infoCount,
    currentLogLevel,

    // 基础方法
    addLog,
    info,
    success,
    warning,
    error,
    clearLogs,

    // 查询方法
    getLogsByType,
    getLogsByTimeRange,
    searchLogs,
    getLogStats,

    // 导出方法
    exportLogsAsText,
    exportLogsAsJSON,

    // 批量操作
    addBatchLogs,

    // 级别控制
    setLogLevel,
    logWithLevel,

    // 专用日志方法
    logPerformance,
    logNetworkRequest,
    logWebRTCEvent
  }
}
