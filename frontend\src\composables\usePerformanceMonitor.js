/**
 * 🚀 性能监控 Composable
 * 监控视频播放性能，帮助诊断卡顿问题
 */

import { ref, reactive, computed } from 'vue'

export function usePerformanceMonitor() {
  // 性能统计
  const stats = reactive({
    // 帧率统计
    fps: {
      current: 0,
      average: 0,
      min: Infinity,
      max: 0,
      samples: []
    },
    
    // 延迟统计
    latency: {
      decode: 0,
      render: 0,
      total: 0,
      samples: []
    },
    
    // 内存使用
    memory: {
      used: 0,
      total: 0,
      percentage: 0
    },
    
    // 解码器状态
    decoder: {
      queueSize: 0,
      droppedFrames: 0,
      errorFrames: 0,
      totalFrames: 0
    },
    
    // 网络统计
    network: {
      bytesReceived: 0,
      packetsReceived: 0,
      packetsLost: 0,
      bandwidth: 0
    }
  })

  // 性能警告
  const warnings = ref([])
  const isPerformanceGood = computed(() => {
    return stats.fps.current >= 25 && 
           stats.latency.total < 100 && 
           stats.decoder.queueSize < 3
  })

  // 时间戳记录
  let lastFrameTime = 0
  let frameCount = 0
  let startTime = performance.now()
  let lastStatsUpdate = 0

  /**
   * 🚀 记录帧接收
   */
  function recordFrameReceived(frameSize) {
    const now = performance.now()
    
    // 更新网络统计
    stats.network.bytesReceived += frameSize
    stats.network.packetsReceived++
    
    // 计算带宽 (每秒更新一次)
    if (now - lastStatsUpdate > 1000) {
      const elapsed = (now - lastStatsUpdate) / 1000
      stats.network.bandwidth = Math.round(stats.network.bytesReceived / elapsed / 1024) // KB/s
      stats.network.bytesReceived = 0 // 重置计数器
      lastStatsUpdate = now
    }
  }

  /**
   * 🚀 记录帧解码开始
   */
  function recordDecodeStart() {
    return performance.now()
  }

  /**
   * 🚀 记录帧解码完成
   */
  function recordDecodeEnd(startTime, frameSize) {
    const now = performance.now()
    const decodeTime = now - startTime
    
    // 更新解码延迟
    stats.latency.decode = decodeTime
    stats.latency.samples.push(decodeTime)
    
    // 保持最近100个样本
    if (stats.latency.samples.length > 100) {
      stats.latency.samples.shift()
    }
    
    // 检查解码性能
    if (decodeTime > 50) { // 超过50ms认为是性能问题
      addWarning(`解码延迟过高: ${decodeTime.toFixed(1)}ms`)
    }
    
    return now
  }

  /**
   * 🚀 记录帧渲染完成
   */
  function recordRenderEnd(decodeEndTime) {
    const now = performance.now()
    const renderTime = now - decodeEndTime
    
    // 更新渲染延迟
    stats.latency.render = renderTime
    stats.latency.total = stats.latency.decode + renderTime
    
    // 更新帧率统计
    frameCount++
    stats.decoder.totalFrames = frameCount
    
    if (lastFrameTime > 0) {
      const frameInterval = now - lastFrameTime
      const currentFps = 1000 / frameInterval
      
      // 更新FPS统计
      stats.fps.current = Math.round(currentFps)
      stats.fps.samples.push(currentFps)
      
      // 保持最近60个样本 (2秒@30fps)
      if (stats.fps.samples.length > 60) {
        stats.fps.samples.shift()
      }
      
      // 计算平均FPS
      if (stats.fps.samples.length > 0) {
        stats.fps.average = Math.round(
          stats.fps.samples.reduce((a, b) => a + b) / stats.fps.samples.length
        )
        stats.fps.min = Math.round(Math.min(...stats.fps.samples))
        stats.fps.max = Math.round(Math.max(...stats.fps.samples))
      }
      
      // 检查帧率性能
      if (currentFps < 20) {
        addWarning(`帧率过低: ${currentFps.toFixed(1)} FPS`)
      }
    }
    
    lastFrameTime = now
    
    // 检查渲染性能
    if (renderTime > 20) { // 超过20ms认为是性能问题
      addWarning(`渲染延迟过高: ${renderTime.toFixed(1)}ms`)
    }
  }

  /**
   * 🚀 更新解码器状态
   */
  function updateDecoderStats(queueSize, droppedFrames, errorFrames) {
    stats.decoder.queueSize = queueSize
    stats.decoder.droppedFrames = droppedFrames
    stats.decoder.errorFrames = errorFrames
    
    // 检查解码器队列
    if (queueSize > 5) {
      addWarning(`解码队列过长: ${queueSize} 帧`)
    }
    
    // 检查错误率
    if (frameCount > 100) {
      const errorRate = errorFrames / frameCount
      if (errorRate > 0.05) { // 错误率超过5%
        addWarning(`解码错误率过高: ${(errorRate * 100).toFixed(1)}%`)
      }
    }
  }

  /**
   * 🚀 更新内存使用情况
   */
  function updateMemoryStats() {
    if (performance.memory) {
      stats.memory.used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024) // MB
      stats.memory.total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024) // MB
      stats.memory.percentage = Math.round((stats.memory.used / stats.memory.total) * 100)
      
      // 检查内存使用
      if (stats.memory.percentage > 80) {
        addWarning(`内存使用过高: ${stats.memory.percentage}%`)
      }
    }
  }

  /**
   * 🚀 添加性能警告
   */
  function addWarning(message) {
    const warning = {
      message,
      timestamp: new Date().toLocaleTimeString(),
      id: Date.now()
    }
    
    warnings.value.unshift(warning)
    
    // 保持最近20个警告
    if (warnings.value.length > 20) {
      warnings.value.pop()
    }
    
    console.warn(`⚠️ 性能警告: ${message}`)
  }

  /**
   * 🚀 清除警告
   */
  function clearWarnings() {
    warnings.value = []
  }

  /**
   * 🚀 获取性能报告
   */
  function getPerformanceReport() {
    const uptime = (performance.now() - startTime) / 1000
    
    return {
      uptime: uptime.toFixed(1),
      fps: {
        current: stats.fps.current,
        average: stats.fps.average,
        min: stats.fps.min === Infinity ? 0 : stats.fps.min,
        max: stats.fps.max
      },
      latency: {
        decode: stats.latency.decode.toFixed(1),
        render: stats.latency.render.toFixed(1),
        total: stats.latency.total.toFixed(1)
      },
      decoder: {
        totalFrames: stats.decoder.totalFrames,
        droppedFrames: stats.decoder.droppedFrames,
        errorFrames: stats.decoder.errorFrames,
        queueSize: stats.decoder.queueSize,
        successRate: frameCount > 0 ? 
          ((frameCount - stats.decoder.errorFrames) / frameCount * 100).toFixed(1) : '0'
      },
      network: {
        bandwidth: stats.network.bandwidth,
        packetsReceived: stats.network.packetsReceived,
        packetsLost: stats.network.packetsLost
      },
      memory: stats.memory,
      warnings: warnings.value.length,
      isPerformanceGood: isPerformanceGood.value
    }
  }

  /**
   * 🚀 重置统计
   */
  function reset() {
    // 重置所有统计
    stats.fps = { current: 0, average: 0, min: Infinity, max: 0, samples: [] }
    stats.latency = { decode: 0, render: 0, total: 0, samples: [] }
    stats.memory = { used: 0, total: 0, percentage: 0 }
    stats.decoder = { queueSize: 0, droppedFrames: 0, errorFrames: 0, totalFrames: 0 }
    stats.network = { bytesReceived: 0, packetsReceived: 0, packetsLost: 0, bandwidth: 0 }
    
    warnings.value = []
    frameCount = 0
    lastFrameTime = 0
    startTime = performance.now()
    lastStatsUpdate = 0
  }

  // 定期更新内存统计
  setInterval(updateMemoryStats, 5000) // 每5秒更新一次

  return {
    stats,
    warnings,
    isPerformanceGood,
    
    // 方法
    recordFrameReceived,
    recordDecodeStart,
    recordDecodeEnd,
    recordRenderEnd,
    updateDecoderStats,
    updateMemoryStats,
    addWarning,
    clearWarnings,
    getPerformanceReport,
    reset
  }
}
