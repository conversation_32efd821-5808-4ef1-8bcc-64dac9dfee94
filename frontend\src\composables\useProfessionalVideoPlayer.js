/**
 * 专业级H264视频播放器
 * 支持高质量解码、抗花屏、智能缓冲
 */

import { ref, nextTick } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  bitrate: 0,
  resolution: '0x0',
  droppedFrames: 0,
  totalFrames: 0,
  bufferHealth: 0
})

// 核心组件
let canvas = null
let canvasContext = null
let decoder = null
let frameBuffer = []
let nalBuffer = new Uint8Array(0)

// 高级配置
const config = {
  maxBufferSize: 10, // 最大缓冲帧数
  targetLatency: 100, // 目标延迟(ms)
  qualityThreshold: 0.8, // 质量阈值
  adaptiveQuality: true, // 自适应质量
  antiAliasing: true, // 抗锯齿
  colorSpace: 'rec2020' // 色彩空间
}

// 性能监控
let performanceMonitor = {
  lastFrameTime: 0,
  frameInterval: [],
  decodeTimeHistory: [],
  qualityScore: 1.0,
  adaptiveMode: false
}

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化专业级视频播放器
 */
export async function initProfessionalVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element is required')
    }

    canvas = canvasElement
    canvasContext = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true, // 减少延迟
      colorSpace: 'rec2020', // 高质量色彩
      willReadFrequently: false
    })

    // 设置高质量渲染
    canvasContext.imageSmoothingEnabled = true
    canvasContext.imageSmoothingQuality = 'high'

    // 设置画布尺寸
    const width = options.width || 1920
    const height = options.height || 1080
    
    canvas.width = width
    canvas.height = height
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    canvas.style.objectFit = 'contain'

    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs VideoDecoder not supported')
    }

    // 创建专业级解码器
    decoder = new VideoDecoder({
      output: handleDecodedFrame,
      error: handleDecoderError
    })

    // 专业级H264解码器配置
    const decoderConfig = {
      codec: 'avc1.64001f', // H.264 High Profile Level 3.1
      codedWidth: width,
      codedHeight: height,
      hardwareAcceleration: 'prefer-hardware',
      optimizeForLatency: true,
      // 高级配置
      description: createAVCDecoderConfig(width, height)
    }

    await decoder.configure(decoderConfig)
    
    isInitialized.value = true
    log('SUCCESS', '🎬 专业级H264播放器初始化成功 (High Profile)')
    
    // 启动性能监控
    startPerformanceMonitoring()
    
    return true
  } catch (error) {
    log('ERROR', `❌ 专业播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 创建AVC解码器配置
 */
function createAVCDecoderConfig(width, height) {
  // 创建AVC配置记录 (AVCDecoderConfigurationRecord)
  const sps = new Uint8Array([
    0x67, 0x64, 0x00, 0x1f, // SPS NAL header + profile_idc + constraints + level_idc
    0xac, 0xd9, 0x40, 0x50, // 更多SPS参数
    0x05, 0xbb, 0x01, 0x6a, 
    0x02, 0x02, 0x02, 0x80
  ])
  
  const pps = new Uint8Array([
    0x68, 0xeb, 0xe3, 0xcb, 0x22, 0xc0
  ])

  // 构建AVCDecoderConfigurationRecord
  const config = new Uint8Array(11 + sps.length + pps.length)
  let offset = 0
  
  config[offset++] = 0x01 // configurationVersion
  config[offset++] = sps[1] // AVCProfileIndication
  config[offset++] = sps[2] // profile_compatibility
  config[offset++] = sps[3] // AVCLevelIndication
  config[offset++] = 0xFF // lengthSizeMinusOne (4 bytes)
  config[offset++] = 0xE1 // numOfSequenceParameterSets
  
  // SPS length and data
  config[offset++] = (sps.length >> 8) & 0xFF
  config[offset++] = sps.length & 0xFF
  config.set(sps, offset)
  offset += sps.length
  
  config[offset++] = 0x01 // numOfPictureParameterSets
  
  // PPS length and data
  config[offset++] = (pps.length >> 8) & 0xFF
  config[offset++] = pps.length & 0xFF
  config.set(pps, offset)
  
  return config
}

/**
 * 启动性能监控
 */
function startPerformanceMonitoring() {
  setInterval(() => {
    updateVideoStats()
    adaptiveQualityControl()
  }, 1000)
}

/**
 * 更新视频统计信息
 */
function updateVideoStats() {
  const now = Date.now()
  
  // 计算FPS
  if (performanceMonitor.frameInterval.length > 0) {
    const avgInterval = performanceMonitor.frameInterval.reduce((a, b) => a + b, 0) / performanceMonitor.frameInterval.length
    videoStats.value.fps = Math.round(1000 / avgInterval)
  }
  
  // 计算缓冲健康度
  videoStats.value.bufferHealth = Math.min(frameBuffer.length / config.maxBufferSize, 1.0)
  
  // 更新分辨率
  if (canvas) {
    videoStats.value.resolution = `${canvas.width}x${canvas.height}`
  }
  
  // 清理历史数据
  if (performanceMonitor.frameInterval.length > 30) {
    performanceMonitor.frameInterval = performanceMonitor.frameInterval.slice(-30)
  }
  if (performanceMonitor.decodeTimeHistory.length > 30) {
    performanceMonitor.decodeTimeHistory = performanceMonitor.decodeTimeHistory.slice(-30)
  }
}

/**
 * 自适应质量控制
 */
function adaptiveQualityControl() {
  if (!config.adaptiveQuality) return
  
  const avgDecodeTime = performanceMonitor.decodeTimeHistory.reduce((a, b) => a + b, 0) / performanceMonitor.decodeTimeHistory.length
  const droppedFrameRate = videoStats.value.droppedFrames / Math.max(videoStats.value.totalFrames, 1)
  
  // 质量评分
  let qualityScore = 1.0
  if (avgDecodeTime > 33) qualityScore -= 0.2 // 解码时间过长
  if (droppedFrameRate > 0.05) qualityScore -= 0.3 // 掉帧率过高
  if (videoStats.value.bufferHealth < 0.3) qualityScore -= 0.2 // 缓冲不足
  
  performanceMonitor.qualityScore = Math.max(qualityScore, 0.1)
  
  // 自适应调整
  if (qualityScore < config.qualityThreshold && !performanceMonitor.adaptiveMode) {
    performanceMonitor.adaptiveMode = true
    config.maxBufferSize = Math.max(config.maxBufferSize - 2, 3)
    log('WARNING', '⚡ 启用自适应模式，降低缓冲以减少延迟')
  } else if (qualityScore > 0.9 && performanceMonitor.adaptiveMode) {
    performanceMonitor.adaptiveMode = false
    config.maxBufferSize = Math.min(config.maxBufferSize + 1, 10)
    log('SUCCESS', '✨ 恢复高质量模式')
  }
}

/**
 * 处理解码后的视频帧
 */
function handleDecodedFrame(frame) {
  try {
    const now = performance.now()
    const decodeStartTime = now
    
    // 帧有效性检查
    if (!frame || frame.codedWidth === 0 || frame.codedHeight === 0) {
      frame?.close()
      videoStats.value.droppedFrames++
      return
    }

    // 动态调整画布尺寸
    if (canvas.width !== frame.codedWidth || canvas.height !== frame.codedHeight) {
      canvas.width = frame.codedWidth
      canvas.height = frame.codedHeight
      log('INFO', `📐 视频尺寸调整: ${frame.codedWidth}x${frame.codedHeight}`)
    }

    // 高质量渲染
    canvasContext.save()
    
    // 清除画布（防止残影）
    canvasContext.fillStyle = '#000000'
    canvasContext.fillRect(0, 0, canvas.width, canvas.height)
    
    // 抗锯齿渲染
    if (config.antiAliasing) {
      canvasContext.imageSmoothingEnabled = true
      canvasContext.imageSmoothingQuality = 'high'
    }
    
    // 渲染帧
    canvasContext.drawImage(frame, 0, 0, canvas.width, canvas.height)
    canvasContext.restore()
    
    // 更新统计信息
    videoStats.value.totalFrames++
    const decodeTime = performance.now() - decodeStartTime
    performanceMonitor.decodeTimeHistory.push(decodeTime)
    
    // 计算帧间隔
    if (performanceMonitor.lastFrameTime > 0) {
      const interval = now - performanceMonitor.lastFrameTime
      performanceMonitor.frameInterval.push(interval)
    }
    performanceMonitor.lastFrameTime = now
    
    isPlaying.value = true
    
    // 释放帧资源
    frame.close()
    
  } catch (error) {
    log('ERROR', `❌ 帧渲染失败: ${error.message}`)
    frame?.close()
    videoStats.value.droppedFrames++
  }
}

/**
 * 处理解码器错误
 */
function handleDecoderError(error) {
  log('ERROR', `❌ 解码器错误: ${error.message}`)
  
  // 智能错误恢复
  if (decoder && decoder.state !== 'closed') {
    try {
      decoder.reset()
      nalBuffer = new Uint8Array(0) // 清空NAL缓冲
      frameBuffer = [] // 清空帧缓冲
      log('INFO', '🔄 解码器已重置，清空缓冲区')
    } catch (resetError) {
      log('ERROR', `❌ 解码器重置失败: ${resetError.message}`)
    }
  }
}

/**
 * 专业级H264数据处理
 */
export async function handleProfessionalH264Data(data) {
  if (!decoder || decoder.state !== 'configured') {
    return false
  }

  try {
    // 将新数据添加到NAL缓冲区
    const newBuffer = new Uint8Array(nalBuffer.length + data.length)
    newBuffer.set(nalBuffer)
    newBuffer.set(data, nalBuffer.length)
    nalBuffer = newBuffer

    // 解析NAL单元
    const nalUnits = parseNALUnits(nalBuffer)
    
    for (const nalUnit of nalUnits) {
      await processNALUnit(nalUnit)
    }

    return true
    
  } catch (error) {
    log('ERROR', `❌ 专业H264处理失败: ${error.message}`)
    return false
  }
}

/**
 * 解析NAL单元
 */
function parseNALUnits(buffer) {
  const nalUnits = []
  let start = 0
  
  for (let i = 0; i < buffer.length - 3; i++) {
    // 查找起始码 0x00000001
    if (buffer[i] === 0x00 && buffer[i+1] === 0x00 && 
        buffer[i+2] === 0x00 && buffer[i+3] === 0x01) {
      
      if (start > 0) {
        // 提取完整的NAL单元
        nalUnits.push(buffer.slice(start, i))
      }
      start = i
    }
  }
  
  // 处理最后一个NAL单元
  if (start > 0 && start < buffer.length) {
    nalUnits.push(buffer.slice(start))
    nalBuffer = new Uint8Array(0) // 清空缓冲区
  } else if (nalUnits.length > 0) {
    nalBuffer = buffer.slice(start) // 保留未完成的数据
  }
  
  return nalUnits
}

/**
 * 处理单个NAL单元
 */
async function processNALUnit(nalUnit) {
  if (nalUnit.length < 5) return
  
  const nalType = nalUnit[4] & 0x1F
  const isKeyFrame = nalType === 5 || nalType === 7 || nalType === 8
  
  // 智能缓冲管理
  if (frameBuffer.length >= config.maxBufferSize) {
    // 如果是关键帧，清空缓冲区
    if (isKeyFrame) {
      frameBuffer = []
      log('INFO', '🔑 关键帧到达，清空缓冲区')
    } else {
      // 丢弃最旧的帧
      frameBuffer.shift()
    }
  }

  try {
    // 创建编码帧
    const chunk = new EncodedVideoChunk({
      type: isKeyFrame ? 'key' : 'delta',
      timestamp: performance.now() * 1000,
      data: nalUnit
    })

    // 检查解码队列
    if (decoder.decodeQueueSize < config.maxBufferSize) {
      decoder.decode(chunk)
    } else {
      log('WARNING', '⚠️ 解码队列满，跳过帧')
      videoStats.value.droppedFrames++
    }
    
  } catch (error) {
    log('ERROR', `❌ NAL单元处理失败: ${error.message}`)
  }
}

/**
 * 获取播放器统计信息
 */
export function getPlayerStats() {
  return {
    ...videoStats.value,
    qualityScore: performanceMonitor.qualityScore,
    adaptiveMode: performanceMonitor.adaptiveMode,
    queueSize: decoder?.decodeQueueSize || 0
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (decoder && decoder.state !== 'closed') {
      decoder.close()
    }
    
    decoder = null
    canvas = null
    canvasContext = null
    frameBuffer = []
    nalBuffer = new Uint8Array(0)
    
    isInitialized.value = false
    isPlaying.value = false
    
    log('INFO', '🧹 专业播放器资源已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}
