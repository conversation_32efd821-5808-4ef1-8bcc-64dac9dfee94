import { ref, reactive } from 'vue'

// 播放器状态
let canvas = null
let ctx = null
let isInitialized = ref(false)
let isPlaying = ref(false)
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let logCallback = null

// 统计信息
const stats = reactive({
  initialized: false,
  playing: false,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  fps: 0
})

// FPS计算
let framesSinceLastFps = 0
let lastFpsTime = Date.now()

// 视频解码器 - 使用Video元素
let videoElement = null
let mediaSource = null
let sourceBuffer = null
let isMediaSourceReady = false

/**
 * 🎥 日志输出
 */
function log(level, message) {
  const timestamp = new Date().toLocaleTimeString()
  const logMessage = `[${timestamp}] [真实视频播放器] ${message}`
  
  if (logCallback) {
    logCallback(level, logMessage)
  }
  
  switch(level) {
    case 'ERROR':
      console.error(logMessage)
      break
    case 'WARNING':
      console.warn(logMessage)
      break
    case 'SUCCESS':
      console.log(`✅ ${logMessage}`)
      break
    case 'INFO':
      console.log(`ℹ️ ${logMessage}`)
      break
    default:
      console.log(logMessage)
  }
}

/**
 * 🎥 初始化真实视频播放器
 */
export async function initRealVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d')

    // 设置画布尺寸
    canvas.width = options.width || 1600
    canvas.height = options.height || 960

    // 创建隐藏的video元素
    videoElement = document.createElement('video')
    videoElement.style.display = 'none'
    videoElement.muted = true
    videoElement.autoplay = true
    videoElement.playsInline = true
    document.body.appendChild(videoElement)

    // 检查MediaSource支持
    if (!window.MediaSource) {
      throw new Error('MediaSource API not supported')
    }

    // 创建MediaSource
    mediaSource = new MediaSource()
    videoElement.src = URL.createObjectURL(mediaSource)

    // 等待MediaSource打开
    await new Promise((resolve, reject) => {
      mediaSource.addEventListener('sourceopen', () => {
        try {
          // 添加H264源缓冲区
          sourceBuffer = mediaSource.addSourceBuffer('video/mp4; codecs="avc1.42E01E"')
          
          sourceBuffer.addEventListener('updateend', () => {
            if (!isMediaSourceReady) {
              isMediaSourceReady = true
              log('SUCCESS', '📺 MediaSource已准备就绪')
            }
          })

          sourceBuffer.addEventListener('error', (e) => {
            log('ERROR', `❌ SourceBuffer错误: ${e}`)
          })

          resolve()
        } catch (error) {
          reject(error)
        }
      })

      mediaSource.addEventListener('error', reject)
      
      setTimeout(() => reject(new Error('MediaSource打开超时')), 5000)
    })

    // 监听video播放事件
    videoElement.addEventListener('loadeddata', () => {
      log('SUCCESS', '📺 视频数据已加载')
    })

    videoElement.addEventListener('canplay', () => {
      log('SUCCESS', '📺 视频可以播放')
      videoElement.play().catch(e => log('ERROR', `播放失败: ${e.message}`))
    })

    videoElement.addEventListener('playing', () => {
      log('SUCCESS', '📺 视频开始播放')
      isPlaying.value = true
      stats.playing = true
      startVideoToCanvasLoop()
    })

    videoElement.addEventListener('error', (e) => {
      log('ERROR', `❌ 视频错误: ${e.message}`)
    })

    // 清除画布
    ctx.fillStyle = '#000000'
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    isInitialized.value = true
    stats.initialized = true
    
    log('SUCCESS', `🎥 真实视频播放器初始化成功 (${canvas.width}x${canvas.height})`)
    return true

  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎥 视频到Canvas的循环渲染
 */
function startVideoToCanvasLoop() {
  function renderFrame() {
    if (videoElement && !videoElement.paused && !videoElement.ended) {
      // 将video内容绘制到canvas
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)
      
      // 计算FPS
      framesSinceLastFps++
      const now = Date.now()
      if (now - lastFpsTime >= 1000) {
        stats.fps = framesSinceLastFps
        framesSinceLastFps = 0
        lastFpsTime = now
      }
    }
    
    if (isPlaying.value) {
      requestAnimationFrame(renderFrame)
    }
  }
  
  renderFrame()
}

/**
 * 🎥 处理H264数据
 */
export async function handleRealVideoH264Data(data) {
  if (!sourceBuffer || !isMediaSourceReady) {
    log('WARNING', '⚠️ MediaSource未准备就绪')
    return false
  }

  try {
    frameCount++
    stats.totalFrames = frameCount

    // 将H264数据包装为MP4格式
    const mp4Data = wrapH264ToMP4(data)
    
    if (mp4Data && !sourceBuffer.updating) {
      sourceBuffer.appendBuffer(mp4Data)
      decodedCount++
      stats.decodedFrames = decodedCount
    }

    // 每100帧显示一次统计
    if (frameCount % 100 === 1) {
      log('INFO', `🎥 真实视频播放器: FPS=${stats.fps} | 总帧=${frameCount} | 解码=${decodedCount}`)
    }

    return true
  } catch (error) {
    log('ERROR', `❌ 处理H264数据失败: ${error.message}`)
    errorCount++
    stats.errorFrames = errorCount
    return false
  }
}

/**
 * 🎥 将H264数据包装为MP4格式（简化版）
 */
function wrapH264ToMP4(h264Data) {
  try {
    // 这是一个简化的MP4包装器
    // 实际应用中需要更复杂的MP4封装
    
    // 创建基本的MP4头部
    const mp4Header = new Uint8Array([
      0x00, 0x00, 0x00, 0x20, // box size
      0x66, 0x74, 0x79, 0x70, // ftyp
      0x69, 0x73, 0x6F, 0x6D, // major brand
      0x00, 0x00, 0x02, 0x00, // minor version
      0x69, 0x73, 0x6F, 0x6D, // compatible brands
      0x69, 0x73, 0x6F, 0x32,
      0x61, 0x76, 0x63, 0x31,
      0x6D, 0x70, 0x34, 0x31
    ])

    // 合并头部和H264数据
    const result = new Uint8Array(mp4Header.length + h264Data.length)
    result.set(mp4Header, 0)
    result.set(h264Data, mp4Header.length)
    
    return result
  } catch (error) {
    log('ERROR', `❌ MP4包装失败: ${error.message}`)
    return null
  }
}

/**
 * 🎥 获取播放器状态
 */
export function getRealVideoStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats,
    totalFrames: frameCount,
    decodedFrames: decodedCount,
    errorFrames: errorCount
  }
}

/**
 * 🎥 设置日志回调
 */
export function setRealVideoLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎥 清理播放器
 */
export function cleanupRealVideoPlayer() {
  try {
    if (videoElement) {
      videoElement.pause()
      videoElement.src = ''
      document.body.removeChild(videoElement)
      videoElement = null
    }

    if (mediaSource) {
      if (mediaSource.readyState === 'open') {
        mediaSource.endOfStream()
      }
      mediaSource = null
    }

    sourceBuffer = null
    isMediaSourceReady = false

    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    canvas = null
    ctx = null
    frameCount = 0
    decodedCount = 0
    errorCount = 0
    
    isInitialized.value = false
    isPlaying.value = false
    stats.initialized = false
    stats.playing = false

    log('SUCCESS', '🎥 真实视频播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 🎥 重置播放器
 */
export async function resetRealVideoPlayer() {
  try {
    cleanupRealVideoPlayer()
    
    if (canvas) {
      await initRealVideoPlayer(canvas, {
        width: canvas.width,
        height: canvas.height
      })
    }
    
    log('SUCCESS', '🔄 真实视频播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}
