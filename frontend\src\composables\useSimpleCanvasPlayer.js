/**
 * 🎨 简单Canvas播放器 - 纯Canvas实现，无WebCodecs
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const stats = ref({
  fps: 0,
  totalFrames: 0,
  errorFrames: 0,
  initialized: false,
  playing: false
})

// 核心组件
let canvas = null
let ctx = null
let frameCount = 0
let errorCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 🎨 初始化Canvas播放器
 */
export async function initSimpleCanvasPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true,
      willReadFrequently: false
    })

    // 设置画布尺寸
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 设置样式
    canvas.style.width = '100%'
    canvas.style.height = '100%'
    canvas.style.objectFit = 'contain'
    canvas.style.background = '#000'

    isInitialized.value = true
    stats.value.initialized = true

    log('SUCCESS', '🎨 Canvas播放器初始化成功')
    return true

  } catch (error) {
    log('ERROR', `❌ Canvas播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎨 处理H264数据 - 显示实时视频信息和数据流状态
 */
export async function handleSimpleH264Data(data) {
  if (!canvas || !ctx) {
    log('WARNING', '⚠️ Canvas未初始化')
    return false
  }

  try {
    frameCount++
    stats.value.totalFrames = frameCount

    // 🎨 创建动态背景
    const time = Date.now() / 1000
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height)
    gradient.addColorStop(0, `hsl(${(time * 30) % 360}, 20%, 10%)`)
    gradient.addColorStop(1, `hsl(${(time * 30 + 180) % 360}, 20%, 5%)`)

    ctx.fillStyle = gradient
    ctx.fillRect(0, 0, canvas.width, canvas.height)

    // 🎨 显示主要信息
    ctx.fillStyle = '#00ff88'
    ctx.font = 'bold 32px Arial'
    ctx.textAlign = 'center'

    const centerX = canvas.width / 2
    const centerY = canvas.height / 2

    ctx.fillText(`🎮 实时视频流接收中`, centerX, centerY - 120)

    // 🎨 显示详细统计
    ctx.font = '24px Arial'
    ctx.fillStyle = '#ffffff'
    ctx.fillText(`📊 总帧数: ${frameCount}`, centerX, centerY - 60)
    ctx.fillText(`📦 当前帧: ${data.length} 字节`, centerX, centerY - 20)
    ctx.fillText(`⚡ FPS: ${stats.value.fps || 0}`, centerX, centerY + 20)
    ctx.fillText(`🕒 ${new Date().toLocaleTimeString()}`, centerX, centerY + 60)

    // 🎨 检查并显示帧类型
    const isKey = isSimpleKeyFrame(data)
    if (isKey) {
      ctx.fillStyle = '#ffff00'
      ctx.font = 'bold 28px Arial'
      ctx.fillText('🔑 关键帧检测', centerX, centerY + 120)
    } else {
      ctx.fillStyle = '#88ff88'
      ctx.font = '20px Arial'
      ctx.fillText('📹 数据帧', centerX, centerY + 120)
    }

    // 🎨 显示数据流活动指示器
    const activity = '●'.repeat((frameCount % 10) + 1) + '○'.repeat(9 - (frameCount % 10))
    ctx.fillStyle = '#00aaff'
    ctx.font = '16px Arial'
    ctx.fillText(`数据流: ${activity}`, centerX, centerY + 160)

    // 🎨 显示NAL单元信息
    const nalInfo = analyzeNALUnits(data)
    if (nalInfo.length > 0) {
      ctx.fillStyle = '#ff8800'
      ctx.font = '18px Arial'
      ctx.fillText(`NAL单元: [${nalInfo.join(', ')}]`, centerX, centerY + 200)
    }

    // 计算FPS
    framesSinceLastFps++
    const now = Date.now()
    if (now - lastFpsTime >= 1000) {
      stats.value.fps = framesSinceLastFps
      framesSinceLastFps = 0
      lastFpsTime = now
    }

    isPlaying.value = true
    stats.value.playing = true

    // 🚀 减少日志输出频率
    if (frameCount % 100 === 1) {
      log('INFO', `🎨 Canvas显示: 帧=${frameCount}, FPS=${stats.value.fps}, 大小=${data.length}字节, NAL=${nalInfo.join(',')}`)
    }

    return true
  } catch (error) {
    log('ERROR', `❌ Canvas显示失败: ${error.message}`)
    errorCount++
    stats.value.errorFrames = errorCount
    return false
  }
}

/**
 * 🎨 处理音频数据 - 占位符
 */
export async function handleSimpleAudioData(data) {
  log('INFO', `🎵 接收音频数据: ${data.length} 字节`)
  return true
}

/**
 * 🎨 获取播放器统计信息
 */
export function getSimpleStats() {
  return {
    ...stats.value,
    frameCount,
    errorCount
  }
}

/**
 * 🎨 设置日志回调
 */
export function setSimpleLogCallback(callback) {
  logCallback = callback
}

/**
 * 🎨 重置播放器
 */
export async function resetSimplePlayer() {
  try {
    if (canvas && ctx) {
      ctx.fillStyle = '#000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }
    
    frameCount = 0
    errorCount = 0
    framesSinceLastFps = 0
    lastFpsTime = Date.now()
    
    stats.value.totalFrames = 0
    stats.value.errorFrames = 0
    stats.value.fps = 0
    
    log('SUCCESS', '🔄 Canvas播放器已重置')
    return true
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
    return false
  }
}

/**
 * 🎨 清理播放器
 */
export function cleanupSimplePlayer() {
  try {
    if (canvas && ctx) {
      ctx.fillStyle = '#000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    canvas = null
    ctx = null
    frameCount = 0
    errorCount = 0
    framesSinceLastFps = 0
    
    isInitialized.value = false
    isPlaying.value = false
    stats.value.initialized = false
    stats.value.playing = false

    log('SUCCESS', '🎨 Canvas播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 🔍 分析NAL单元类型
 */
function analyzeNALUnits(data) {
  const nalTypes = []

  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i + 1] === 0x00 &&
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      const nalType = data[i + 4] & 0x1F
      if (!nalTypes.includes(nalType)) {
        nalTypes.push(nalType)
      }
    }
  }

  return nalTypes.map(type => {
    switch(type) {
      case 1: return 'P帧'
      case 5: return 'IDR'
      case 7: return 'SPS'
      case 8: return 'PPS'
      case 6: return 'SEI'
      default: return `NAL${type}`
    }
  })
}

/**
 * 🎨 检查是否为关键帧
 */
export function isSimpleKeyFrame(data) {
  if (data.length < 4) return false

  // 检查Annex-B起始码
  const hasStartCode = (data[0] === 0x00 && data[1] === 0x00 && data[2] === 0x00 && data[3] === 0x01) ||
                       (data[0] === 0x00 && data[1] === 0x00 && data[2] === 0x01)

  if (!hasStartCode) return false

  // 检查NAL单元类型
  const nalTypes = analyzeNALUnits(data)
  return nalTypes.some(type => type === 'IDR' || type === 'SPS' || type === 'PPS')
}

export default {
  initSimpleCanvasPlayer,
  handleSimpleH264Data,
  handleSimpleAudioData,
  getSimpleStats,
  setSimpleLogCallback,
  resetSimplePlayer,
  cleanupSimplePlayer,
  isSimpleKeyFrame,
  isInitialized,
  isPlaying,
  stats
}
