/**
 * 简单H264播放器 - 不处理SPS/PPS，直接解码
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const stats = ref({
  fps: 0,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  initialized: false,
  playing: false
})

// 核心组件
let canvas = null
let ctx = null
let decoder = null
let isDecoderConfigured = false
let needsKeyFrame = false // 🔥 关键修复：添加缺失的变量

// 统计
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化简单H264播放器
 */
export async function initSimpleH264Player(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 检查WebCodecs支持
    if (!window.VideoDecoder) {
      throw new Error('WebCodecs not supported')
    }

    // 创建解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 渲染帧到Canvas
          ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
          frame.close()
          
          decodedCount++
          stats.value.decodedFrames = decodedCount
          
          // 计算FPS
          framesSinceLastFps++
          const now = Date.now()
          if (now - lastFpsTime >= 1000) {
            stats.value.fps = framesSinceLastFps
            framesSinceLastFps = 0
            lastFpsTime = now
          }
          
          isPlaying.value = true
          stats.value.playing = true
          
        } catch (error) {
          log('ERROR', `❌ 帧渲染失败: ${error.message}`)
          errorCount++
          stats.value.errorFrames = errorCount
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码器错误: ${error.message}`)
        errorCount++
        stats.value.errorFrames = errorCount
        // 🔥 智能错误处理：只在严重错误时重置
        if (!error.message.includes('Decoding error')) {
          isDecoderConfigured = false
        }
      }
    })

    // 简单配置解码器 - 不需要SPS/PPS
    const config = {
      codec: 'avc1.42e01f', // H.264 Baseline Profile
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-hardware',
      optimizeForLatency: true
    }

    await decoder.configure(config)
    isDecoderConfigured = true

    isInitialized.value = true
    stats.value.initialized = true
    log('SUCCESS', '🎬 简单H264播放器已初始化')

    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 处理H264数据 - 增强版本，支持SPS/PPS和更好的关键帧处理
 */
export async function handleSimpleH264Data(data) {
  if (!decoder) {
    log('WARNING', '⚠️ 解码器未创建')
    return false
  }

  // 🔥 关键修复：解码器关闭时立即重新创建
  if (decoder.state === 'closed') {
    log('WARNING', '⚠️ 解码器已关闭，立即重新创建')
    await resetSimplePlayer()
    if (!decoder || decoder.state === 'closed') {
      log('ERROR', '❌ 无法重新创建解码器')
      return false
    }
    log('SUCCESS', '✅ 解码器已重新创建')
  }

  try {
    frameCount++
    stats.value.totalFrames = frameCount

    // 🔥 简单方案：只处理所有帧，不做复杂控制

    // 🔥 增强调试：显示数据格式信息
    if (frameCount % 100 === 1) { // 每100帧显示一次
      const dataHex = Array.from(data.slice(0, 16)).map(b => b.toString(16).padStart(2, '0')).join(' ')
      log('DEBUG', `🔍 帧${frameCount}数据格式: ${dataHex}... (${data.length} bytes)`)
    }

    // 提取和处理SPS/PPS
    const parameterSets = extractParameterSets(data)

    // 🔥 增强调试：显示参数集提取结果
    if (frameCount % 50 === 1) { // 每50帧检查一次
      log('DEBUG', `🔍 参数集状态: SPS=${!!spsData}, PPS=${!!ppsData}, 当前帧SPS=${parameterSets.hasSPS}, PPS=${parameterSets.hasPPS}`)
    }

    // 🔥 关键修复：只在SPS/PPS真正改变时才重新配置
    let needsReconfiguration = false

    if (parameterSets.hasSPS || parameterSets.hasPPS) {
      if (parameterSets.hasSPS) {
        // 🔥 检查SPS是否真的改变了
        const newSpsHex = Array.from(parameterSets.sps).map(b => b.toString(16).padStart(2, '0')).join('')
        const oldSpsHex = spsData ? Array.from(spsData).map(b => b.toString(16).padStart(2, '0')).join('') : ''

        // 🔥 强制调试SPS比较
        if (frameCount % 20 === 1) {
          log('DEBUG', `🔍 SPS比较: 新=${newSpsHex.substring(0, 32)}... 旧=${oldSpsHex.substring(0, 32)}... 相等=${newSpsHex === oldSpsHex}`)
        }

        if (newSpsHex !== oldSpsHex) {
          spsData = parameterSets.sps
          needsReconfiguration = true
          log('SUCCESS', `🔧 SPS已更新 (${parameterSets.sps.length} bytes)`)
          const spsHex = Array.from(parameterSets.sps.slice(0, 12)).map(b => b.toString(16).padStart(2, '0')).join(' ')
          log('DEBUG', `🔍 新SPS内容: ${spsHex}...`)
        } else {
          log('DEBUG', `🔍 SPS未改变，跳过更新 (帧${frameCount})`)
        }
      }

      if (parameterSets.hasPPS) {
        // 🔥 检查PPS是否真的改变了
        const newPpsHex = Array.from(parameterSets.pps).map(b => b.toString(16).padStart(2, '0')).join('')
        const oldPpsHex = ppsData ? Array.from(ppsData).map(b => b.toString(16).padStart(2, '0')).join('') : ''

        // 🔥 强制调试PPS比较
        if (frameCount % 20 === 1) {
          log('DEBUG', `🔍 PPS比较: 新=${newPpsHex} 旧=${oldPpsHex} 相等=${newPpsHex === oldPpsHex}`)
        }

        if (newPpsHex !== oldPpsHex) {
          ppsData = parameterSets.pps
          needsReconfiguration = true
          log('SUCCESS', `🔧 PPS已更新 (${parameterSets.pps.length} bytes)`)
          const ppsHex = Array.from(parameterSets.pps.slice(0, 8)).map(b => b.toString(16).padStart(2, '0')).join(' ')
          log('DEBUG', `🔍 新PPS内容: ${ppsHex}...`)
        } else {
          log('DEBUG', `🔍 PPS未改变，跳过更新 (帧${frameCount})`)
        }
      }

      // 🔥 关键修复：只有在参数集真正改变或首次配置时才重新配置
      if (spsData && ppsData && (needsReconfiguration || !isConfiguredWithSPS)) {
        log('INFO', `🔧 使用SPS/PPS重新配置解码器... (帧${frameCount}, 需要重配置: ${needsReconfiguration}, SPS配置: ${isConfiguredWithSPS})`)
        const success = await reconfigureDecoderWithSPS()
        if (!success) {
          log('WARNING', '⚠️ SPS/PPS配置失败，尝试简单配置')
          await trySimpleConfiguration()
        }
      } else if (spsData && ppsData && frameCount % 100 === 1) {
        log('DEBUG', `🔍 解码器已配置且参数集未改变，跳过重新配置`)
      }
    }

    // 如果解码器未配置，尝试不同的配置策略
    if (!isDecoderConfigured) {
      if (frameCount === 1) {
        log('INFO', '🔧 首帧：尝试简单配置启动解码器')
        await trySimpleConfiguration()
      } else if (frameCount % 30 === 0) {
        log('WARNING', '⚠️ 解码器未配置，等待SPS/PPS')
        // 🔥 增强：更智能的备用配置策略
        if (frameCount > 50) {
          log('INFO', '🔧 等待过久，尝试强制简单配置')
          await trySimpleConfiguration()
        }
      } else if (!spsData && !ppsData && frameCount > 30) {
        // 🔥 新增：如果长时间没有SPS/PPS，尝试从关键帧中强制提取
        const frameInfo = analyzeFrame(data)
        if (frameInfo.isKeyFrame) {
          log('INFO', '🔧 检测到关键帧但无SPS/PPS，尝试强制配置')
          await trySimpleConfiguration()
        }
      }

      if (!isDecoderConfigured) {
        return false
      }
    }

    // 增强的关键帧检测
    const frameInfo = analyzeFrame(data)

    // 🔥 关键修复：将Annex-B格式转换为AVC格式
    let processedData = data
    if (spsData && ppsData && isDecoderConfigured) {
      const avcData = convertAnnexBToAVC(data)
      if (avcData && avcData.length > 0) {
        processedData = avcData
        if (frameCount % 50 === 0 || frameInfo.isKeyFrame) {
          log('DEBUG', `🔄 帧已转换为AVC格式 (${data.length} -> ${avcData.length} bytes)`)
        }
      } else {
        log('WARNING', '⚠️ AVC转换失败，使用原始数据')
      }
    }

    // 创建EncodedVideoChunk
    const chunk = new EncodedVideoChunk({
      type: frameInfo.isKeyFrame ? 'key' : 'delta',
      timestamp: performance.now() * 1000,
      data: processedData
    })

    // 检查解码器状态再次确认
    if (decoder.state !== 'configured') {
      log('WARNING', `⚠️ 解码器状态异常: ${decoder.state}`)
      return false
    }

    // 🔥 关键修复：检查解码器队列长度，防止延迟累积
    if (decoder.decodeQueueSize > 5) {
      log('WARNING', `⚠️ 解码队列过长(${decoder.decodeQueueSize})，跳过当前帧防止延迟`)
      return false
    }

    // 🔥 关键修复：如果需要关键帧，强制重新配置解码器
    if (needsKeyFrame) {
      if (frameInfo.isKeyFrame) {
        log('WARNING', '🔧 需要关键帧，强制重新配置解码器')
        // 强制重新配置解码器
        isDecoderConfigured = false
        isConfiguredWithSPS = false
        needsKeyFrame = false

        // 如果有SPS/PPS，立即重新配置
        if (spsData && ppsData) {
          await reconfigureDecoderWithSPS()
        }
      } else {
        if (frameCount % 50 === 1) {
          log('DEBUG', `⏳ 等待关键帧，跳过P帧 (帧${frameCount})`)
        }
        return false
      }
    }

    // 🔥 新策略：强制硬件解码，解码所有帧
    try {
      decoder.decode(chunk)

      if (frameInfo.isKeyFrame) {
        needsKeyFrame = false
        log('SUCCESS', `🔑 关键帧解码成功 (${frameInfo.nalTypes.join(',')})`)
      } else {
        if (frameCount % 100 === 1) {
          log('DEBUG', `📹 P帧解码提交 (帧${frameCount})`)
        }
      }
      consecutiveDecodeErrors = 0
    } catch (decodeError) {
      log('ERROR', `❌ 解码提交失败: ${decodeError.message}`)
      consecutiveDecodeErrors++
      if (consecutiveDecodeErrors > 5) {
        log('WARNING', '⚠️ 连续解码失败，等待关键帧')
        needsKeyFrame = true
        consecutiveDecodeErrors = 0
      }
    }

    return true

  } catch (error) {
    log('ERROR', `❌ H264解码失败: ${error.message}`)
    errorCount++
    stats.value.errorFrames = errorCount

    // 🔥 关键修复：减少重置，只标记需要关键帧
    if (error.message.includes('key frame is required')) {
      log('WARNING', '⚠️ 需要关键帧，等待下一个IDR帧')
      // 🔥 重要：只标记需要关键帧，不重置配置
      needsKeyFrame = true
      log('DEBUG', '🔧 已标记需要关键帧')
    } else if (error.message.includes('closed codec')) {
      log('INFO', '🔄 检测到解码器关闭，将在下次调用时重置')
      isDecoderConfigured = false
    } else if (error.message.includes('description field')) {
      log('WARNING', '⚠️ 需要AVC描述符，尝试重新配置')
      isDecoderConfigured = false
      if (spsData && ppsData) {
        setTimeout(() => reconfigureDecoderWithSPS(), 100)
      }
    }

    return false
  }
}

// 全局变量用于存储SPS/PPS
let spsData = null
let ppsData = null
let isConfiguredWithSPS = false // 🔥 跟踪是否已用SPS/PPS配置
let consecutiveDecodeErrors = 0 // 🔥 连续解码错误计数器
const MAX_CONSECUTIVE_ERRORS = 3 // 🔥 最大连续错误数

/**
 * 提取SPS/PPS参数集 - 增强版本
 */
function extractParameterSets(data) {
  let hasSPS = false
  let hasPPS = false
  let sps = null
  let pps = null

  // 🔥 强制调试：显示原始数据
  if (Math.random() < 0.05) { // 5%概率
    const dataHex = Array.from(data.slice(0, 32)).map(b => b.toString(16).padStart(2, '0')).join(' ')
    console.log(`[DEBUG] 🔍 检查数据: ${dataHex}... (${data.length} bytes)`)
  }

  // 🔥 修复：更强大的SPS/PPS检测
  let i = 0
  while (i < data.length - 3) {
    let startCodeLength = 0

    // 检查4字节起始码 0x00 0x00 0x00 0x01
    if (i <= data.length - 4 &&
        data[i] === 0x00 && data[i + 1] === 0x00 &&
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      startCodeLength = 4
    }
    // 检查3字节起始码 0x00 0x00 0x01
    else if (data[i] === 0x00 && data[i + 1] === 0x00 && data[i + 2] === 0x01) {
      startCodeLength = 3
    }

    if (startCodeLength > 0) {
      const nalHeaderIndex = i + startCodeLength
      if (nalHeaderIndex >= data.length) {
        i++
        continue
      }

      const nalType = data[nalHeaderIndex] & 0x1F

      // 🔥 修复：更准确的NAL单元结束位置查找
      let end = data.length
      for (let j = nalHeaderIndex + 1; j <= data.length - 3; j++) {
        // 查找下一个起始码
        if ((j < data.length - 3 &&
             data[j] === 0x00 && data[j + 1] === 0x00 &&
             data[j + 2] === 0x00 && data[j + 3] === 0x01) ||
            (data[j] === 0x00 && data[j + 1] === 0x00 && data[j + 2] === 0x01)) {
          end = j
          break
        }
      }

      if (nalType === 7) { // SPS
        hasSPS = true
        sps = data.slice(i, end)
        // 🔥 强制输出SPS发现
        const spsHex = Array.from(sps.slice(0, Math.min(16, sps.length))).map(b => b.toString(16).padStart(2, '0')).join(' ')
        console.log(`[SUCCESS] 🎯 找到SPS: ${sps.length} bytes, 内容: ${spsHex}...`)
      } else if (nalType === 8) { // PPS
        hasPPS = true
        pps = data.slice(i, end)
        // 🔥 强制输出PPS发现
        const ppsHex = Array.from(pps.slice(0, Math.min(16, pps.length))).map(b => b.toString(16).padStart(2, '0')).join(' ')
        console.log(`[SUCCESS] 🎯 找到PPS: ${pps.length} bytes, 内容: ${ppsHex}...`)
      }

      // 跳过当前NAL单元
      i = end
    } else {
      i++
    }
  }

  return { hasSPS, hasPPS, sps, pps }
}

/**
 * 增强的帧分析
 */
function analyzeFrame(data) {
  const nalTypes = []
  let isKeyFrame = false
  let hasIDR = false
  let hasSPS = false
  let hasPPS = false

  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i + 1] === 0x00 &&
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      const nalType = data[i + 4] & 0x1F
      nalTypes.push(nalType)

      if (nalType === 5) { // IDR frame
        hasIDR = true
        isKeyFrame = true
      } else if (nalType === 7) { // SPS
        hasSPS = true
      } else if (nalType === 8) { // PPS
        hasPPS = true
      }
    }
  }

  // 如果有SPS或PPS，也认为是关键帧
  if (hasSPS || hasPPS) {
    isKeyFrame = true
  }

  return {
    isKeyFrame,
    hasIDR,
    hasSPS,
    hasPPS,
    nalTypes,
    size: data.length
  }
}

/**
 * 使用SPS/PPS重新配置解码器
 * 🔥 关键修复：完全重新创建解码器，避免状态问题
 */
async function reconfigureDecoderWithSPS() {
  if (!spsData || !ppsData) {
    return false
  }

  try {
    // 🔥 完全销毁旧解码器
    if (decoder) {
      try {
        if (decoder.state === 'configured') {
          decoder.close()
        }
      } catch (e) {
        // 忽略关闭错误
      }
      decoder = null
    }

    // 等待清理完成
    await new Promise(resolve => setTimeout(resolve, 100))

    // 创建AVC配置描述符
    log('DEBUG', '🔧 正在创建AVC配置描述符...')
    const avcConfig = createAVCDecoderConfig(spsData, ppsData)
    if (!avcConfig) {
      throw new Error('AVC配置创建失败')
    }
    log('DEBUG', '✅ AVC配置描述符创建成功')

    // 🔥 创建全新的解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 🔥 简单直接渲染
          ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
          frame.close()

          decodedCount++
          stats.value.decodedFrames = decodedCount
          consecutiveDecodeErrors = 0

          // 计算FPS
          framesSinceLastFps++
          const now = Date.now()
          if (now - lastFpsTime >= 1000) {
            stats.value.fps = framesSinceLastFps
            framesSinceLastFps = 0
            lastFpsTime = now
          }

          isPlaying.value = true
          stats.value.playing = true
        } catch (error) {
          log('ERROR', `❌ 帧渲染失败: ${error.message}`)
          log('ERROR', `❌ 渲染错误详情: Canvas=${!!canvas}, ctx=${!!ctx}, frame=${!!frame}`)
          log('ERROR', `❌ Canvas尺寸: ${canvas?.width}x${canvas?.height}, 帧尺寸: ${frame?.codedWidth}x${frame?.codedHeight}`)
          frame.close()
          errorCount++
          stats.value.errorFrames = errorCount
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码器错误: ${error.message}`)
        errorCount++
        stats.value.errorFrames = errorCount

        // 🔥 关键修复：所有错误都不重置解码器配置，保持解码器运行
        if (error.message.includes('key frame is required')) {
          needsKeyFrame = true
          consecutiveDecodeErrors = 0
          log('WARNING', '⚠️ 需要关键帧，等待下一个IDR帧')
        } else if (error.message.includes('Decoding error')) {
          consecutiveDecodeErrors++
          log('WARNING', `⚠️ P帧解码错误 (连续${consecutiveDecodeErrors}次)，继续等待下一帧`)

          // 🔥 重要：即使连续错误也不重置，只标记需要关键帧
          if (consecutiveDecodeErrors >= 10) {
            log('WARNING', `⚠️ 连续解码错误过多(${consecutiveDecodeErrors}次)，等待关键帧`)
            needsKeyFrame = true
            consecutiveDecodeErrors = 0
          }
        } else {
          // 🔥 重要：其他错误也不重置配置，保持解码器运行
          consecutiveDecodeErrors++
          log('WARNING', `⚠️ 其他解码器错误 (连续${consecutiveDecodeErrors}次): ${error.message}`)

          if (consecutiveDecodeErrors >= 15) {
            log('WARNING', '⚠️ 严重错误过多，等待关键帧')
            needsKeyFrame = true
            consecutiveDecodeErrors = 0
          }
        }
      }
    })

    // 🔥 GPU加速解码配置 - 支持High Profile
    const config = {
      codec: 'avc1.64001f', // H.264 High Profile Level 3.1 (更好画质)
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-hardware', // 🔥 优先GPU解码
      optimizeForLatency: true,
      description: avcConfig
    }

    log('DEBUG', `🔧 正在配置解码器: ${config.codec}, ${config.codedWidth}x${config.codedHeight}`)
    log('DEBUG', `🔧 AVC描述符长度: ${avcConfig.length} bytes`)

    await decoder.configure(config)
    isDecoderConfigured = true
    isConfiguredWithSPS = true // 🔥 标记已用SPS/PPS配置
    log('SUCCESS', `🔧 解码器已使用SPS/PPS重新配置: ${config.codec} (${config.hardwareAcceleration})`)
    log('DEBUG', `🔧 解码器状态: ${decoder.state}`)

    return true
  } catch (error) {
    log('ERROR', `❌ SPS/PPS配置失败: ${error.message}`)
    isDecoderConfigured = false
    return false
  }
}

/**
 * 创建AVC解码器配置描述符
 */
function createAVCDecoderConfig(spsData, ppsData) {
  try {
    // 提取SPS和PPS的实际数据（去掉起始码）
    const spsNAL = extractNALUnit(spsData)
    const ppsNAL = extractNALUnit(ppsData)

    if (!spsNAL || !ppsNAL) {
      throw new Error('无法提取SPS/PPS NAL单元')
    }

    log('DEBUG', `🔍 SPS NAL长度: ${spsNAL.length}, PPS NAL长度: ${ppsNAL.length}`)

    // 验证SPS和PPS的有效性
    if (spsNAL.length < 4 || ppsNAL.length < 1) {
      throw new Error('SPS/PPS数据太短，可能无效')
    }

    // 创建AVCC格式的配置
    const configSize = 7 + 2 + spsNAL.length + 1 + 2 + ppsNAL.length
    const config = new Uint8Array(configSize)
    let offset = 0

    // AVCC头部
    config[offset++] = 0x01 // configurationVersion
    config[offset++] = spsNAL[1] // AVCProfileIndication (从SPS获取)
    config[offset++] = spsNAL[2] // profile_compatibility
    config[offset++] = spsNAL[3] // AVCLevelIndication
    config[offset++] = 0xFF // lengthSizeMinusOne (4 bytes) | reserved bits

    // SPS部分
    config[offset++] = 0xE1 // numOfSequenceParameterSets (1) | reserved bits
    config[offset++] = (spsNAL.length >> 8) & 0xFF // SPS length high byte
    config[offset++] = spsNAL.length & 0xFF // SPS length low byte
    config.set(spsNAL, offset)
    offset += spsNAL.length

    // PPS部分
    config[offset++] = 0x01 // numOfPictureParameterSets (1)
    config[offset++] = (ppsNAL.length >> 8) & 0xFF // PPS length high byte
    config[offset++] = ppsNAL.length & 0xFF // PPS length low byte
    config.set(ppsNAL, offset)

    // 调试：显示生成的配置
    const configHex = Array.from(config.slice(0, Math.min(16, config.length)))
      .map(b => b.toString(16).padStart(2, '0')).join(' ')
    log('DEBUG', `🔧 AVC配置生成 (${config.length} bytes): ${configHex}...`)

    // 验证配置的基本结构
    if (config[0] !== 0x01) {
      throw new Error('AVC配置版本错误')
    }

    log('SUCCESS', `✅ AVC配置创建成功: Profile=${config[1]}, Level=${config[3]}, SPS=${spsNAL.length}B, PPS=${ppsNAL.length}B`)
    return config
  } catch (error) {
    log('ERROR', `❌ 创建AVC配置失败: ${error.message}`)
    return null
  }
}

/**
 * 提取NAL单元（去掉起始码）
 */
function extractNALUnit(nalData) {
  // 查找起始码 0x00 0x00 0x00 0x01
  for (let i = 0; i < nalData.length - 3; i++) {
    if (nalData[i] === 0x00 && nalData[i + 1] === 0x00 &&
        nalData[i + 2] === 0x00 && nalData[i + 3] === 0x01) {
      return nalData.slice(i + 4)
    }
  }
  return null
}

/**
 * 将Annex-B格式转换为AVC格式
 * 这是解决WebCodecs解码问题的关键
 */
function convertAnnexBToAVC(annexBData) {
  try {
    const nalUnits = []
    let i = 0

    // 🔥 增强：解析所有NAL单元，支持3字节和4字节起始码
    while (i < annexBData.length - 2) {
      let startCodeLength = 0

      // 检查4字节起始码 0x00 0x00 0x00 0x01
      if (i < annexBData.length - 3 &&
          annexBData[i] === 0x00 && annexBData[i + 1] === 0x00 &&
          annexBData[i + 2] === 0x00 && annexBData[i + 3] === 0x01) {
        startCodeLength = 4
      }
      // 检查3字节起始码 0x00 0x00 0x01
      else if (annexBData[i] === 0x00 && annexBData[i + 1] === 0x00 && annexBData[i + 2] === 0x01) {
        startCodeLength = 3
      }

      if (startCodeLength > 0) {
        // 找到NAL单元的结束位置
        let end = annexBData.length
        for (let j = i + startCodeLength; j < annexBData.length - 2; j++) {
          // 检查下一个起始码
          if ((j < annexBData.length - 3 &&
               annexBData[j] === 0x00 && annexBData[j + 1] === 0x00 &&
               annexBData[j + 2] === 0x00 && annexBData[j + 3] === 0x01) ||
              (annexBData[j] === 0x00 && annexBData[j + 1] === 0x00 && annexBData[j + 2] === 0x01)) {
            end = j
            break
          }
        }

        // 提取NAL单元数据（不包括起始码）
        const nalUnit = annexBData.slice(i + startCodeLength, end)
        const nalType = nalUnit[0] & 0x1F

        // 🔥 关键修复：只保留视频数据NAL单元，跳过SPS/PPS
        // SPS(7)和PPS(8)已经在解码器配置中处理
        if (nalType !== 7 && nalType !== 8) {
          // 保留所有非SPS/PPS的NAL单元（IDR帧、P帧等）
          nalUnits.push(nalUnit)
        }

        i = end
      } else {
        i++
      }
    }

    if (nalUnits.length === 0) {
      return null
    }

    // 计算AVC格式数据的总大小
    let totalSize = 0
    for (const unit of nalUnits) {
      totalSize += 4 + unit.length // 4字节长度前缀 + NAL单元数据
    }

    // 创建AVC格式数据
    const avcData = new Uint8Array(totalSize)
    let offset = 0

    for (const unit of nalUnits) {
      // 写入NAL单元长度（大端序）
      avcData[offset++] = (unit.length >> 24) & 0xFF
      avcData[offset++] = (unit.length >> 16) & 0xFF
      avcData[offset++] = (unit.length >> 8) & 0xFF
      avcData[offset++] = unit.length & 0xFF

      // 写入NAL单元数据
      avcData.set(unit, offset)
      offset += unit.length
    }

    return avcData
  } catch (error) {
    log('ERROR', `❌ AVC转换失败: ${error.message}`)
    return null
  }
}

/**
 * 尝试简单配置（使用已有的SPS/PPS）
 */
async function trySimpleConfiguration() {
  if (!decoder) {
    return false
  }

  // 🔥 关键修复：如果有SPS/PPS，使用AVC配置
  if (spsData && ppsData) {
    log('INFO', '🔧 简单配置使用SPS/PPS创建AVC描述符')
    const avcConfig = createAVCDecoderConfig(spsData, ppsData)
    if (avcConfig) {
      return await tryConfigureWithAVC(avcConfig)
    }
  }

/**
 * 使用AVC配置尝试配置解码器
 */
async function tryConfigureWithAVC(avcConfig) {
  const configs = [
    {
      codec: 'avc1.42c01f',
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-hardware',
      optimizeForLatency: true,
      description: avcConfig
    },
    {
      codec: 'avc1.42e01f',
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-software',
      optimizeForLatency: true,
      description: avcConfig
    }
  ]

  for (const config of configs) {
    try {
      await decoder.configure(config)
      isDecoderConfigured = true
      isConfiguredWithSPS = true
      log('SUCCESS', `🔧 AVC配置成功: ${config.codec} (${config.hardwareAcceleration})`)
      return true
    } catch (error) {
      log('WARNING', `⚠️ AVC配置 ${config.codec} 失败: ${error.message}`)
    }
  }
  return false
}

  try {
    // 如果解码器已配置，先关闭
    if (decoder.state === 'configured') {
      decoder.close()
      await new Promise(resolve => setTimeout(resolve, 50))

      // 重新创建解码器
      decoder = new VideoDecoder({
        output: (frame) => {
          try {
            ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
            frame.close()
            decodedCount++
            stats.value.decodedFrames = decodedCount

            // 计算FPS
            framesSinceLastFps++
            const now = Date.now()
            if (now - lastFpsTime >= 1000) {
              stats.value.fps = framesSinceLastFps
              framesSinceLastFps = 0
              lastFpsTime = now
            }

            isPlaying.value = true
            stats.value.playing = true
          } catch (error) {
            log('ERROR', `❌ 帧渲染失败: ${error.message}`)
            errorCount++
            stats.value.errorFrames = errorCount
          }
        },
        error: (error) => {
          log('ERROR', `❌ 解码器错误: ${error.message}`)
          errorCount++
          stats.value.errorFrames = errorCount
          // 🔥 智能错误处理：只在严重错误时重置
          if (!error.message.includes('Decoding error')) {
            isDecoderConfigured = false
          }
        }
      })
    }

    // 🔥 增强：尝试多种简单配置，包括更通用的配置
    const simpleConfigs = [
      {
        codec: 'avc1.42e01f', // H.264 Baseline Profile Level 3.1
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'prefer-software', // 先用软件解码
        optimizeForLatency: true
      },
      {
        codec: 'avc1.42001e', // H.264 Baseline Profile Level 3.0
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'prefer-software',
        optimizeForLatency: true
      },
      {
        codec: 'avc1.42c01e', // H.264 Baseline Profile Level 3.0 (alternative)
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'prefer-software',
        optimizeForLatency: true
      },
      {
        codec: 'avc1.42c01f', // H.264 Baseline Profile Level 3.1 (alternative)
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'prefer-software',
        optimizeForLatency: true
      },
      {
        codec: 'avc1.42001f', // H.264 Baseline Profile Level 3.1 (fallback)
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'no-preference',
        optimizeForLatency: true
      }
    ]

    for (const config of simpleConfigs) {
      try {
        await decoder.configure(config)
        isDecoderConfigured = true
        log('SUCCESS', `🔧 简单配置成功: ${config.codec} (${config.hardwareAcceleration})`)
        return true
      } catch (error) {
        log('WARNING', `⚠️ 配置 ${config.codec} 失败: ${error.message}`)
      }
    }

    log('ERROR', '❌ 所有简单配置都失败')
    return false
  } catch (error) {
    log('ERROR', `❌ 简单配置异常: ${error.message}`)
    return false
  }
}

/**
 * 获取统计信息
 */
export function getSimpleStats() {
  return {
    ...stats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 重置播放器
 */
export async function resetSimplePlayer() {
  try {
    // 安全关闭现有解码器
    if (decoder && decoder.state !== 'closed') {
      try {
        decoder.close()
      } catch (e) {
        log('WARNING', `⚠️ 关闭解码器时出错: ${e.message}`)
      }
    }

    isDecoderConfigured = false

    // 等待一小段时间确保解码器完全关闭
    await new Promise(resolve => setTimeout(resolve, 50))

    // 重新创建解码器
    decoder = new VideoDecoder({
      output: (frame) => {
        try {
          if (ctx && canvas) {
            ctx.drawImage(frame, 0, 0, canvas.width, canvas.height)
            frame.close()
            decodedCount++
            stats.value.decodedFrames = decodedCount
            isPlaying.value = true
            stats.value.playing = true
          } else {
            frame.close()
          }
        } catch (error) {
          log('ERROR', `❌ 帧渲染失败: ${error.message}`)
          frame.close()
        }
      },
      error: (error) => {
        log('ERROR', `❌ 解码器错误: ${error.message}`)
        errorCount++
        stats.value.errorFrames = errorCount
        // 🔥 智能错误处理：只在严重错误时重置
        if (!error.message.includes('Decoding error')) {
          isDecoderConfigured = false
        }
      }
    })

    // 重新配置
    if (canvas) {
      const config = {
        codec: 'avc1.42e01f',
        codedWidth: canvas.width,
        codedHeight: canvas.height,
        hardwareAcceleration: 'prefer-hardware',
        optimizeForLatency: true
      }

      await decoder.configure(config)
      isDecoderConfigured = true

      log('SUCCESS', '🔄 播放器已重置')
    } else {
      log('ERROR', '❌ Canvas不可用，无法重置播放器')
    }

  } catch (error) {
    log('ERROR', `❌ 重置播放器失败: ${error.message}`)
    isDecoderConfigured = false
  }
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (decoder) {
      decoder.close()
      decoder = null
    }
    
    canvas = null
    ctx = null
    isDecoderConfigured = false
    
    isInitialized.value = false
    isPlaying.value = false
    stats.value.initialized = false
    stats.value.playing = false
    
    log('INFO', '🧹 简单播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}
