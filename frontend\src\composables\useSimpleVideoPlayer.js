/**
 * 简单视频播放器 - 使用video元素播放
 * 创建blob URL直接播放H264数据
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const videoStats = ref({
  fps: 0,
  totalFrames: 0,
  bytesReceived: 0
})

// 核心组件
let canvas = null
let ctx = null
let videoElement = null

// 统计
let frameCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0
let totalBytes = 0

// 数据缓冲
let h264Buffer = []

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化简单视频播放器
 */
export async function initSimpleVideoPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true
    })

    // 设置画布
    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 创建隐藏的video元素
    videoElement = document.createElement('video')
    videoElement.style.position = 'absolute'
    videoElement.style.left = '-9999px'
    videoElement.style.top = '-9999px'
    videoElement.style.width = '1px'
    videoElement.style.height = '1px'
    videoElement.muted = true
    videoElement.autoplay = true
    videoElement.playsInline = true
    videoElement.controls = false
    document.body.appendChild(videoElement)

    // 监听video事件
    videoElement.addEventListener('loadeddata', () => {
      log('SUCCESS', '📺 视频数据已加载')
      startVideoRendering()
    })

    videoElement.addEventListener('playing', () => {
      log('SUCCESS', '▶️ 视频开始播放')
      isPlaying.value = true
    })

    videoElement.addEventListener('error', (e) => {
      log('ERROR', `❌ 视频错误: ${e.message || 'Unknown error'}`)
    })

    isInitialized.value = true
    log('SUCCESS', '📺 简单视频播放器已初始化')

    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 开始视频渲染到Canvas
 */
function startVideoRendering() {
  function render() {
    if (videoElement && !videoElement.paused && !videoElement.ended && videoElement.videoWidth > 0) {
      // 将video渲染到canvas
      ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height)

      // 更新FPS统计
      framesSinceLastFps++
      const now = Date.now()
      if (now - lastFpsTime >= 1000) {
        videoStats.value.fps = framesSinceLastFps
        framesSinceLastFps = 0
        lastFpsTime = now
      }
    }

    requestAnimationFrame(render)
  }

  render()
}

/**
 * 处理H264数据 - 累积数据并创建视频
 */
export async function handleSimpleH264Data(data) {
  if (!videoElement) {
    return false
  }

  try {
    totalBytes += data.length
    videoStats.value.bytesReceived = totalBytes

    // 累积H264数据
    h264Buffer.push(new Uint8Array(data))

    // 更新统计
    frameCount++
    videoStats.value.totalFrames = frameCount

    // 每收集一定数量的帧后，尝试创建视频
    if (h264Buffer.length >= 30) { // 每30帧创建一次视频
      await createVideoFromBuffer()
    }

    return true

  } catch (error) {
    log('ERROR', `❌ H264数据处理失败: ${error.message}`)
    return false
  }
}

/**
 * 从缓冲区创建视频
 */
async function createVideoFromBuffer() {
  try {
    if (h264Buffer.length === 0) {
      return
    }

    // 合并所有H264数据
    const totalLength = h264Buffer.reduce((sum, arr) => sum + arr.length, 0)
    const combinedData = new Uint8Array(totalLength)
    let offset = 0

    for (const chunk of h264Buffer) {
      combinedData.set(chunk, offset)
      offset += chunk.length
    }

    // 创建Blob和URL
    const blob = new Blob([combinedData], { type: 'video/mp4' })
    const videoUrl = URL.createObjectURL(blob)

    // 设置video源
    if (videoElement.src) {
      URL.revokeObjectURL(videoElement.src)
    }

    videoElement.src = videoUrl

    // 尝试播放
    try {
      await videoElement.play()
      log('SUCCESS', `🎬 视频片段已创建并播放 (${h264Buffer.length} 帧)`)
    } catch (playError) {
      log('WARNING', `⚠️ 播放失败: ${playError.message}`)
    }

    // 清空缓冲区
    h264Buffer = []

  } catch (error) {
    log('ERROR', `❌ 创建视频失败: ${error.message}`)
  }
}

/**
 * 获取统计信息
 */
export function getSimpleStats() {
  return {
    ...videoStats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (videoElement) {
      videoElement.pause()
      if (videoElement.src) {
        URL.revokeObjectURL(videoElement.src)
      }
      if (videoElement.parentNode) {
        document.body.removeChild(videoElement)
      }
      videoElement = null
    }

    canvas = null
    ctx = null
    h264Buffer = []

    isInitialized.value = false
    isPlaying.value = false

    log('INFO', '🧹 简单播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}

/**
 * 重置播放器
 */
export function resetPlayer() {
  try {
    frameCount = 0
    totalBytes = 0
    h264Buffer = []

    videoStats.value = {
      fps: 0,
      totalFrames: 0,
      bytesReceived: 0
    }

    if (videoElement) {
      videoElement.pause()
      if (videoElement.src) {
        URL.revokeObjectURL(videoElement.src)
        videoElement.src = ''
      }
    }

    if (canvas && ctx) {
      ctx.fillStyle = '#000000'
      ctx.fillRect(0, 0, canvas.width, canvas.height)
    }

    log('SUCCESS', '🔄 简单播放器已重置')
  } catch (error) {
    log('ERROR', `❌ 重置失败: ${error.message}`)
  }
}
