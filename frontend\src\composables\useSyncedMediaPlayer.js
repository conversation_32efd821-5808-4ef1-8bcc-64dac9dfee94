/**
 * 🎬 修复版音视频同步播放器 - 基于现有RTP+UDP架构
 * 架构：视频RTP端口5000 + 音频UDP端口5001 → DataChannel传输
 * 核心功能：精确时间戳同步、帧率适配、音视频缓冲管理
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const stats = ref({
  fps: 0,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  audioFrames: 0,
  syncOffset: 0,
  bufferHealth: 0
})

// 核心组件
let canvas = null
let ctx = null
let videoDecoder = null
let audioDecoder = null
let audioContext = null

// 时间同步管理
let mediaStartTime = null
let videoFrameRate = 30 // 默认30fps，会自动检测
let audioSampleRate = 48000
let baseTimestamp = 0
let lastVideoTimestamp = 0
let lastAudioTimestamp = 0

// 缓冲管理
const videoBuffer = []
const audioBuffer = []
const MAX_BUFFER_SIZE = 30 // 最大缓冲帧数
const MIN_BUFFER_SIZE = 5   // 最小缓冲帧数

// 统计
let frameCount = 0
let audioFrameCount = 0
let droppedFrames = 0
let audioDropped = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 🎬 初始化同步媒体播放器
 */
export async function initSyncedMediaPlayer(canvasElement, options = {}) {
  try {
    if (!canvasElement) {
      throw new Error('Canvas element required')
    }

    canvas = canvasElement
    ctx = canvas.getContext('2d', {
      alpha: false,
      desynchronized: true,
      willReadFrequently: false
    })

    canvas.width = options.width || 1920
    canvas.height = options.height || 1080

    // 检测视频帧率
    videoFrameRate = options.fps || 30
    
    // 初始化音频上下文
    audioContext = new (window.AudioContext || window.webkitAudioContext)()
    audioSampleRate = audioContext.sampleRate

    // 🎬 创建视频解码器
    videoDecoder = new VideoDecoder({
      output: (frame) => {
        try {
          // 添加到视频缓冲区而不是直接渲染
          videoBuffer.push({
            frame: frame,
            timestamp: frame.timestamp,
            presentationTime: frame.timestamp / 1000 // 转换为毫秒
          })

          // 限制缓冲区大小
          if (videoBuffer.length > MAX_BUFFER_SIZE) {
            const dropped = videoBuffer.shift()
            dropped.frame.close()
            droppedFrames++
          }

          // 触发同步渲染
          requestAnimationFrame(syncRender)
          
        } catch (error) {
          log('ERROR', `❌ 视频帧处理失败: ${error.message}`)
          frame.close()
        }
      },
      error: (error) => {
        log('ERROR', `❌ 视频解码错误: ${error.message}`)
        stats.value.errorFrames++
      }
    })

    // 🎵 创建音频解码器
    audioDecoder = new AudioDecoder({
      output: (audioData) => {
        try {
          // 添加到音频缓冲区
          audioBuffer.push({
            data: audioData,
            timestamp: audioData.timestamp,
            presentationTime: audioData.timestamp / 1000
          })

          // 限制音频缓冲区
          if (audioBuffer.length > MAX_BUFFER_SIZE) {
            const dropped = audioBuffer.shift()
            dropped.data.close()
            audioDropped++
          }

          // 触发音频播放
          scheduleAudioPlayback()
          
        } catch (error) {
          log('ERROR', `❌ 音频帧处理失败: ${error.message}`)
          audioData.close()
        }
      },
      error: (error) => {
        log('ERROR', `❌ 音频解码错误: ${error.message}`)
      }
    })

    // 🎮 配置视频解码器 - 支持高分辨率
    const videoConfig = {
      codec: 'avc1.64001f', // H.264 High Profile
      codedWidth: canvas.width,
      codedHeight: canvas.height,
      hardwareAcceleration: 'prefer-hardware',
      optimizeForLatency: false // MP4文件不需要低延迟优化
    }

    await videoDecoder.configure(videoConfig)

    // 🎵 配置音频解码器
    const audioConfig = {
      codec: 'mp4a.40.2', // AAC-LC
      sampleRate: audioSampleRate,
      numberOfChannels: 2
    }

    await audioDecoder.configure(audioConfig)

    isInitialized.value = true
    stats.value.initialized = true
    log('SUCCESS', '🎬 同步媒体播放器已初始化')

    // 启动同步循环
    startSyncLoop()

    return true
  } catch (error) {
    log('ERROR', `❌ 初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 🎬 处理视频数据 - 来自DataChannel的H264数据
 */
export async function handleSyncedVideoData(data, originalTimestamp = null) {
  if (!videoDecoder || videoDecoder.state === 'closed') {
    return false
  }

  try {
    frameCount++
    stats.value.totalFrames = frameCount

    // 🎯 修复时间戳问题 - 使用连续递增的时间戳
    let timestamp
    if (originalTimestamp) {
      timestamp = originalTimestamp
    } else {
      // 根据实际帧率计算，避免固定50ms导致的速度问题
      const frameDuration = 1000000 / videoFrameRate // 微秒 (30fps = 33333微秒)
      timestamp = baseTimestamp + (frameCount * frameDuration)
    }

    // 检测关键帧
    const isKey = isKeyFrame(data)

    const chunk = new EncodedVideoChunk({
      type: isKey ? 'key' : 'delta',
      timestamp: timestamp,
      data: data
    })

    videoDecoder.decode(chunk)
    lastVideoTimestamp = timestamp

    return true
  } catch (error) {
    log('ERROR', `❌ 视频解码失败: ${error.message}`)
    return false
  }
}

/**
 * 🎵 处理音频数据 - 来自DataChannel的AAC数据
 */
export async function handleSyncedAudioData(data, originalTimestamp = null) {
  if (!audioDecoder || audioDecoder.state === 'closed') {
    return false
  }

  try {
    audioFrameCount++
    stats.value.audioFrames = audioFrameCount

    // 🎯 音频时间戳同步 - 修复停顿问题
    let timestamp
    if (originalTimestamp) {
      timestamp = originalTimestamp
    } else {
      // AAC帧持续时间约23.22ms，确保连续性
      const frameDuration = 23220 // 微秒
      timestamp = baseTimestamp + (audioFrameCount * frameDuration)
    }

    const chunk = new EncodedAudioChunk({
      type: 'key', // 音频通常都是关键帧
      timestamp: timestamp,
      data: data
    })

    audioDecoder.decode(chunk)
    lastAudioTimestamp = timestamp

    return true
  } catch (error) {
    log('ERROR', `❌ 音频解码失败: ${error.message}`)
    return false
  }
}

/**
 * 🎬 同步渲染视频帧
 */
function syncRender() {
  if (videoBuffer.length === 0) return

  const currentTime = performance.now()
  if (!mediaStartTime) {
    mediaStartTime = currentTime
  }

  const playbackTime = currentTime - mediaStartTime

  // 查找应该显示的帧
  let frameToRender = null
  let framesToDrop = []

  for (let i = 0; i < videoBuffer.length; i++) {
    const bufferedFrame = videoBuffer[i]
    const frameTime = bufferedFrame.presentationTime

    if (frameTime <= playbackTime + 33) { // 33ms容差
      frameToRender = bufferedFrame
      framesToDrop.push(...videoBuffer.splice(0, i + 1))
      break
    }
  }

  if (frameToRender) {
    // 渲染帧
    ctx.drawImage(frameToRender.frame, 0, 0, canvas.width, canvas.height)
    frameToRender.frame.close()
    
    stats.value.decodedFrames++
    isPlaying.value = true

    // 清理过期帧
    framesToDrop.forEach(f => {
      if (f !== frameToRender) {
        f.frame.close()
      }
    })
  }

  // 更新缓冲健康度
  stats.value.bufferHealth = videoBuffer.length
}

/**
 * 🎵 调度音频播放
 */
function scheduleAudioPlayback() {
  if (audioBuffer.length === 0 || audioContext.state !== 'running') return

  const currentTime = audioContext.currentTime
  const audioFrame = audioBuffer.shift()

  try {
    // 创建音频缓冲区
    const audioBuffer = audioContext.createBuffer(
      audioFrame.data.numberOfChannels,
      audioFrame.data.numberOfFrames,
      audioFrame.data.sampleRate
    )

    // 复制音频数据
    for (let channel = 0; channel < audioFrame.data.numberOfChannels; channel++) {
      const channelData = new Float32Array(audioFrame.data.numberOfFrames)
      audioFrame.data.copyTo(channelData, { planeIndex: channel })
      audioBuffer.copyToChannel(channelData, channel)
    }

    // 创建音频源
    const source = audioContext.createBufferSource()
    source.buffer = audioBuffer
    source.connect(audioContext.destination)

    // 计算播放时间
    const playTime = Math.max(currentTime, currentTime + 0.01)
    source.start(playTime)

    audioFrame.data.close()
  } catch (error) {
    log('ERROR', `❌ 音频播放失败: ${error.message}`)
    audioFrame.data.close()
  }
}

/**
 * 🔄 启动同步循环
 */
function startSyncLoop() {
  setInterval(() => {
    // 更新统计信息
    stats.value.syncOffset = Math.abs(lastVideoTimestamp - lastAudioTimestamp) / 1000
    
    // 每秒更新一次统计
    if (frameCount % 30 === 0) {
      log('INFO', `📊 视频: ${stats.value.decodedFrames}帧 | 音频: ${stats.value.audioFrames}帧 | 缓冲: ${stats.value.bufferHealth} | 同步偏差: ${stats.value.syncOffset.toFixed(1)}ms`)
    }
  }, 1000)
}

/**
 * 🔍 简单关键帧检测
 */
function isKeyFrame(data) {
  if (data.length < 5) return false
  
  for (let i = 0; i < data.length - 4; i++) {
    if (data[i] === 0x00 && data[i + 1] === 0x00 && 
        data[i + 2] === 0x00 && data[i + 3] === 0x01) {
      const nalType = data[i + 4] & 0x1F
      if (nalType === 5 || nalType === 7 || nalType === 8) return true
    }
  }
  return false
}

/**
 * 🎮 设置日志回调
 */
export function setSyncedPlayerLogCallback(callback) {
  logCallback = callback
}

/**
 * 📊 获取播放器状态
 */
export function getSyncedPlayerStats() {
  return {
    isInitialized: isInitialized.value,
    isPlaying: isPlaying.value,
    stats: stats.value
  }
}

/**
 * 🧹 清理播放器
 */
export function cleanupSyncedPlayer() {
  // 清理视频缓冲区
  videoBuffer.forEach(item => item.frame.close())
  videoBuffer.length = 0

  // 清理音频缓冲区
  audioBuffer.forEach(item => item.data.close())
  audioBuffer.length = 0

  if (videoDecoder && videoDecoder.state !== 'closed') {
    videoDecoder.close()
  }
  
  if (audioDecoder && audioDecoder.state !== 'closed') {
    audioDecoder.close()
  }

  if (audioContext && audioContext.state !== 'closed') {
    audioContext.close()
  }
  
  isInitialized.value = false
  isPlaying.value = false
  
  log('INFO', '🧹 同步媒体播放器已清理')
}
