/**
 * 使用Video元素的H264播放器 - 避免WebCodecs复杂配置
 */

import { ref } from 'vue'

// 播放器状态
const isInitialized = ref(false)
const isPlaying = ref(false)
const stats = ref({
  fps: 0,
  totalFrames: 0,
  decodedFrames: 0,
  errorFrames: 0,
  initialized: false,
  playing: false
})

// 核心组件
let videoElement = null
let mediaSource = null
let sourceBuffer = null
let isSourceBufferReady = false

// 统计
let frameCount = 0
let decodedCount = 0
let errorCount = 0
let lastFpsTime = Date.now()
let framesSinceLastFps = 0

let logCallback = null

function log(level, message) {
  if (logCallback) {
    logCallback(level, message)
  }
}

/**
 * 初始化Video元素播放器
 */
export async function initVideoElementPlayer(videoEl, options = {}) {
  try {
    if (!videoEl) {
      throw new Error('Video element required')
    }

    videoElement = videoEl
    
    // 设置video元素属性
    videoElement.muted = true
    videoElement.autoplay = true
    videoElement.playsInline = true
    videoElement.controls = false

    // 检查MediaSource支持
    if (!window.MediaSource) {
      throw new Error('MediaSource not supported')
    }

    // 创建MediaSource
    mediaSource = new MediaSource()
    
    mediaSource.addEventListener('sourceopen', () => {
      try {
        // 创建SourceBuffer for H.264
        sourceBuffer = mediaSource.addSourceBuffer('video/mp4; codecs="avc1.42E01E"')
        
        sourceBuffer.addEventListener('updateend', () => {
          if (!sourceBuffer.updating && mediaSource.readyState === 'open') {
            isSourceBufferReady = true
            if (!isPlaying.value && videoElement.paused) {
              videoElement.play().catch(e => {
                log('WARNING', `⚠️ 自动播放失败: ${e.message}`)
              })
            }
          }
        })

        sourceBuffer.addEventListener('error', (e) => {
          log('ERROR', `❌ SourceBuffer错误: ${e}`)
          errorCount++
          stats.value.errorFrames = errorCount
        })

        isSourceBufferReady = true
        log('SUCCESS', '📺 SourceBuffer已创建')

      } catch (error) {
        log('ERROR', `❌ SourceBuffer创建失败: ${error.message}`)
      }
    })

    mediaSource.addEventListener('sourceended', () => {
      log('INFO', '📺 MediaSource已结束')
    })

    mediaSource.addEventListener('error', (e) => {
      log('ERROR', `❌ MediaSource错误: ${e}`)
    })

    // 设置video源
    videoElement.src = URL.createObjectURL(mediaSource)

    // 监听video事件
    videoElement.addEventListener('loadstart', () => {
      log('INFO', '📺 开始加载视频')
    })

    videoElement.addEventListener('canplay', () => {
      log('SUCCESS', '📺 视频可以播放')
      isPlaying.value = true
      stats.value.playing = true
    })

    videoElement.addEventListener('playing', () => {
      log('SUCCESS', '📺 视频开始播放')
      isPlaying.value = true
      stats.value.playing = true
    })

    videoElement.addEventListener('error', (e) => {
      log('ERROR', `❌ 视频错误: ${e.target.error?.message || 'Unknown error'}`)
      errorCount++
      stats.value.errorFrames = errorCount
    })

    isInitialized.value = true
    stats.value.initialized = true
    log('SUCCESS', '🎬 Video元素播放器已初始化')

    return true
  } catch (error) {
    log('ERROR', `❌ 播放器初始化失败: ${error.message}`)
    return false
  }
}

/**
 * 处理H264数据 - 转换为MP4格式
 */
export async function handleVideoElementH264Data(data) {
  if (!sourceBuffer || !isSourceBufferReady || sourceBuffer.updating) {
    return false
  }

  try {
    frameCount++
    stats.value.totalFrames = frameCount

    // 简单的MP4包装 - 这里需要将H264 NAL units包装成MP4格式
    // 为了简化，我们先尝试直接添加数据
    const mp4Data = wrapH264ToMP4(data)
    
    if (mp4Data && mp4Data.length > 0) {
      sourceBuffer.appendBuffer(mp4Data)
      
      decodedCount++
      stats.value.decodedFrames = decodedCount
      
      // 计算FPS
      framesSinceLastFps++
      const now = Date.now()
      if (now - lastFpsTime >= 1000) {
        stats.value.fps = framesSinceLastFps
        framesSinceLastFps = 0
        lastFpsTime = now
      }
      
      return true
    }

    return false

  } catch (error) {
    log('ERROR', `❌ H264数据处理失败: ${error.message}`)
    errorCount++
    stats.value.errorFrames = errorCount
    return false
  }
}

/**
 * 简单的H264到MP4包装 - 这是一个简化版本
 */
function wrapH264ToMP4(h264Data) {
  try {
    // 这里应该实现完整的MP4包装
    // 为了简化，我们返回原始数据，但这可能不会工作
    // 实际应用中需要使用专门的MP4包装库
    
    // 检查是否是有效的H264 NAL unit
    if (h264Data.length >= 4 && 
        h264Data[0] === 0x00 && h264Data[1] === 0x00 && 
        h264Data[2] === 0x00 && h264Data[3] === 0x01) {
      
      // 简单返回数据，实际需要MP4包装
      return h264Data
    }
    
    return null
  } catch (error) {
    log('ERROR', `❌ MP4包装失败: ${error.message}`)
    return null
  }
}

/**
 * 获取统计信息
 */
export function getVideoElementStats() {
  return {
    ...stats.value,
    initialized: isInitialized.value,
    playing: isPlaying.value
  }
}

/**
 * 设置日志回调
 */
export function setLogCallback(callback) {
  logCallback = callback
}

/**
 * 重置播放器
 */
export async function resetVideoElementPlayer() {
  try {
    if (sourceBuffer && !sourceBuffer.updating) {
      sourceBuffer.abort()
    }
    
    if (mediaSource && mediaSource.readyState === 'open') {
      mediaSource.endOfStream()
    }
    
    if (videoElement) {
      videoElement.pause()
      videoElement.currentTime = 0
    }
    
    isSourceBufferReady = false
    isPlaying.value = false
    stats.value.playing = false
    
    log('SUCCESS', '🔄 Video播放器已重置')
    
  } catch (error) {
    log('ERROR', `❌ 重置播放器失败: ${error.message}`)
  }
}

/**
 * 清理资源
 */
export function cleanup() {
  try {
    if (sourceBuffer && !sourceBuffer.updating) {
      sourceBuffer.abort()
    }
    
    if (mediaSource && mediaSource.readyState === 'open') {
      mediaSource.endOfStream()
    }
    
    if (videoElement) {
      videoElement.pause()
      videoElement.src = ''
      videoElement = null
    }
    
    mediaSource = null
    sourceBuffer = null
    isSourceBufferReady = false
    
    isInitialized.value = false
    isPlaying.value = false
    stats.value.initialized = false
    stats.value.playing = false
    
    log('INFO', '🧹 Video播放器已清理')
  } catch (error) {
    log('ERROR', `❌ 清理失败: ${error.message}`)
  }
}
