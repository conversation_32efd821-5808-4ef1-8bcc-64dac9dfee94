import { ref, onUnmounted } from 'vue'

export function useWebCodecsDecoder() {
  const decoder = ref(null)
  const canvas = ref(null)
  const ctx = ref(null)
  const isSupported = ref(false)
  const isInitialized = ref(false)
  const frameCount = ref(0)
  const lastFrameTime = ref(0)
  const fps = ref(0)
  const isConfigured = ref(false)
  const spsData = ref(null)
  const ppsData = ref(null)

  // 立即检查支持状态
  const checkSupportAndSet = () => {
    const hasVideoDecoder = 'VideoDecoder' in window
    const hasVideoFrame = 'VideoFrame' in window
    const hasEncodedVideoChunk = 'EncodedVideoChunk' in window

    console.log('[WebCodecs] 详细检测结果:')
    console.log('  - VideoDecoder:', hasVideoDecoder)
    console.log('  - VideoFrame:', hasVideoFrame)
    console.log('  - EncodedVideoChunk:', hasEncodedVideoChunk)
    console.log('  - User Agent:', navigator.userAgent)
    console.log('  - 是否HTTPS:', location.protocol === 'https:')
    console.log('  - 是否localhost:', location.hostname === 'localhost' || location.hostname === '127.0.0.1')

    const supported = hasVideoDecoder && hasVideoFrame && hasEncodedVideoChunk
    isSupported.value = supported
    console.log('[WebCodecs] 最终支持状态:', supported)
    return supported
  }

  // 初始化时立即检查支持
  checkSupportAndSet()

  // 检查WebCodecs支持
  const checkSupport = () => {
    // 重新检查并更新支持状态
    return checkSupportAndSet()
  }

  // 解析H.264 NAL单元
  const parseH264NAL = (data) => {
    const nalUnits = []
    let i = 0

    while (i < data.length - 4) {
      // 查找Annex-B起始码 (0x00 0x00 0x00 0x01)
      if (data[i] === 0x00 && data[i + 1] === 0x00 && data[i + 2] === 0x00 && data[i + 3] === 0x01) {
        const start = i + 4
        let end = data.length

        // 查找下一个起始码
        for (let j = start + 1; j < data.length - 4; j++) {
          if (data[j] === 0x00 && data[j + 1] === 0x00 && data[j + 2] === 0x00 && data[j + 3] === 0x01) {
            end = j
            break
          }
        }

        if (start < end) {
          const nalType = data[start] & 0x1F
          nalUnits.push({
            type: nalType,
            data: data.slice(start, end)
          })
        }

        i = end
      } else {
        i++
      }
    }

    return nalUnits
  }

  // 配置解码器（当收到SPS/PPS时）
  const configureDecoder = (sps, pps) => {
    if (!decoder.value || isConfigured.value) return false

    try {
      // 创建description字段（包含SPS和PPS）
      const description = new Uint8Array(sps.length + pps.length + 8)
      let offset = 0

      // 添加SPS
      description[offset++] = 0x00
      description[offset++] = 0x00
      description[offset++] = 0x00
      description[offset++] = 0x01
      description.set(sps, offset)
      offset += sps.length

      // 添加PPS
      description[offset++] = 0x00
      description[offset++] = 0x00
      description[offset++] = 0x00
      description[offset++] = 0x01
      description.set(pps, offset)

      const config = {
        codec: 'avc1.42e01e', // H.264 Baseline Profile Level 3.0 (更兼容)
        codedWidth: 1920,
        codedHeight: 1080,
        hardwareAcceleration: 'prefer-hardware',
        description: description
      }

      console.log('[WebCodecs] 🔧 开始配置解码器，config:', config)
      decoder.value.configure(config)
      isConfigured.value = true
      spsData.value = sps
      ppsData.value = pps

      console.log('[WebCodecs] ✅ 解码器配置成功，SPS长度:', sps.length, 'PPS长度:', pps.length)
      console.log('[WebCodecs] 配置后解码器状态:', decoder.value.state)
      return true

    } catch (error) {
      console.error('[WebCodecs] 解码器配置失败:', error)
      return false
    }
  }

  // 初始化解码器
  const initDecoder = (canvasElement) => {
    console.log('[WebCodecs] 开始初始化解码器...')
    const supportResult = checkSupport()
    console.log('[WebCodecs] 支持检查结果:', supportResult)

    if (!supportResult) {
      const errorMsg = `浏览器不支持WebCodecs API。请确保：
1. 使用 Chrome 94+ 或 Edge 94+
2. 在 chrome://flags/ 中启用 "Experimental Web Platform features"
3. 网站运行在 HTTPS 或 localhost 环境
4. 当前环境: ${location.protocol}//${location.hostname}`
      console.error('[WebCodecs] 支持检查失败:', errorMsg)
      throw new Error(errorMsg)
    }

    canvas.value = canvasElement
    ctx.value = canvas.value.getContext('2d')

    try {
      decoder.value = new VideoDecoder({
        output: handleDecodedFrame,
        error: handleDecodeError
      })

      // 先不配置解码器，等收到SPS/PPS后再配置
      isInitialized.value = true

      console.log('[WebCodecs] 解码器创建成功，等待SPS/PPS配置')
      return true

    } catch (error) {
      console.error('[WebCodecs] 解码器创建失败:', error)
      throw error
    }
  }

  // 处理解码后的帧
  const handleDecodedFrame = (frame) => {
    try {
      if (!ctx.value || !canvas.value) {
        frame.close()
        return
      }

      // 调整canvas尺寸
      if (canvas.value.width !== frame.displayWidth || canvas.value.height !== frame.displayHeight) {
        canvas.value.width = frame.displayWidth
        canvas.value.height = frame.displayHeight
        console.log(`[WebCodecs] Canvas尺寸调整为: ${frame.displayWidth}x${frame.displayHeight}`)
      }

      // 渲染帧到canvas
      ctx.value.drawImage(frame, 0, 0)
      
      // 更新统计信息
      frameCount.value++
      const now = performance.now()
      if (lastFrameTime.value > 0) {
        const timeDiff = now - lastFrameTime.value
        fps.value = Math.round(1000 / timeDiff)
      }
      lastFrameTime.value = now

      // 释放帧资源
      frame.close()

      // 定期输出统计
      if (frameCount.value % 30 === 0) {
        console.log(`[WebCodecs] 已解码 ${frameCount.value} 帧, FPS: ${fps.value}`)
      }

    } catch (error) {
      console.error('[WebCodecs] 帧渲染错误:', error)
      frame.close()
    }
  }

  // 处理解码错误
  const handleDecodeError = (error) => {
    console.error('[WebCodecs] 解码错误:', error)
  }

  // 解码H.264数据
  const decodeH264Data = (data, isKeyFrame = false) => {
    if (!decoder.value || !isInitialized.value) {
      console.warn('[WebCodecs] 解码器未初始化')
      return false
    }

    try {
      // 解析NAL单元
      const nalUnits = parseH264NAL(data)
      console.log('[WebCodecs] 解析到NAL单元:', nalUnits.map(nal => `类型${nal.type}`).join(', '))

      // 检查是否包含SPS/PPS
      let sps = null, pps = null
      let hasVideoData = false

      for (const nal of nalUnits) {
        if (nal.type === 7) { // SPS
          sps = nal.data
        } else if (nal.type === 8) { // PPS
          pps = nal.data
        } else if (nal.type === 5 || nal.type === 1) { // IDR帧或P帧
          hasVideoData = true
        }
      }

      // 如果有SPS/PPS，先配置解码器
      if (sps && pps && !isConfigured.value) {
        console.log('[WebCodecs] 🔧 检测到SPS/PPS，尝试配置解码器')
        console.log('[WebCodecs] SPS长度:', sps.length, 'PPS长度:', pps.length)
        console.log('[WebCodecs] 当前解码器状态:', decoder.value.state)
        console.log('[WebCodecs] isConfigured:', isConfigured.value)

        if (!configureDecoder(sps, pps)) {
          console.error('[WebCodecs] ❌ 解码器配置失败')
          return false
        } else {
          console.log('[WebCodecs] ✅ 解码器配置成功')
        }
      } else {
        if (!sps) console.log('[WebCodecs] 🔍 缺少SPS')
        if (!pps) console.log('[WebCodecs] 🔍 缺少PPS')
        if (isConfigured.value) console.log('[WebCodecs] 🔍 解码器已配置')
      }

      // 如果解码器未配置，跳过视频数据
      if (!isConfigured.value) {
        console.log('[WebCodecs] 等待SPS/PPS配置解码器')
        return false
      }

      // 检查解码器状态
      if (decoder.value.state !== 'configured') {
        console.warn('[WebCodecs] 解码器状态异常:', decoder.value.state)

        // 如果解码器关闭，尝试重新初始化
        if (decoder.value.state === 'closed') {
          console.log('[WebCodecs] 尝试重新初始化解码器')
          if (initDecoder(canvas.value)) {
            console.log('[WebCodecs] 解码器重新初始化成功')
            // 重新检查状态
            if (decoder.value.state !== 'configured') {
              console.warn('[WebCodecs] 重新初始化后状态仍异常:', decoder.value.state)
              return false
            }
          } else {
            console.error('[WebCodecs] 解码器重新初始化失败')
            return false
          }
        } else {
          return false
        }
      }

      // 只解码视频帧数据
      if (hasVideoData) {
        const chunk = new EncodedVideoChunk({
          type: isKeyFrame ? 'key' : 'delta',
          timestamp: performance.now() * 1000, // 微秒
          data: data
        })

        decoder.value.decode(chunk)
        return true
      }

      return false

    } catch (error) {
      console.error('[WebCodecs] 解码失败:', error)
      return false
    }
  }

  // 重置解码器
  const resetDecoder = () => {
    if (decoder.value) {
      try {
        decoder.value.reset()
        frameCount.value = 0
        fps.value = 0
        isConfigured.value = false
        spsData.value = null
        ppsData.value = null
        console.log('[WebCodecs] 解码器已重置')
      } catch (error) {
        console.error('[WebCodecs] 重置失败:', error)
      }
    }
  }

  // 清理资源
  const cleanup = () => {
    if (decoder.value) {
      try {
        decoder.value.close()
        console.log('[WebCodecs] 解码器已关闭')
      } catch (error) {
        console.error('[WebCodecs] 关闭失败:', error)
      }
      decoder.value = null
    }
    isInitialized.value = false
    frameCount.value = 0
    fps.value = 0
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    isSupported,
    isInitialized,
    frameCount,
    fps,
    
    // 方法
    checkSupport,
    initDecoder,
    decodeH264Data,
    resetDecoder,
    cleanup
  }
}
