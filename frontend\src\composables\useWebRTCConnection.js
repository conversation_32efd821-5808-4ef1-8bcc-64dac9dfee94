import { ref, computed } from 'vue'

/**
 * WebRTC连接管理 Composable
 * 负责WebRTC连接的建立、维护和断开
 */
export function useWebRTCConnection() {
  // 连接状态
  const serverUrl = ref('http://localhost:8080')
  const peerConnection = ref(null)
  const dataChannel = ref(null)
  const videoChannel = ref(null)
  const audioChannel = ref(null)
  const connected = ref(false)
  const connecting = ref(false)
  const connectionStatus = ref('未连接')
  const isReceivingVideo = ref(false)
  const isReceivingAudio = ref(false)

  // 事件回调
  const onDataChannelMessage = ref(null)
  const onVideoChannelMessage = ref(null)
  const onAudioChannelMessage = ref(null)
  const onDataChannelOpen = ref(null)
  const onVideoChannelOpen = ref(null)
  const onAudioChannelOpen = ref(null)
  const onDataChannelClose = ref(null)
  const onVideoChannelClose = ref(null)
  const onAudioChannelClose = ref(null)

  // 计算属性
  const isConnected = computed(() => connected.value)
  const isConnecting = computed(() => connecting.value)

  /**
   * 设置PeerConnection事件监听器
   */
  function setupPeerConnectionEvents() {
    if (!peerConnection.value) return

    // ICE连接状态变化
    peerConnection.value.oniceconnectionstatechange = () => {
      const state = peerConnection.value.iceConnectionState
      console.log(`[WebRTC] ICE连接状态: ${state}`)
      
      switch (state) {
        case 'connected':
        case 'completed':
          connected.value = true
          connecting.value = false
          connectionStatus.value = '已连接'
          console.log('[WebRTC] ✅ 连接建立成功')
          break
        case 'disconnected':
          connectionStatus.value = '连接中断'
          break
        case 'failed':
          connected.value = false
          connecting.value = false
          connectionStatus.value = '连接失败'
          console.log('[WebRTC] ❌ 连接失败')
          break
        case 'closed':
          connected.value = false
          connecting.value = false
          connectionStatus.value = '连接已关闭'
          break
      }
    }

    // 连接状态变化
    peerConnection.value.onconnectionstatechange = () => {
      const state = peerConnection.value.connectionState
      console.log(`[WebRTC] 连接状态: ${state}`)

      if (state === 'connected') {
        connected.value = true
        connecting.value = false
        connectionStatus.value = '已连接'

        // 检查接收器
        const receivers = peerConnection.value.getReceivers()
        console.log(`[WebRTC] 📡 当前接收器数量: ${receivers.length}`)
        receivers.forEach((receiver, index) => {
          console.log(`[WebRTC] 接收器 ${index}: ${receiver.track ? receiver.track.kind : 'null'}`)
        })

      } else if (state === 'disconnected' || state === 'failed') {
        connected.value = false
        connecting.value = false
        connectionStatus.value = '已断开'
      }
    }

    // ICE候选者
    peerConnection.value.onicecandidate = (event) => {
      if (event.candidate) {
        console.log('[WebRTC] 发现ICE候选者')
      } else {
        console.log('[WebRTC] ICE候选者收集完成')
      }
    }

    // Track事件处理 - REMOVED (使用DataChannel代替)
  }

  /**
   * 设置DataChannel事件监听器
   */
  function setupDataChannelEvents() {
    if (!dataChannel.value) return

    dataChannel.value.onopen = () => {
      console.log('[DataChannel] ✅ Input DataChannel已打开')
      if (onDataChannelOpen.value) {
        onDataChannelOpen.value()
      }
    }

    dataChannel.value.onclose = () => {
      console.log('[DataChannel] ❌ Input DataChannel已关闭')
      if (onDataChannelClose.value) {
        onDataChannelClose.value()
      }
    }

    dataChannel.value.onerror = (error) => {
      console.error('[DataChannel] Input DataChannel错误:', error)
    }

    dataChannel.value.onmessage = (event) => {
      if (onDataChannelMessage.value) {
        onDataChannelMessage.value(event.data)
      }
    }
  }

  /**
   * 设置视频DataChannel事件监听器
   */
  function setupVideoChannelEvents() {
    if (!videoChannel.value) return

    videoChannel.value.onopen = () => {
      console.log('[VideoChannel] ✅ Video DataChannel已打开')
      isReceivingVideo.value = true
      if (onVideoChannelOpen.value) {
        onVideoChannelOpen.value()
      }
    }

    videoChannel.value.onclose = () => {
      console.log('[VideoChannel] ❌ Video DataChannel已关闭')
      isReceivingVideo.value = false
      if (onVideoChannelClose.value) {
        onVideoChannelClose.value()
      }
    }

    videoChannel.value.onerror = (error) => {
      console.error('[VideoChannel] Video DataChannel错误:', error)
    }

    videoChannel.value.onmessage = (event) => {
      if (onVideoChannelMessage.value) {
        onVideoChannelMessage.value(event.data)
      }
    }
  }

  /**
   * 设置音频DataChannel事件监听器
   */
  function setupAudioChannelEvents() {
    if (!audioChannel.value) return

    audioChannel.value.onopen = () => {
      console.log('[AudioChannel] ✅ Audio DataChannel已打开')
      isReceivingAudio.value = true
      if (onAudioChannelOpen.value) {
        onAudioChannelOpen.value()
      }
    }

    audioChannel.value.onclose = () => {
      console.log('[AudioChannel] ❌ Audio DataChannel已关闭')
      isReceivingAudio.value = false
      if (onAudioChannelClose.value) {
        onAudioChannelClose.value()
      }
    }

    audioChannel.value.onerror = (error) => {
      console.error('[AudioChannel] Audio DataChannel错误:', error)
    }

    audioChannel.value.onmessage = (event) => {
      if (onAudioChannelMessage.value) {
        onAudioChannelMessage.value(event.data)
      }
    }
  }

  /**
   * 连接到服务器
   */
  async function connectToServer() {
    // 强制断开现有连接
    if (connecting.value || connected.value) {
      console.log('[WebRTC] 🔄 强制断开现有连接')
      disconnectFromServer()
      await new Promise(resolve => setTimeout(resolve, 100)) // 等待断开完成
    }

    connecting.value = true
    connectionStatus.value = '连接中...'
    console.log('[WebRTC] 🚀 开始全新连接到服务器:', serverUrl.value)

    try {
      // 创建PeerConnection
      peerConnection.value = new RTCPeerConnection({
        iceServers: [
          { urls: 'stun:stun.l.google.com:19302' }
        ]
      })

      // 设置事件监听器
      setupPeerConnectionEvents()

      // 监听来自服务器的DataChannel
      peerConnection.value.ondatachannel = (event) => {
        console.log(`[WebRTC] 接收到DataChannel: ${event.channel.label}`)

        if (event.channel.label === 'input') {
          dataChannel.value = event.channel
          setupDataChannelEvents()
        } else if (event.channel.label === 'video') {
          videoChannel.value = event.channel
          setupVideoChannelEvents()
        } else if (event.channel.label === 'audio') {
          audioChannel.value = event.channel
          setupAudioChannelEvents()
        } else {
          console.log(`[WebRTC] 未知DataChannel: ${event.channel.label}`)
        }
      }

      // 从服务器获取offer
      const offerResponse = await fetch(`${serverUrl.value}/offer`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!offerResponse.ok) {
        throw new Error(`HTTP ${offerResponse.status}: ${offerResponse.statusText}`)
      }

      const offerData = await offerResponse.json()
      console.log('[WebRTC] 📋 收到服务器Offer SDP:')
      console.log(offerData.sdp)

      // 检查SDP中的媒体轨道
      const sdpLines = offerData.sdp.split('\n')
      const videoLines = sdpLines.filter(line => line.includes('m=video'))
      const audioLines = sdpLines.filter(line => line.includes('m=audio'))
      console.log(`[WebRTC] SDP包含视频轨道: ${videoLines.length > 0}`)
      console.log(`[WebRTC] SDP包含音频轨道: ${audioLines.length > 0}`)

      // 设置远程描述
      await peerConnection.value.setRemoteDescription({
        type: 'offer',
        sdp: offerData.sdp
      })
      console.log('[WebRTC] ✅ 远程描述设置成功')

      // 创建并发送answer
      const answer = await peerConnection.value.createAnswer()
      await peerConnection.value.setLocalDescription(answer)

      // 发送answer到服务器
      const answerResponse = await fetch(`${serverUrl.value}/answer`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'answer',
          sdp: answer.sdp
        })
      })

      if (!answerResponse.ok) {
        throw new Error(`发送answer失败: HTTP ${answerResponse.status}`)
      }

      return { success: true }

    } catch (error) {
      connecting.value = false
      connectionStatus.value = '连接失败'

      if (peerConnection.value) {
        peerConnection.value.close()
        peerConnection.value = null
      }

      return { success: false, error: error.message }
    }
  }

  /**
   * 断开连接
   */
  function disconnectFromServer() {
    if (peerConnection.value) {
      peerConnection.value.close()
      peerConnection.value = null
    }

    if (dataChannel.value) {
      dataChannel.value.close()
      dataChannel.value = null
    }

    connected.value = false
    connecting.value = false
    connectionStatus.value = '未连接'
    isReceivingVideo.value = false
    isReceivingAudio.value = false

    return { success: true }
  }

  /**
   * 测试连接
   */
  async function testConnection() {
    if (!connected.value) {
      return { success: false, error: '未连接' }
    }

    try {
      const response = await fetch(`${serverUrl.value}/stats`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      })

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }

      const stats = await response.json()
      return { success: true, data: stats }

    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  /**
   * 发送DataChannel消息
   */
  function sendDataChannelMessage(message) {
    if (!dataChannel.value || dataChannel.value.readyState !== 'open') {
      return { success: false, error: 'DataChannel未打开' }
    }

    try {
      dataChannel.value.send(message)
      return { success: true }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }

  return {
    // 状态
    serverUrl,
    connected: isConnected,
    connecting: isConnecting,
    connectionStatus,
    isReceivingVideo,
    isReceivingAudio,
    peerConnection,
    dataChannel,
    videoChannel,
    audioChannel,

    // 事件回调设置
    onDataChannelMessage,
    onVideoChannelMessage,
    onAudioChannelMessage,
    onDataChannelOpen,
    onVideoChannelOpen,
    onAudioChannelOpen,
    onDataChannelClose,
    onVideoChannelClose,
    onAudioChannelClose,

    // 方法
    connectToServer,
    disconnectFromServer,
    testConnection,
    sendDataChannelMessage
  }
}
