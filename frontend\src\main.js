import { createApp } from 'vue'
import './style.css'
import './styles/modern-theme.css'
import App from './App.vue'

import ElementPlus from "element-plus"
import "element-plus/dist/index.css"
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入路由和状态管理
import router from './router'
import pinia from './stores'
import { useSettingsStore } from './stores/settings'

const app = createApp(App)

// 使用插件
app.use(ElementPlus)
app.use(router)
app.use(pinia)

// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 初始化设置
const settingsStore = useSettingsStore()
settingsStore.loadSettings()

app.mount("#app")