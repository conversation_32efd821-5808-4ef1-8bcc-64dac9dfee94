import { createRouter, createWebHistory } from 'vue-router'

// 导入页面组件
import MainView from '../views/MainView.vue'
import StatusView from '../views/StatusView.vue'
import LogsView from '../views/LogsView.vue'

const routes = [
  {
    path: '/',
    name: 'main',
    component: MainView,
    meta: {
      title: '远程控制',
      icon: 'VideoCamera',
      description: 'WebRTC H264 远程控制主界面'
    }
  },
  {
    path: '/status',
    name: 'status',
    component: StatusView,
    meta: {
      title: '系统统计',
      icon: 'DataAnalysis',
      description: '查看系统性能统计和运行指标'
    }
  },
  {
    path: '/logs',
    name: 'logs',
    component: LogsView,
    meta: {
      title: '系统日志',
      icon: 'Document',
      description: '查看详细的系统日志和调试信息'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    redirect: '/'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = `${to.meta.title} - WebRTC远程控制系统`
  }
  next()
})

export default router
