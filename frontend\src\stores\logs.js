import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useLogsStore = defineStore('logs', () => {
  // 日志数据
  const logs = ref([])
  const maxLogs = ref(1000)
  
  // 过滤设置
  const filterLevel = ref('all') // all, debug, info, warn, error
  const filterKeyword = ref('')
  const filterCategory = ref('all') // all, webrtc, video, audio, network, input
  
  // 日志级别映射
  const logLevels = {
    DEBUG: 0,
    INFO: 1,
    SUCCESS: 2,
    WARNING: 3,
    ERROR: 4,
    INPUT: 5
  }
  
  // 计算属性
  const filteredLogs = computed(() => {
    return logs.value.filter(log => {
      // 级别过滤
      if (filterLevel.value !== 'all') {
        const currentLevel = logLevels[log.type] || 1
        const filterLevelNum = logLevels[filterLevel.value.toUpperCase()] || 1
        if (currentLevel < filterLevelNum) return false
      }
      
      // 关键词过滤
      if (filterKeyword.value) {
        const keyword = filterKeyword.value.toLowerCase()
        if (!log.message.toLowerCase().includes(keyword)) return false
      }
      
      // 分类过滤
      if (filterCategory.value !== 'all') {
        if (!log.category || log.category !== filterCategory.value) return false
      }
      
      return true
    })
  })
  
  const logStats = computed(() => {
    const stats = {
      total: logs.value.length,
      debug: 0,
      info: 0,
      success: 0,
      warning: 0,
      error: 0,
      input: 0
    }
    
    logs.value.forEach(log => {
      const type = log.type.toLowerCase()
      if (stats.hasOwnProperty(type)) {
        stats[type]++
      }
    })
    
    return stats
  })
  
  // Actions
  function addLog(type, message, category = null) {
    const log = {
      id: Date.now() + Math.random(),
      type: type.toUpperCase(),
      message,
      category,
      time: new Date().toLocaleTimeString(),
      timestamp: Date.now(),
      fullTime: new Date().toISOString()
    }
    
    logs.value.unshift(log)
    
    // 限制日志数量
    if (logs.value.length > maxLogs.value) {
      logs.value = logs.value.slice(0, maxLogs.value)
    }
    
    // 控制台输出
    console.log(`[${type}] ${message}`)
  }
  
  function clearLogs() {
    logs.value = []
    addLog('INFO', '日志已清空')
  }
  
  function exportLogs() {
    const exportData = {
      exportTime: new Date().toISOString(),
      totalLogs: logs.value.length,
      logs: logs.value
    }
    
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    
    const link = document.createElement('a')
    link.href = URL.createObjectURL(dataBlob)
    link.download = `webrtc-logs-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    link.click()
    
    addLog('INFO', '日志已导出')
  }
  
  function importLogs(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = JSON.parse(e.target.result)
          if (data.logs && Array.isArray(data.logs)) {
            logs.value = [...data.logs, ...logs.value]
            if (logs.value.length > maxLogs.value) {
              logs.value = logs.value.slice(0, maxLogs.value)
            }
            addLog('INFO', `已导入 ${data.logs.length} 条日志`)
            resolve(data.logs.length)
          } else {
            throw new Error('无效的日志文件格式')
          }
        } catch (error) {
          addLog('ERROR', `导入日志失败: ${error.message}`)
          reject(error)
        }
      }
      reader.onerror = () => {
        const error = new Error('读取文件失败')
        addLog('ERROR', error.message)
        reject(error)
      }
      reader.readAsText(file)
    })
  }
  
  function setFilter(level, keyword, category) {
    filterLevel.value = level || 'all'
    filterKeyword.value = keyword || ''
    filterCategory.value = category || 'all'
  }
  
  function getLogsByTimeRange(startTime, endTime) {
    return logs.value.filter(log => {
      return log.timestamp >= startTime && log.timestamp <= endTime
    })
  }
  
  function getLogsByType(type) {
    return logs.value.filter(log => log.type === type.toUpperCase())
  }
  
  function searchLogs(query) {
    const lowerQuery = query.toLowerCase()
    return logs.value.filter(log => 
      log.message.toLowerCase().includes(lowerQuery) ||
      log.type.toLowerCase().includes(lowerQuery) ||
      (log.category && log.category.toLowerCase().includes(lowerQuery))
    )
  }
  
  return {
    // 状态
    logs,
    maxLogs,
    filterLevel,
    filterKeyword,
    filterCategory,
    
    // 计算属性
    filteredLogs,
    logStats,
    
    // 方法
    addLog,
    clearLogs,
    exportLogs,
    importLogs,
    setFilter,
    getLogsByTimeRange,
    getLogsByType,
    searchLogs
  }
})
