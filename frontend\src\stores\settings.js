import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useSettingsStore = defineStore('settings', () => {
  // 显示设置
  const theme = ref('light') // light, dark, auto
  const language = ref('zh-CN') // zh-CN, en-US
  const showVideoInfo = ref(true)
  const showPerformanceStats = ref(false)
  const logLevel = ref('info') // debug, info, warn, error
  
  // 视频设置
  const videoSettings = ref({
    preferredResolution: 'auto', // auto, 1920x1080, 1280x720, 640x360
    preferredFrameRate: 30,
    hardwareAcceleration: 'prefer-hardware', // prefer-hardware, prefer-software, no-preference
    lowLatencyMode: true
  })
  
  // 音频设置
  const audioSettings = ref({
    sampleRate: 44100, // 44100, 48000, 22050
    channels: 2, // 1, 2
    bitrate: 128, // 64, 128, 192, 256
    enableNoiseSuppression: true,
    enableEchoCancellation: true
  })
  
  // 网络设置
  const networkSettings = ref({
    connectionTimeout: 10000, // ms
    reconnectAttempts: 3,
    reconnectDelay: 2000, // ms
    packetLossThreshold: 5, // %
    latencyThreshold: 200 // ms
  })
  
  // 控制设置
  const controlSettings = ref({
    mouseSensitivity: 1.0,
    keyboardRepeatDelay: 500, // ms
    keyboardRepeatRate: 50, // ms
    enableMouseAcceleration: false,
    enableKeyboardShortcuts: true
  })
  
  // 安全设置
  const securitySettings = ref({
    enableSSL: false,
    requireAuthentication: false,
    sessionTimeout: 3600000, // ms (1 hour)
    maxConcurrentSessions: 1
  })
  
  // 开发者设置
  const developerSettings = ref({
    enableDebugMode: false,
    showWebRTCStats: false,
    enableVerboseLogging: false,
    saveLogsToFile: false
  })
  
  // Actions
  function updateVideoSettings(settings) {
    videoSettings.value = { ...videoSettings.value, ...settings }
    saveSettings()
  }
  
  function updateAudioSettings(settings) {
    audioSettings.value = { ...audioSettings.value, ...settings }
    saveSettings()
  }
  
  function updateNetworkSettings(settings) {
    networkSettings.value = { ...networkSettings.value, ...settings }
    saveSettings()
  }
  
  function updateControlSettings(settings) {
    controlSettings.value = { ...controlSettings.value, ...settings }
    saveSettings()
  }
  
  function updateSecuritySettings(settings) {
    securitySettings.value = { ...securitySettings.value, ...settings }
    saveSettings()
  }
  
  function updateDeveloperSettings(settings) {
    developerSettings.value = { ...developerSettings.value, ...settings }
    saveSettings()
  }
  
  function setTheme(newTheme) {
    theme.value = newTheme
    applyTheme()
    saveSettings()
  }
  
  function setLanguage(newLanguage) {
    language.value = newLanguage
    saveSettings()
  }
  
  function applyTheme() {
    const root = document.documentElement
    if (theme.value === 'dark') {
      root.classList.add('dark')
    } else if (theme.value === 'light') {
      root.classList.remove('dark')
    } else {
      // auto mode
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      if (prefersDark) {
        root.classList.add('dark')
      } else {
        root.classList.remove('dark')
      }
    }
  }
  
  function saveSettings() {
    const settings = {
      theme: theme.value,
      language: language.value,
      showVideoInfo: showVideoInfo.value,
      showPerformanceStats: showPerformanceStats.value,
      logLevel: logLevel.value,
      videoSettings: videoSettings.value,
      audioSettings: audioSettings.value,
      networkSettings: networkSettings.value,
      controlSettings: controlSettings.value,
      securitySettings: securitySettings.value,
      developerSettings: developerSettings.value
    }
    localStorage.setItem('webrtc-settings', JSON.stringify(settings))
  }
  
  function loadSettings() {
    try {
      const saved = localStorage.getItem('webrtc-settings')
      if (saved) {
        const settings = JSON.parse(saved)
        
        theme.value = settings.theme || 'light'
        language.value = settings.language || 'zh-CN'
        showVideoInfo.value = settings.showVideoInfo ?? true
        showPerformanceStats.value = settings.showPerformanceStats ?? false
        logLevel.value = settings.logLevel || 'info'
        
        if (settings.videoSettings) {
          videoSettings.value = { ...videoSettings.value, ...settings.videoSettings }
        }
        if (settings.audioSettings) {
          audioSettings.value = { ...audioSettings.value, ...settings.audioSettings }
        }
        if (settings.networkSettings) {
          networkSettings.value = { ...networkSettings.value, ...settings.networkSettings }
        }
        if (settings.controlSettings) {
          controlSettings.value = { ...controlSettings.value, ...settings.controlSettings }
        }
        if (settings.securitySettings) {
          securitySettings.value = { ...securitySettings.value, ...settings.securitySettings }
        }
        if (settings.developerSettings) {
          developerSettings.value = { ...developerSettings.value, ...settings.developerSettings }
        }
        
        applyTheme()
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }
  
  function resetToDefaults() {
    theme.value = 'light'
    language.value = 'zh-CN'
    showVideoInfo.value = true
    showPerformanceStats.value = false
    logLevel.value = 'info'
    
    videoSettings.value = {
      preferredResolution: 'auto',
      preferredFrameRate: 30,
      hardwareAcceleration: 'prefer-hardware',
      lowLatencyMode: true
    }
    
    audioSettings.value = {
      sampleRate: 44100,
      channels: 2,
      bitrate: 128,
      enableNoiseSuppression: true,
      enableEchoCancellation: true
    }
    
    networkSettings.value = {
      connectionTimeout: 10000,
      reconnectAttempts: 3,
      reconnectDelay: 2000,
      packetLossThreshold: 5,
      latencyThreshold: 200
    }
    
    controlSettings.value = {
      mouseSensitivity: 1.0,
      keyboardRepeatDelay: 500,
      keyboardRepeatRate: 50,
      enableMouseAcceleration: false,
      enableKeyboardShortcuts: true
    }
    
    securitySettings.value = {
      enableSSL: false,
      requireAuthentication: false,
      sessionTimeout: 3600000,
      maxConcurrentSessions: 1
    }
    
    developerSettings.value = {
      enableDebugMode: false,
      showWebRTCStats: false,
      enableVerboseLogging: false,
      saveLogsToFile: false
    }
    
    applyTheme()
    saveSettings()
  }
  
  return {
    // 状态
    theme,
    language,
    showVideoInfo,
    showPerformanceStats,
    logLevel,
    videoSettings,
    audioSettings,
    networkSettings,
    controlSettings,
    securitySettings,
    developerSettings,
    
    // 方法
    updateVideoSettings,
    updateAudioSettings,
    updateNetworkSettings,
    updateControlSettings,
    updateSecuritySettings,
    updateDeveloperSettings,
    setTheme,
    setLanguage,
    saveSettings,
    loadSettings,
    resetToDefaults
  }
})
