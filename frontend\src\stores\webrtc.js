import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export const useWebRTCStore = defineStore('webrtc', () => {
  // 连接状态
  const connected = ref(false)
  const connecting = ref(false)
  const connectionStatus = ref('未连接')
  const dataChannelOpen = ref(false)
  
  // 服务器配置
  const serverUrl = ref('http://localhost:8080')
  const serverHistory = ref([
    'http://localhost:8080',
    'http://*************:8080'
  ])
  
  // 视频状态
  const videoReceived = ref(false)
  const videoStats = ref({
    resolution: '等待视频流...',
    fps: 0,
    bitrate: 0,
    framesReceived: 0,
    framesDropped: 0,
    chunksReceived: 0,  // 新增：分片统计
    largeFramesReassembled: 0  // 新增：重组的大帧数量
  })
  
  // 音频状态
  const audioStats = ref({
    sampleRate: 0,
    channels: 0,
    framesReceived: 0,
    framesPlayed: 0,
    decodeErrors: 0
  })
  
  // 控制设置
  const mouseControlEnabled = ref(true)
  const keyboardControlEnabled = ref(true)
  const audioEnabled = ref(true)
  
  // 性能统计
  const performanceStats = ref({
    cpuUsage: 0,
    memoryUsage: 0,
    networkLatency: 0,
    packetLoss: 0
  })
  
  // 计算属性
  const isReady = computed(() => connected.value && dataChannelOpen.value)
  const connectionQuality = computed(() => {
    if (!connected.value) return 'disconnected'
    if (performanceStats.value.packetLoss > 5) return 'poor'
    if (performanceStats.value.networkLatency > 200) return 'fair'
    return 'good'
  })
  
  // Actions
  function updateConnectionStatus(status) {
    connectionStatus.value = status
  }
  
  function updateVideoStats(stats) {
    videoStats.value = { ...videoStats.value, ...stats }
  }
  
  function updateAudioStats(stats) {
    audioStats.value = { ...audioStats.value, ...stats }
  }
  
  function updatePerformanceStats(stats) {
    performanceStats.value = { ...performanceStats.value, ...stats }
  }
  
  function addServerToHistory(url) {
    if (!serverHistory.value.includes(url)) {
      serverHistory.value.unshift(url)
      if (serverHistory.value.length > 10) {
        serverHistory.value = serverHistory.value.slice(0, 10)
      }
    }
  }
  
  function resetStats() {
    videoStats.value = {
      resolution: '等待视频流...',
      fps: 0,
      bitrate: 0,
      framesReceived: 0,
      framesDropped: 0,
      chunksReceived: 0,
      largeFramesReassembled: 0
    }
    audioStats.value = {
      sampleRate: 0,
      channels: 0,
      framesReceived: 0,
      framesPlayed: 0,
      decodeErrors: 0
    }
    performanceStats.value = {
      cpuUsage: 0,
      memoryUsage: 0,
      networkLatency: 0,
      packetLoss: 0
    }
  }
  
  return {
    // 状态
    connected,
    connecting,
    connectionStatus,
    dataChannelOpen,
    serverUrl,
    serverHistory,
    videoReceived,
    videoStats,
    audioStats,
    mouseControlEnabled,
    keyboardControlEnabled,
    audioEnabled,
    performanceStats,
    
    // 计算属性
    isReady,
    connectionQuality,
    
    // 方法
    updateConnectionStatus,
    updateVideoStats,
    updateAudioStats,
    updatePerformanceStats,
    addServerToHistory,
    resetStats
  }
})
