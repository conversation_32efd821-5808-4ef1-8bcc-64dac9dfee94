/* Modern Clean Theme for WebRTC Remote Control */

/* 全局变量 */
:root {
  /* 主色调 */
  --primary-gradient: linear-gradient(135deg, #6366f1 0%, #a855f7 100%);
  --secondary-gradient: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
  --danger-gradient: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  --warning-gradient: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  --info-gradient: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
  
  /* 背景渐变 */
  --bg-gradient: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --bg-gradient-dark: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  
  /* 卡片样式 */
  --card-bg: rgba(255, 255, 255, 0.95);
  --card-bg-dark: rgba(15, 23, 42, 0.95);
  --card-border: rgba(226, 232, 240, 0.8);
  --card-border-dark: rgba(51, 65, 85, 0.8);
  --card-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  --card-shadow-hover: 0 20px 40px rgba(0, 0, 0, 0.15);
  
  /* 文字渐变 */
  --text-gradient: linear-gradient(135deg, #1e293b 0%, #475569 100%);
  --text-gradient-dark: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
  
  /* 圆角 */
  --radius-sm: 8px;
  --radius-md: 12px;
  --radius-lg: 16px;
  --radius-xl: 20px;
  
  /* 间距 */
  --spacing-xs: 8px;
  --spacing-sm: 12px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 动画 */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  
  /* 字体 */
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
}

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: var(--bg-gradient);
  margin: 0;
  padding: 0;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Element Plus 组件样式覆盖 */
.el-button {
  border-radius: var(--radius-md) !important;
  font-weight: 600 !important;
  transition: var(--transition-fast) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.el-button:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.el-button--primary {
  background: var(--primary-gradient) !important;
  border: none !important;
}

.el-button--success {
  background: var(--secondary-gradient) !important;
  border: none !important;
}

.el-button--danger {
  background: var(--danger-gradient) !important;
  border: none !important;
}

.el-button--warning {
  background: var(--warning-gradient) !important;
  border: none !important;
}

.el-button--info {
  background: var(--info-gradient) !important;
  border: none !important;
}

.el-card {
  background: var(--card-bg) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid var(--card-border) !important;
  border-radius: var(--radius-xl) !important;
  box-shadow: var(--card-shadow) !important;
  transition: var(--transition-normal) !important;
}

.el-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--card-shadow-hover) !important;
}

.el-card__header {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(168, 85, 247, 0.05) 100%) !important;
  border-bottom: 1px solid var(--card-border) !important;
  padding: var(--spacing-lg) !important;
}

.el-card__body {
  padding: var(--spacing-xl) !important;
}

.el-input__wrapper {
  border-radius: var(--radius-md) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: var(--transition-fast) !important;
}

.el-input__wrapper:hover {
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

.el-tag {
  border-radius: var(--radius-md) !important;
  font-weight: 600 !important;
  padding: var(--spacing-xs) var(--spacing-md) !important;
  border: none !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.el-menu-item {
  margin-bottom: var(--spacing-xs) !important;
  border-radius: var(--radius-md) !important;
  transition: var(--transition-fast) !important;
  font-weight: 500 !important;
}

.el-menu-item:hover {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%) !important;
  transform: translateX(4px) !important;
}

.el-menu-item.is-active {
  background: var(--primary-gradient) !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3) !important;
}

.el-switch.is-checked .el-switch__core {
  background: var(--primary-gradient) !important;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(241, 245, 249, 0.8);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 4px;
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}

/* 深色主题 */
.dark {
  background: var(--bg-gradient-dark) !important;
}

.dark .el-card {
  background: var(--card-bg-dark) !important;
  border-color: var(--card-border-dark) !important;
}

.dark .el-card__header {
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(168, 85, 247, 0.1) 100%) !important;
  border-bottom-color: var(--card-border-dark) !important;
}

.dark ::-webkit-scrollbar-track {
  background: rgba(30, 41, 59, 0.8);
}

.dark ::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #475569 0%, #334155 100%);
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #64748b 0%, #475569 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  :root {
    --spacing-xs: 6px;
    --spacing-sm: 8px;
    --spacing-md: 12px;
    --spacing-lg: 16px;
    --spacing-xl: 20px;
  }
  
  .el-card__header {
    padding: var(--spacing-md) !important;
  }
  
  .el-card__body {
    padding: var(--spacing-lg) !important;
  }
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.4s ease-out;
}

/* 工具类 */
.glass-effect {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(226, 232, 240, 0.8) !important;
}

.dark .glass-effect {
  background: rgba(15, 23, 42, 0.95) !important;
  border-color: rgba(51, 65, 85, 0.8) !important;
}

.gradient-text {
  background: var(--text-gradient);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dark .gradient-text {
  background: var(--text-gradient-dark);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.hover-lift {
  transition: var(--transition-fast);
}

.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: var(--card-shadow-hover);
}
