<template>
  <div class="about-view">
    <div class="about-container">
      <!-- 系统信息卡片 -->
      <el-card class="info-card">
        <template #header>
          <div class="card-header">
            <el-icon class="header-icon"><VideoCamera /></el-icon>
            <div class="header-text">
              <h2>WebRTC H264 远程控制系统</h2>
              <p>基于WebRTC技术的高性能远程桌面控制解决方案</p>
            </div>
          </div>
        </template>
        
        <div class="info-content">
          <div class="info-section">
            <h3>版本信息</h3>
            <div class="info-grid">
              <div class="info-item">
                <span class="info-label">系统版本</span>
                <span class="info-value">v1.0.0</span>
              </div>
              <div class="info-item">
                <span class="info-label">构建时间</span>
                <span class="info-value">{{ buildTime }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">Git提交</span>
                <span class="info-value">{{ gitCommit }}</span>
              </div>
              <div class="info-item">
                <span class="info-label">环境</span>
                <span class="info-value">{{ environment }}</span>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h3>技术栈</h3>
            <div class="tech-stack">
              <div class="tech-category">
                <h4>前端技术</h4>
                <div class="tech-tags">
                  <el-tag>Vue 3</el-tag>
                  <el-tag>Element Plus</el-tag>
                  <el-tag>Vue Router</el-tag>
                  <el-tag>Pinia</el-tag>
                  <el-tag>WebRTC</el-tag>
                  <el-tag>WebCodecs</el-tag>
                </div>
              </div>
              <div class="tech-category">
                <h4>后端技术</h4>
                <div class="tech-tags">
                  <el-tag type="success">C++</el-tag>
                  <el-tag type="success">WebRTC Native</el-tag>
                  <el-tag type="success">FFmpeg</el-tag>
                  <el-tag type="success">HTTP Server</el-tag>
                  <el-tag type="success">UDP Socket</el-tag>
                </div>
              </div>
              <div class="tech-category">
                <h4>编解码</h4>
                <div class="tech-tags">
                  <el-tag type="warning">H.264</el-tag>
                  <el-tag type="warning">AAC</el-tag>
                  <el-tag type="warning">ADTS</el-tag>
                  <el-tag type="warning">RTP</el-tag>
                </div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h3>功能特性</h3>
            <div class="features-grid">
              <div class="feature-item">
                <el-icon class="feature-icon"><VideoCamera /></el-icon>
                <div class="feature-content">
                  <h4>高质量视频传输</h4>
                  <p>支持H.264硬件编解码，提供流畅的视频体验</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Microphone /></el-icon>
                <div class="feature-content">
                  <h4>实时音频传输</h4>
                  <p>AAC音频编码，低延迟高保真音频传输</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Mouse /></el-icon>
                <div class="feature-content">
                  <h4>精确控制</h4>
                  <p>支持鼠标键盘控制，响应迅速，操作精准</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Monitor /></el-icon>
                <div class="feature-content">
                  <h4>全屏支持</h4>
                  <p>支持全屏模式，提供沉浸式远程控制体验</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><DataAnalysis /></el-icon>
                <div class="feature-content">
                  <h4>性能监控</h4>
                  <p>实时监控系统性能，提供详细的统计信息</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Setting /></el-icon>
                <div class="feature-content">
                  <h4>灵活配置</h4>
                  <p>丰富的配置选项，满足不同场景需求</p>
                </div>
              </div>
            </div>
          </div>

          <div class="info-section">
            <h3>系统要求</h3>
            <div class="requirements">
              <div class="requirement-category">
                <h4>浏览器支持</h4>
                <ul>
                  <li>Chrome 94+ (推荐)</li>
                  <li>Edge 94+</li>
                  <li>Firefox 90+ (部分功能)</li>
                  <li>Safari 15+ (部分功能)</li>
                </ul>
              </div>
              <div class="requirement-category">
                <h4>必需功能</h4>
                <ul>
                  <li>WebRTC支持</li>
                  <li>WebCodecs API</li>
                  <li>Fullscreen API</li>
                  <li>Canvas 2D Context</li>
                </ul>
              </div>
              <div class="requirement-category">
                <h4>网络要求</h4>
                <ul>
                  <li>带宽: 最低2Mbps</li>
                  <li>延迟: 建议<100ms</li>
                  <li>丢包率: <1%</li>
                  <li>UDP端口: 5000, 5001</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 开发团队信息 -->
      <el-card class="team-card">
        <template #header>
          <div class="card-header">
            <el-icon><User /></el-icon>
            <span>开发信息</span>
          </div>
        </template>
        
        <div class="team-content">
          <div class="developer-info">
            <div class="developer-avatar">
              <el-icon><User /></el-icon>
            </div>
            <div class="developer-details">
              <h3>开发者</h3>
              <p>WebRTC远程控制系统</p>
              <p>专注于高性能实时通信技术</p>
            </div>
          </div>
          
          <div class="contact-info">
            <h4>技术支持</h4>
            <div class="contact-links">
              <el-button :icon="Document" text>文档</el-button>
              <el-button :icon="ChatDotRound" text>反馈</el-button>
              <el-button :icon="Star" text>GitHub</el-button>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 许可证信息 -->
      <el-card class="license-card">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>许可证</span>
          </div>
        </template>
        
        <div class="license-content">
          <p>本软件基于MIT许可证开源发布</p>
          <p>Copyright © 2024 WebRTC Remote Control System</p>
          <p>允许自由使用、修改和分发，但需保留版权声明</p>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import {
  VideoCamera,
  Microphone,
  Mouse,
  Monitor,
  DataAnalysis,
  Setting,
  User,
  Document,
  ChatDotRound,
  Star
} from '@element-plus/icons-vue'

// 系统信息
const buildTime = ref(new Date().toLocaleString())
const gitCommit = ref('abc123f')
const environment = ref('Development')
</script>

<style scoped>
.about-view {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.about-container {
  max-width: 1000px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 16px;
}

.header-icon {
  font-size: 32px;
  color: var(--el-color-primary);
}

.header-text h2 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 24px;
  font-weight: 600;
}

.header-text p {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.info-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.info-section h3 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
  font-weight: 600;
  border-bottom: 2px solid var(--el-color-primary);
  padding-bottom: 8px;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
}

.info-label {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.info-value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.tech-stack {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.tech-category h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
}

.tech-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 20px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-primary);
}

.feature-icon {
  font-size: 24px;
  color: var(--el-color-primary);
  flex-shrink: 0;
  margin-top: 4px;
}

.feature-content h4 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
}

.feature-content p {
  margin: 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
  line-height: 1.5;
}

.requirements {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.requirement-category h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
}

.requirement-category ul {
  margin: 0;
  padding-left: 20px;
  color: var(--el-text-color-regular);
}

.requirement-category li {
  margin-bottom: 6px;
  line-height: 1.4;
}

.team-card,
.license-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.team-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.developer-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.developer-avatar {
  width: 60px;
  height: 60px;
  background: var(--el-color-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.developer-details h3 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
  font-weight: 600;
}

.developer-details p {
  margin: 0 0 4px 0;
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.contact-info h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 500;
}

.contact-links {
  display: flex;
  gap: 12px;
}

.license-content {
  color: var(--el-text-color-regular);
  line-height: 1.6;
}

.license-content p {
  margin: 0 0 8px 0;
}

/* 深色主题适配 */
:global(.dark) .info-card,
:global(.dark) .team-card,
:global(.dark) .license-card {
  background: var(--el-bg-color-page);
}
</style>
