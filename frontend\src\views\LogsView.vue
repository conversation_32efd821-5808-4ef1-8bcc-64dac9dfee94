<template>
  <div class="logs-view">
    <!-- 日志控制栏 -->
    <div class="logs-toolbar">
      <div class="toolbar-left">
        <el-input
          v-model="logsStore.filterKeyword"
          placeholder="搜索日志..."
          :prefix-icon="Search"
          clearable
          style="width: 300px" />
        
        <el-select
          v-model="logsStore.filterLevel"
          placeholder="日志级别"
          style="width: 120px">
          <el-option label="全部" value="all" />
          <el-option label="调试" value="debug" />
          <el-option label="信息" value="info" />
          <el-option label="警告" value="warning" />
          <el-option label="错误" value="error" />
        </el-select>
        
        <el-select
          v-model="logsStore.filterCategory"
          placeholder="分类"
          style="width: 120px">
          <el-option label="全部" value="all" />
          <el-option label="WebRTC" value="webrtc" />
          <el-option label="视频" value="video" />
          <el-option label="音频" value="audio" />
          <el-option label="网络" value="network" />
          <el-option label="输入" value="input" />
        </el-select>
      </div>
      
      <div class="toolbar-right">
        <el-button :icon="Download" @click="logsStore.exportLogs">
          导出日志
        </el-button>
        <el-button :icon="Upload" @click="importLogs">
          导入日志
        </el-button>
        <el-button :icon="Delete" @click="clearLogs" type="danger">
          清空日志
        </el-button>
        <input
          ref="fileInput"
          type="file"
          accept=".json"
          style="display: none"
          @change="handleFileImport" />
      </div>
    </div>

    <!-- 日志统计 -->
    <div class="logs-stats">
      <div class="stats-grid">
        <div class="stat-item">
          <div class="stat-value">{{ logsStore.logStats.total }}</div>
          <div class="stat-label">总计</div>
        </div>
        <div class="stat-item error">
          <div class="stat-value">{{ logsStore.logStats.error }}</div>
          <div class="stat-label">错误</div>
        </div>
        <div class="stat-item warning">
          <div class="stat-value">{{ logsStore.logStats.warning }}</div>
          <div class="stat-label">警告</div>
        </div>
        <div class="stat-item success">
          <div class="stat-value">{{ logsStore.logStats.success }}</div>
          <div class="stat-label">成功</div>
        </div>
        <div class="stat-item info">
          <div class="stat-value">{{ logsStore.logStats.info }}</div>
          <div class="stat-label">信息</div>
        </div>
        <div class="stat-item debug">
          <div class="stat-value">{{ logsStore.logStats.debug }}</div>
          <div class="stat-label">调试</div>
        </div>
      </div>
    </div>

    <!-- 日志列表 -->
    <div class="logs-container">
      <el-card class="logs-card">
        <div class="logs-header">
          <span>日志列表 ({{ logsStore.filteredLogs.length }} / {{ logsStore.logs.length }})</span>
          <el-switch
            v-model="autoScroll"
            active-text="自动滚动"
            inactive-text="手动滚动" />
        </div>
        
        <div class="logs-content" ref="logsContainer">
          <div
            v-for="log in logsStore.filteredLogs"
            :key="log.id"
            :class="['log-entry', `log-${log.type.toLowerCase()}`]">
            <div class="log-time">{{ log.time }}</div>
            <div class="log-type">[{{ log.type }}]</div>
            <div class="log-category" v-if="log.category">[{{ log.category }}]</div>
            <div class="log-message">{{ log.message }}</div>
            <div class="log-actions">
              <el-button
                :icon="CopyDocument"
                size="small"
                text
                @click="copyLog(log)" />
            </div>
          </div>
          
          <div v-if="logsStore.filteredLogs.length === 0" class="logs-empty">
            <el-empty description="暂无日志数据" />
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Download,
  Upload,
  Delete,
  CopyDocument
} from '@element-plus/icons-vue'

import { useLogsStore } from '../stores/logs'

const logsStore = useLogsStore()

// 模板引用
const fileInput = ref(null)
const logsContainer = ref(null)

// 状态
const autoScroll = ref(true)

// 监听日志变化，自动滚动到底部
watch(
  () => logsStore.logs.length,
  () => {
    if (autoScroll.value) {
      nextTick(() => {
        if (logsContainer.value) {
          logsContainer.value.scrollTop = logsContainer.value.scrollHeight
        }
      })
    }
  }
)

// 方法
function clearLogs() {
  ElMessageBox.confirm(
    '确定要清空所有日志吗？此操作不可撤销。',
    '确认清空',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    logsStore.clearLogs()
    ElMessage.success('日志已清空')
  }).catch(() => {
    // 用户取消
  })
}

function importLogs() {
  fileInput.value.click()
}

function handleFileImport(event) {
  const file = event.target.files[0]
  if (!file) return

  logsStore.importLogs(file)
    .then((count) => {
      ElMessage.success(`成功导入 ${count} 条日志`)
    })
    .catch((error) => {
      ElMessage.error(`导入失败: ${error.message}`)
    })
  
  // 清空文件输入
  event.target.value = ''
}

function copyLog(log) {
  const logText = `[${log.time}] [${log.type}]${log.category ? ` [${log.category}]` : ''} ${log.message}`
  
  if (navigator.clipboard) {
    navigator.clipboard.writeText(logText).then(() => {
      ElMessage.success('日志已复制到剪贴板')
    }).catch(() => {
      ElMessage.error('复制失败')
    })
  } else {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = logText
    document.body.appendChild(textArea)
    textArea.select()
    try {
      document.execCommand('copy')
      ElMessage.success('日志已复制到剪贴板')
    } catch (err) {
      ElMessage.error('复制失败')
    }
    document.body.removeChild(textArea)
  }
}
</script>

<style scoped>
.logs-view {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.logs-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logs-stats {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 6px;
  border-left: 4px solid var(--el-color-primary);
}

.stat-item.error {
  border-left-color: var(--el-color-danger);
}

.stat-item.warning {
  border-left-color: var(--el-color-warning);
}

.stat-item.success {
  border-left-color: var(--el-color-success);
}

.stat-item.info {
  border-left-color: var(--el-color-info);
}

.stat-item.debug {
  border-left-color: var(--el-color-purple);
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-text-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.logs-container {
  flex: 1;
  min-height: 0;
}

.logs-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  font-weight: 600;
}

.logs-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 16px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  transition: background-color 0.2s;
}

.log-entry:hover {
  background-color: var(--el-fill-color-lighter);
}

.log-time {
  color: var(--el-text-color-secondary);
  font-size: 11px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 70px;
}

.log-type {
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: 60px;
}

.log-category {
  font-weight: 500;
  white-space: nowrap;
  flex-shrink: 0;
  color: var(--el-color-primary);
  font-size: 11px;
}

.log-message {
  flex: 1;
  word-wrap: break-word;
}

.log-actions {
  flex-shrink: 0;
  opacity: 0;
  transition: opacity 0.2s;
}

.log-entry:hover .log-actions {
  opacity: 1;
}

/* 不同日志类型的颜色 */
.log-success .log-type {
  color: var(--el-color-success);
}

.log-error .log-type {
  color: var(--el-color-danger);
}

.log-warning .log-type {
  color: var(--el-color-warning);
}

.log-info .log-type {
  color: var(--el-color-info);
}

.log-debug .log-type {
  color: var(--el-color-purple);
}

.log-input .log-type {
  color: var(--el-color-primary);
}

.logs-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 6px;
}

.logs-content::-webkit-scrollbar-track {
  background: var(--el-fill-color-lighter);
}

.logs-content::-webkit-scrollbar-thumb {
  background: var(--el-border-color);
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-dark);
}

/* 深色主题适配 */
:global(.dark) .logs-toolbar,
:global(.dark) .logs-stats {
  background: var(--el-bg-color-page);
}
</style>
