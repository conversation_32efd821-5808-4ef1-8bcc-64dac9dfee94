<template>
  <div class="main-view">
    <!-- 控制面板 -->
    <ControlPanel
      :server-url="connection.serverUrl.value"
      @update:server-url="(value) => connection.serverUrl.value = value"
      :connected="connection.connected.value"
      :connecting="connection.connecting.value"
      :connection-status="connection.connectionStatus.value"
      :is-receiving-video="connection.isReceivingVideo.value"
      :is-receiving-audio="connection.isReceivingAudio.value"
      :use-web-codecs="false"
      :current-decoder="decoder.currentDecoder.value"
      @connect="handleConnect"
      @disconnect="handleDisconnect"
      @request-key-frame="handleRequestKeyFrame"
      @reset-player="handleResetPlayer"
      @toggle-decoder="handleToggleDecoder"
      @switch-decoder="switchToDecoder"
      @diagnose="handleDiagnose"
      @test-connection="handleTestConnection"
      @check-codec-support="handleCheckCodecSupport" />

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 视频区域 -->
      <div class="video-section">
        <div
          class="video-container"
          @mousedown="handleMouseEvent"
          @mouseup="handleMouseEvent"
          @mousemove="handleMouseEvent"
          @click="handleMouseAndFocus"
          @contextmenu="handleContextMenu"
          @wheel="handleWheelEvent"
          @keydown="handleKeyEvent"
          @keyup="handleKeyEvent"
          @focus="handleFocus"
          @blur="handleBlur"
          tabindex="0"
          ref="videoContainer">
          <!-- Canvas播放器 (主要显示) -->
          <canvas
            ref="remoteCanvas"
            class="remote-video optimized-canvas"
            style="width: 100%; height: 100%; object-fit: contain; background: #000; display: block; border: 2px solid green; z-index: 1000;">
          </canvas>

          <!-- VideoTrack播放器 (隐藏，不再使用) -->
          <video
            ref="remoteVideo"
            autoplay
            playsinline
            muted
            controls
            class="remote-video"
            style="display: none;">
            您的浏览器不支持视频播放
          </video>

          <!-- 键鼠控制状态指示器 -->
          <div v-if="connection.connected.value" class="control-status">
            <div class="status-indicator" :class="{ active: isControlActive }">
              <el-icon><Mouse /></el-icon>
              <span>{{ isControlActive ? '键鼠控制已激活' : '点击激活键鼠控制' }}</span>
            </div>
          </div>

          <div v-if="!connection.isReceivingVideo.value" class="video-placeholder">
            <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
            <p>等待视频流...</p>
            <p v-if="connection.connected.value">连接已建立，等待视频数据...</p>
            <p v-if="connection.connected.value" class="control-hint">
              💡 提示：视频开始播放后，点击视频区域即可进行键鼠控制
            </p>
          </div>
        </div>
      </div>

      <!-- 侧边信息面板 -->
      <div class="info-panel">
        <!-- 连接状态 -->
        <StatusPanel
          :connection-status="connection.connectionStatus.value"
          :is-receiving-video="connection.isReceivingVideo.value"
          :is-receiving-audio="connection.isReceivingAudio.value"
          :use-web-codecs="false"
          :current-decoder="decoder.currentDecoder.value"
          :stats="{}"
          :frame-stats="dataProcessor.frameStats"
          :buffer-stats="dataProcessor.bufferManager"
          @refresh="handleRefreshStatus"
          @reset-stats="handleResetStats" />

        <!-- 日志显示 -->
        <LogPanel
          :logs="logger.logs.value"
          @clear="logger.clearLogs"
          @export="handleExportLogs" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, Mouse } from '@element-plus/icons-vue'

// 导入组件
import ControlPanel from '@/components/ControlPanel.vue'
import StatusPanel from '@/components/StatusPanel.vue'
import LogPanel from '@/components/LogPanel.vue'

// 导入 Composables
import { useWebRTCConnection } from '@/composables/useWebRTCConnection'
import { useDecoderManager } from '@/composables/useDecoderManager'
import { useDataProcessor } from '@/composables/useDataProcessor'
import { useLogger } from '@/composables/useLogger'

// 模板引用
const remoteVideo = ref(null)
const remoteCanvas = ref(null)
const videoContainer = ref(null)

// 控制状态
const isControlActive = ref(false)

// 初始化 Composables
console.log('🎯🎯🎯 MainView: 页面加载，初始化Composables')
const logger = useLogger(1000)
const connection = useWebRTCConnection()
const decoder = useDecoderManager()
const dataProcessor = useDataProcessor()
console.log('🎯🎯🎯 MainView: Composables初始化完成')

// 设置事件回调
connection.onDataChannelMessage.value = (data) => {
  // 处理输入事件数据
  console.log(`⌨️ 收到输入事件数据: ${data.length} 字节`)
}

connection.onVideoChannelMessage.value = (data) => {
  // 处理视频数据（H264）
  const result = dataProcessor.processDataChannelMessage(data)
  if (!result.success && result.error) {
    logger.error(`视频数据处理失败: ${result.error}`)
  }
}

connection.onAudioChannelMessage.value = (data) => {
  // 处理音频数据
  console.log(`🎵 收到音频数据: ${data.byteLength || data.length} 字节`)
  if (dataProcessor.onAudioData.value) {
    dataProcessor.onAudioData.value(data)
  }
}

connection.onDataChannelOpen.value = () => {
  logger.success('📡 DataChannel已打开')
}

connection.onDataChannelClose.value = () => {
  logger.warning('📡 DataChannel已关闭')
}

// Track处理 - REMOVED (使用DataChannel代替)

dataProcessor.onH264Data.value = async (data) => {
  const result = await decoder.handleH264Data(data)
  if (!result.success && result.error) {
    logger.error(`H264解码失败: ${result.error}`)
  } else if (result.success) {
    // 记录成功解码
    logger.success(`📺 ${result.decoder}解码成功`)
  }
}

dataProcessor.onAudioData.value = async (data) => {
  const result = await decoder.handleAudioData(data)
  if (!result.success && result.error) {
    logger.error(`音频解码失败: ${result.error}`)
  } else if (result.success) {
    logger.success(`🔊 ${result.decoder}音频解码成功`)
  }
}

dataProcessor.onError.value = (error) => {
  logger.error(`数据处理错误: ${error.message}`)
}

// 事件处理函数
async function handleConnect() {
  logger.info('开始连接到服务器...')
  console.log('🔗🔗🔗 MainView: 开始连接到服务器')

  const result = await connection.connectToServer()
  console.log('🔗🔗🔗 MainView: 连接结果:', result)

  if (result.success) {
    logger.success('连接建立成功，等待数据...')
    console.log('🔗🔗🔗 MainView: 连接成功，初始化解码器')
    // 初始化解码器
    await initializeDecoders()
  } else {
    logger.error(`连接失败: ${result.error}`)
    ElMessage.error(`连接失败: ${result.error}`)
    console.log('🔗🔗🔗 MainView: 连接失败:', result.error)
  }
}

function handleDisconnect() {
  const result = connection.disconnectFromServer()
  if (result.success) {
    logger.info('已断开连接')
  }
}

async function handleRequestKeyFrame() {
  logger.info('🔑 请求关键帧 (Canvas播放器)')
}

async function handleResetPlayer() {
  decoder.cleanup()
  logger.success('🔄 Canvas播放器已重置')
}

async function handleToggleDecoder() {
  logger.info('🔄 切换解码器...')
  const result = await decoder.switchDecoder()
  if (result) {
    logger.success(`✅ 已切换到: ${decoder.currentDecoder.value}`)
    ElMessage.success(`解码器已切换到: ${decoder.currentDecoder.value}`)
  } else {
    logger.error('❌ 解码器切换失败')
    ElMessage.error('解码器切换失败')
  }
}

// 🎬 快速切换到特定解码器
async function switchToDecoder(type) {
  logger.info(`🎬 切换到${type}解码器...`)
  const result = await decoder.switchDecoder(type)
  if (result) {
    logger.success(`✅ 已切换到: ${decoder.currentDecoder.value}`)
    ElMessage.success(`解码器已切换到: ${decoder.currentDecoder.value}`)
  } else {
    logger.error('❌ 解码器切换失败')
    ElMessage.error('解码器切换失败')
  }
}

function handleDiagnose() {
  logger.info('🔍 Canvas播放器状态诊断')
  logger.info(`当前解码器: ${decoder.currentDecoder.value}`)
  logger.info(`初始化状态: ${decoder.isInitialized.value ? '已初始化' : '未初始化'}`)
}

async function handleTestConnection() {
  const result = await connection.testConnection()
  if (result.success) {
    logger.success('连接测试成功')
  } else {
    logger.error(`连接测试失败: ${result.error}`)
  }
}

function handleCheckCodecSupport() {
  logger.info('检查编解码器支持...')
  // 这里可以添加编解码器支持检查逻辑
  logger.info('编解码器支持检查完成')
}

function handleRefreshStatus() {
  logger.info('刷新状态...')
}

function handleResetStats() {
  dataProcessor.resetStats()
  logger.info('统计信息已重置')
}

function handleExportLogs() {
  logger.info('日志已导出')
}

// 初始化解码器
async function initializeDecoders() {
  if (!remoteCanvas.value) {
    logger.error('Canvas元素未找到')
    return
  }

  // 🚀 使用服务器实际分辨率配置 (1600x960@30fps)
  const result = await decoder.initDecoder(
    remoteCanvas.value,
    {
      width: 1600,
      height: 960,
      framerate: 30,
      lowLatency: true // 🚀 启用低延迟模式
    },
    (level, message) => logger.addLog(level, message)
  )

  if (result) {
    logger.success('✅ Canvas播放器初始化成功')
  } else {
    logger.error('❌ Canvas播放器初始化失败')
  }
}

// 处理视频轨道
function handleVideoTrack(track) {
  logger.info(`📺 开始处理视频轨道: ${track.id}`)
  console.log('📺📺📺 VideoTrack详细信息:', track)
  console.log('📺📺📺 Track状态:', track.readyState)
  console.log('📺📺📺 Track设置:', track.getSettings())

  if (!remoteVideo.value) {
    logger.error('❌ 视频元素未找到')
    console.log('📺📺📺 remoteVideo.value:', remoteVideo.value)
    return
  }

  try {
    // 创建MediaStream并设置到video元素
    const stream = new MediaStream([track])
    console.log('📺📺📺 创建的MediaStream:', stream)
    console.log('📺📺📺 Stream tracks:', stream.getTracks())
    console.log('📺📺📺 Stream active:', stream.active)
    console.log('📺📺📺 Stream id:', stream.id)

    // 监听stream事件
    stream.addEventListener('addtrack', (event) => {
      console.log('📺📺📺 Stream添加了track:', event.track)
    })

    stream.addEventListener('removetrack', (event) => {
      console.log('📺📺📺 Stream移除了track:', event.track)
    })

    remoteVideo.value.srcObject = stream
    console.log('📺📺📺 srcObject已设置:', remoteVideo.value.srcObject)

    // 设置视频元素属性
    remoteVideo.value.autoplay = true
    remoteVideo.value.playsInline = true
    remoteVideo.value.muted = true

    // 强制播放
    setTimeout(() => {
      console.log('📺📺📺 尝试强制播放视频')
      remoteVideo.value.play().then(() => {
        console.log('📺📺📺 视频播放成功')
        logger.success('📺 视频播放成功')

        // 定期检查视频状态
        const checkVideoStatus = () => {
          if (!remoteVideo.value) {
            console.log('📺📺📺 视频元素不存在')
            return
          }
          console.log('📺📺📺 视频状态检查:')
          console.log('  - paused:', remoteVideo.value.paused)
          console.log('  - currentTime:', remoteVideo.value.currentTime)
          console.log('  - duration:', remoteVideo.value.duration)
          console.log('  - videoWidth:', remoteVideo.value.videoWidth)
          console.log('  - videoHeight:', remoteVideo.value.videoHeight)
          console.log('  - readyState:', remoteVideo.value.readyState)
          console.log('  - networkState:', remoteVideo.value.networkState)

          if (remoteVideo.value.videoWidth === 0 || remoteVideo.value.videoHeight === 0) {
            console.warn('📺📺📺 ⚠️ 视频尺寸为0，可能没有视频数据')
            logger.warning('⚠️ 视频尺寸为0，可能没有视频数据')
          }
        }

        // 立即检查一次
        setTimeout(checkVideoStatus, 1000)
        // 然后每5秒检查一次
        const statusInterval = setInterval(checkVideoStatus, 5000)

        // 监听track的数据事件
        track.addEventListener('unmute', () => {
          console.log('📺📺📺 Track unmuted - 开始接收数据')
          logger.success('📺 Track开始接收数据')
        })

        track.addEventListener('mute', () => {
          console.log('📺📺📺 Track muted - 停止接收数据')
          logger.warning('⚠️ Track停止接收数据')
        })

        track.addEventListener('ended', () => {
          console.log('📺📺📺 Track ended')
          logger.info('📺 Track结束')
          clearInterval(statusInterval)
        })

      }).catch(error => {
        console.error('📺📺📺 视频播放失败:', error)
        logger.error(`❌ 视频播放失败: ${error.message}`)
      })
    }, 100)

    // 监听视频事件
    remoteVideo.value.onloadedmetadata = () => {
      console.log('📺📺📺 视频元数据加载完成')
      console.log('📺📺📺 视频尺寸:', remoteVideo.value.videoWidth, 'x', remoteVideo.value.videoHeight)
      logger.success('📺 视频元数据加载完成')
    }

    remoteVideo.value.onplay = () => {
      console.log('📺📺📺 视频开始播放')
      logger.success('📺 视频开始播放')
    }

    remoteVideo.value.onplaying = () => {
      console.log('📺📺📺 视频正在播放')
      logger.success('📺 视频正在播放')
    }

    remoteVideo.value.onerror = (error) => {
      console.error('📺📺📺 视频播放错误:', error)
      logger.error(`❌ 视频播放错误: ${error}`)
    }

    remoteVideo.value.onstalled = () => {
      console.warn('📺📺📺 视频播放停滞')
      logger.warning('⚠️ 视频播放停滞')
    }

    logger.success('✅ VideoTrack已设置到video元素')

  } catch (error) {
    console.error('📺📺📺 VideoTrack处理失败:', error)
    logger.error(`❌ VideoTrack处理失败: ${error.message}`)
  }
}

// 处理音频轨道
function handleAudioTrack(track) {
  logger.info(`🔊 开始处理音频轨道: ${track.id}`)

  try {
    // 创建音频元素或使用现有的video元素
    if (remoteVideo.value) {
      const currentStream = remoteVideo.value.srcObject
      if (currentStream) {
        // 添加音频轨道到现有流
        currentStream.addTrack(track)
        logger.success('🔊 音频轨道已添加到现有流')
      } else {
        // 创建新的流
        const stream = new MediaStream([track])
        remoteVideo.value.srcObject = stream
        logger.success('🔊 音频轨道已设置到新流')
      }
    }

    logger.success('✅ AudioTrack处理完成')

  } catch (error) {
    logger.error(`❌ AudioTrack处理失败: ${error.message}`)
  }
}

// 鼠标和键盘事件处理（远程控制）
function handleMouseEvent(event) {
  if (!connection.connected.value || !connection.dataChannel.value) {
    return
  }

  // 阻止默认行为
  event.preventDefault()
  event.stopPropagation()

  // 获取视频容器的边界矩形
  const rect = videoContainer.value.getBoundingClientRect()

  // 计算相对于视频容器的坐标
  const relativeX = event.clientX - rect.left
  const relativeY = event.clientY - rect.top

  // 计算相对坐标（0-1范围）
  const normalizedX = relativeX / rect.width
  const normalizedY = relativeY / rect.height

  // 转换为目标屏幕坐标（假设目标屏幕为1920x1080）
  const targetX = Math.round(normalizedX * 1920)
  const targetY = Math.round(normalizedY * 1080)

  // 确保坐标在有效范围内
  const clampedX = Math.max(0, Math.min(1920, targetX))
  const clampedY = Math.max(0, Math.min(1080, targetY))

  // 创建鼠标事件消息
  const mouseMessage = {
    type: event.type,
    x: clampedX,
    y: clampedY,
    button: event.button,
    buttons: event.buttons,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    timestamp: Date.now()
  }

  // 发送到服务器
  const result = connection.sendDataChannelMessage(JSON.stringify(mouseMessage))

  // 详细日志输出
  if (event.type === 'mousemove') {
    // 鼠标移动事件，降低日志频率
    if (Date.now() % 100 < 20) { // 大约每100ms记录一次
      logger.info(`🖱️ 鼠标移动: (${relativeX.toFixed(1)}, ${relativeY.toFixed(1)}) → 目标(${clampedX}, ${clampedY}) | 归一化(${normalizedX.toFixed(3)}, ${normalizedY.toFixed(3)})`)
    }
  } else {
    // 点击事件，详细记录
    const buttonNames = ['左键', '中键', '右键']
    const buttonName = buttonNames[event.button] || `按钮${event.button}`
    const actionName = event.type === 'mousedown' ? '按下' : event.type === 'mouseup' ? '释放' : '点击'

    logger.info(`🖱️ 鼠标${actionName}: ${buttonName} 在(${relativeX.toFixed(1)}, ${relativeY.toFixed(1)}) → 目标(${clampedX}, ${clampedY})`)
    logger.info(`🖱️ 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`)
    logger.info(`🖱️ 按钮状态: buttons=${event.buttons}, 容器尺寸: ${rect.width.toFixed(1)}x${rect.height.toFixed(1)}`)
  }

  if (!result.success) {
    logger.error(`🖱️ 鼠标事件发送失败: ${result.error}`)
  }
}

function handleMouseAndFocus(event) {
  // 确保视频容器获得焦点以接收键盘事件
  videoContainer.value?.focus()
  handleMouseEvent(event)

  logger.info(`🎯 视频容器已获得焦点，准备接收键盘输入`)
}

// 焦点事件处理
function handleFocus() {
  isControlActive.value = true
  logger.success('🎮 键鼠控制已激活')
}

function handleBlur() {
  isControlActive.value = false
  logger.info('🎮 键鼠控制已停用')
}

function handleKeyEvent(event) {
  if (!connection.connected.value || !connection.dataChannel.value) {
    return
  }

  // 阻止默认行为（避免浏览器快捷键干扰）
  event.preventDefault()
  event.stopPropagation()

  // 创建键盘事件消息
  const keyMessage = {
    type: event.type,
    key: event.key,
    code: event.code,
    keyCode: event.keyCode,
    which: event.which,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    repeat: event.repeat,
    timestamp: Date.now()
  }

  // 发送到服务器
  const result = connection.sendDataChannelMessage(JSON.stringify(keyMessage))

  // 详细日志输出
  const actionName = event.type === 'keydown' ? '按下' : '释放'
  const keyInfo = event.key.length === 1 ? `'${event.key}'` : event.key
  const codeInfo = event.code !== event.key ? ` (${event.code})` : ''

  logger.info(`⌨️ 键盘${actionName}: ${keyInfo}${codeInfo} | keyCode=${event.keyCode}`)
  logger.info(`⌨️ 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`)

  if (event.repeat) {
    logger.info(`⌨️ 重复按键事件`)
  }

  // 特殊按键提示
  if (['Control', 'Shift', 'Alt', 'Meta'].includes(event.key)) {
    logger.info(`⌨️ 修饰键${actionName}: ${event.key}`)
  } else if (['Enter', 'Escape', 'Tab', 'Backspace', 'Delete'].includes(event.key)) {
    logger.info(`⌨️ 功能键${actionName}: ${event.key}`)
  } else if (event.key.startsWith('F') && event.key.length <= 3) {
    logger.info(`⌨️ 功能键${actionName}: ${event.key}`)
  } else if (event.key.startsWith('Arrow')) {
    logger.info(`⌨️ 方向键${actionName}: ${event.key}`)
  }

  if (!result.success) {
    logger.error(`⌨️ 键盘事件发送失败: ${result.error}`)
  }
}

// 右键菜单事件处理
function handleContextMenu(event) {
  // 阻止浏览器默认右键菜单
  event.preventDefault()
  event.stopPropagation()

  logger.info(`🖱️ 右键菜单被阻止，坐标: (${event.clientX}, ${event.clientY})`)

  // 将右键点击作为普通鼠标事件处理
  handleMouseEvent(event)
}

// 滚轮事件处理
function handleWheelEvent(event) {
  if (!connection.connected.value || !connection.dataChannel.value) {
    return
  }

  // 阻止默认滚动行为
  event.preventDefault()
  event.stopPropagation()

  // 获取视频容器的边界矩形
  const rect = videoContainer.value.getBoundingClientRect()

  // 计算相对坐标
  const relativeX = event.clientX - rect.left
  const relativeY = event.clientY - rect.top
  const normalizedX = relativeX / rect.width
  const normalizedY = relativeY / rect.height
  const targetX = Math.round(normalizedX * 1920)
  const targetY = Math.round(normalizedY * 1080)
  const clampedX = Math.max(0, Math.min(1920, targetX))
  const clampedY = Math.max(0, Math.min(1080, targetY))

  // 创建滚轮事件消息
  const wheelMessage = {
    type: 'wheel',
    x: clampedX,
    y: clampedY,
    deltaX: event.deltaX,
    deltaY: event.deltaY,
    deltaZ: event.deltaZ,
    deltaMode: event.deltaMode,
    ctrlKey: event.ctrlKey,
    shiftKey: event.shiftKey,
    altKey: event.altKey,
    metaKey: event.metaKey,
    timestamp: Date.now()
  }

  // 发送到服务器
  const result = connection.sendDataChannelMessage(JSON.stringify(wheelMessage))

  // 详细日志输出
  const direction = event.deltaY > 0 ? '向下' : event.deltaY < 0 ? '向上' : '水平'
  const deltaInfo = `deltaY=${event.deltaY.toFixed(1)}, deltaX=${event.deltaX.toFixed(1)}`

  logger.info(`🎡 滚轮滚动: ${direction} 在(${relativeX.toFixed(1)}, ${relativeY.toFixed(1)}) → 目标(${clampedX}, ${clampedY})`)
  logger.info(`🎡 滚轮数据: ${deltaInfo}, deltaMode=${event.deltaMode}`)
  logger.info(`🎡 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`)

  if (!result.success) {
    logger.error(`🎡 滚轮事件发送失败: ${result.error}`)
  }
}

// 页面刷新/关闭事件处理
function handleBeforeUnload() {
  if (connection.connected.value || connection.connecting.value) {
    connection.disconnectFromServer()
  }
  decoder.cleanup()
  dataProcessor.cleanup()
}

// 全局键盘事件处理（用于捕获一些特殊快捷键）
function handleGlobalKeyEvent(event) {
  // 只在连接状态下且视频容器有焦点时处理
  if (!connection.connected.value || document.activeElement !== videoContainer.value) {
    return
  }

  // 处理一些特殊的快捷键组合
  if (event.ctrlKey && event.altKey && event.key === 'r') {
    // Ctrl+Alt+R: 请求关键帧
    event.preventDefault()
    handleRequestKeyFrame()
    logger.info('🔑 快捷键触发: 请求关键帧 (Ctrl+Alt+R)')
    return
  }

  if (event.ctrlKey && event.altKey && event.key === 'd') {
    // Ctrl+Alt+D: 断开连接
    event.preventDefault()
    handleDisconnect()
    logger.info('🔌 快捷键触发: 断开连接 (Ctrl+Alt+D)')
    return
  }

  if (event.ctrlKey && event.altKey && event.key === 'f') {
    // Ctrl+Alt+F: 切换全屏
    event.preventDefault()
    toggleFullscreen()
    logger.info('🖥️ 快捷键触发: 切换全屏 (Ctrl+Alt+F)')
    return
  }
}

// 全屏切换功能
function toggleFullscreen() {
  if (!document.fullscreenElement) {
    videoContainer.value?.requestFullscreen().catch(err => {
      logger.error(`全屏请求失败: ${err.message}`)
    })
  } else {
    document.exitFullscreen().catch(err => {
      logger.error(`退出全屏失败: ${err.message}`)
    })
  }
}

// 组件挂载时初始化
onMounted(() => {
  logger.info('🚀 WebRTC远程控制系统初始化完成')
  logger.info('点击"连接"按钮开始连接到服务器')
  logger.info('🎮 键鼠控制提示:')
  logger.info('  • 点击视频区域获得焦点后可进行键鼠控制')
  logger.info('  • Ctrl+Alt+R: 请求关键帧')
  logger.info('  • Ctrl+Alt+D: 断开连接')
  logger.info('  • Ctrl+Alt+F: 切换全屏')

  // 添加页面刷新/关闭事件处理
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 添加全局键盘事件监听
  window.addEventListener('keydown', handleGlobalKeyEvent)
  window.addEventListener('keyup', handleGlobalKeyEvent)

  // 定期报告统计信息
  setInterval(() => {
    const stats = dataProcessor.getStats()
    const h264Stats = stats.frameStats.h264
    const audioStats = stats.frameStats.audio

    if (h264Stats.received > 0 || audioStats.received > 0) {
      console.log(`📊 帧统计 - H264: ${h264Stats.completed}/${h264Stats.received} (掉帧: ${h264Stats.dropped}), 音频: ${audioStats.completed}/${audioStats.received} (掉帧: ${audioStats.dropped})`)
    }
  }, 5000) // 每5秒报告一次
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)
  window.removeEventListener('keydown', handleGlobalKeyEvent)
  window.removeEventListener('keyup', handleGlobalKeyEvent)

  // 退出全屏（如果处于全屏状态）
  if (document.fullscreenElement) {
    document.exitFullscreen().catch(() => {})
  }

  // 断开连接并清理资源
  if (connection.connected.value || connection.connecting.value) {
    connection.disconnectFromServer()
  }

  decoder.cleanup()
  dataProcessor.cleanup()
  logger.info('🧹 资源已清理，事件监听器已移除')
})
</script>

<style scoped>
.main-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.content-area {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.video-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  outline: none;
  cursor: crosshair;
  transition: all 0.3s ease;
  user-select: none; /* 禁止文本选择 */
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.video-container:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5), 0 8px 32px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(59, 130, 246, 0.8);
}

.video-container:hover {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3), 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* 全屏状态下的样式 */
.video-container:fullscreen {
  border-radius: 0;
  cursor: none; /* 全屏时隐藏鼠标指针 */
}

.video-container:-webkit-full-screen {
  border-radius: 0;
  cursor: none;
}

.video-container:-moz-full-screen {
  border-radius: 0;
  cursor: none;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.video-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #9ca3af;
  z-index: 10;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.video-placeholder p {
  margin: 8px 0;
  font-size: 16px;
}

.control-hint {
  font-size: 14px !important;
  color: #10b981 !important;
  margin-top: 16px !important;
}

/* 键鼠控制状态指示器 */
.control-status {
  position: absolute;
  top: 16px;
  right: 16px;
  z-index: 20;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.7);
  color: #9ca3af;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.status-indicator.active {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
  border-color: rgba(16, 185, 129, 0.3);
  box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.status-indicator .el-icon {
  font-size: 16px;
}

.info-panel {
  width: 400px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-area {
    flex-direction: column;
  }

  .info-panel {
    width: 100%;
    flex-direction: row;
    gap: 16px;
  }

  .info-panel > * {
    flex: 1;
  }
}

@media (max-width: 768px) {
  .content-area {
    padding: 8px;
  }

  .info-panel {
    flex-direction: column;
  }

  .video-container {
    border-radius: 8px;
  }
}

/* 🚀 Canvas性能优化 */
.optimized-canvas {
  /* GPU硬件加速 */
  transform: translateZ(0);
  will-change: contents;

  /* 像素完美渲染 */
  image-rendering: pixelated;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;

  /* 禁用用户交互，提升性能 */
  user-select: none;
  -webkit-user-drag: none;
  pointer-events: auto;

  /* 优化渲染性能 */
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
}

/* 深色主题适配 */
:global(.dark) .main-view {
  background: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
}

:global(.dark) .video-placeholder {
  color: #64748b;
}
</style>
