<template>
  <div class="main-view">
    <!-- 连接控制面板 -->
    <div class="control-panel">
      <div class="control-toolbar">
        <div class="connection-controls">
          <el-input
            v-model="serverUrl"
            placeholder="服务器地址"
            style="width: 200px"
            :disabled="connecting || connected">
            <template #prepend>服务器</template>
          </el-input>

          <el-button
            type="primary"
            :loading="connecting"
            :disabled="connected"
            @click="connectToServer">
            {{ connecting ? '连接中...' : '连接' }}
          </el-button>

          <el-button
            type="danger"
            :disabled="!connected && !connecting"
            @click="disconnectFromServer">
            断开连接
          </el-button>

          <el-button
            type="warning"
            :disabled="!connected"
            @click="handleRequestKeyFrame"
            title="解决花屏问题">
            🔑 请求关键帧
          </el-button>

          <el-button
            type="info"
            :disabled="!connected"
            @click="handleResetPlayer"
            title="重置播放器">
            🔄 重置播放器
          </el-button>

          <el-button
            type="warning"
            @click="handleDiagnoseDecoder"
            title="诊断解码器状态">
            🔍 诊断解码器
          </el-button>

          <el-button
            type="info"
            :disabled="!connected"
            @click="testConnection">
            测试连接
          </el-button>

          <el-button
            type="warning"
            :disabled="!isReceivingVideo"
            @click="unmuteVideo">
            取消视频静音
          </el-button>

          <el-button
            type="primary"
            @click="checkCodecSupport">
            检查编解码器支持
          </el-button>
        </div>

        <div class="status-display">
          <el-tag :type="getStatusType(connectionStatus)" size="large">
            {{ connectionStatus }}
          </el-tag>

          <el-tag v-if="isReceivingVideo" type="success" size="large">
            📺 视频接收中
          </el-tag>

          <el-tag v-if="isReceivingAudio" type="success" size="large">
            🔊 音频接收中
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
      <!-- 视频区域 -->
      <div class="video-section">
        <div
          class="video-container"
          @mousedown="handleMouseEvent"
          @mouseup="handleMouseEvent"
          @mousemove="handleMouseEvent"
          @click="handleMouseAndFocus"
          @keydown="handleKeyEvent"
          @keyup="handleKeyEvent"
          tabindex="0"
          ref="videoContainer">
          <!-- H264解码器使用的Canvas -->
          <canvas
            ref="remoteCanvas"
            class="remote-video"
            style="display: block;">
          </canvas>

          <!-- 备用video元素（如果需要） -->
          <video
            ref="remoteVideo"
            autoplay
            playsinline
            muted
            controls
            class="remote-video"
            style="display: none;">
            您的浏览器不支持视频播放
          </video>

          <div v-if="!isReceivingVideo" class="video-placeholder">
            <el-icon class="placeholder-icon"><VideoCamera /></el-icon>
            <p>等待视频流...</p>
            <p v-if="connected">连接已建立，等待视频数据...</p>
          </div>
        </div>
      </div>

      <!-- 侧边信息面板 -->
      <div class="info-panel">
        <!-- 连接状态 -->
        <el-card class="status-card">
          <template #header>
            <span class="card-header">
              <el-icon><Connection /></el-icon>
              连接状态
            </span>
          </template>

          <div class="status-grid">
            <div class="status-item">
              <span class="status-label">WebRTC:</span>
              <el-tag :type="connected ? 'success' : 'danger'" size="small">
                {{ connected ? '已连接' : '未连接' }}
              </el-tag>
            </div>

            <div class="status-item">
              <span class="status-label">视频:</span>
              <el-tag :type="isReceivingVideo ? 'success' : 'info'" size="small">
                {{ isReceivingVideo ? '接收中' : '等待中' }}
              </el-tag>
            </div>

            <div class="status-item">
              <span class="status-label">音频:</span>
              <el-tag :type="isReceivingAudio ? 'success' : 'info'" size="small">
                {{ isReceivingAudio ? '接收中' : '等待中' }}
              </el-tag>
            </div>
          </div>
        </el-card>

        <!-- 日志显示 -->
        <el-card class="logs-card">
          <template #header>
            <div class="card-header">
              <el-icon><Document /></el-icon>
              连接日志
              <el-button size="small" @click="clearLogs">清空</el-button>
            </div>
          </template>

          <div class="logs-container">
            <div
              v-for="(log, index) in logs"
              :key="index"
              :class="['log-entry', `log-${log.type.toLowerCase()}`]">
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type">{{ log.type }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  VideoCamera,
  Connection,
  Document
} from '@element-plus/icons-vue'
import {
  // � 使用同步媒体播放器 - 解决MP4推流问题
  initCleanH264Player,
  handleCleanH264Data,
  handleCleanAudioData,
  getCleanStats,
  setCleanLogCallback,
  cleanupCleanPlayer,
  diagnoseDecoder
} from '../composables/useCleanH264Player'



// 响应式数据
const serverUrl = ref('http://localhost:8080')
const peerConnection = ref(null)
const dataChannel = ref(null)
const connected = ref(false)
const connecting = ref(false)
const connectionStatus = ref('未连接')
const isReceivingVideo = ref(false)
const isReceivingAudio = ref(false)
const logs = ref([])

// 模板引用
const remoteVideo = ref(null)
const remoteCanvas = ref(null)
const videoContainer = ref(null)

// 分片数据重组
const chunkBuffers = new Map() // frameId -> { chunks: Map, totalChunks: number, totalSize: number, dataType: string }

// 掉帧统计
const frameStats = {
  h264: { received: 0, completed: 0, dropped: 0, lastFrameId: 0 },
  audio: { received: 0, completed: 0, dropped: 0, lastFrameId: 0 }
}

// 🔧 分片缓冲区管理
const bufferManager = {
  maxPendingFrames: 5, // 允许5帧缓冲
  maxBufferAge: 5000,  // 5秒超时
  lastCleanup: Date.now()
}

// 设置Clean播放器日志回调
setCleanLogCallback((level, message) => {
  addLog(level, message)
})

// 🔧 正常的缓冲区清理
function cleanupOldBuffers() {
  const now = Date.now()

  // 清理超时的缓冲区
  for (const [frameId, frameBuffer] of chunkBuffers.entries()) {
    if (now - frameBuffer.timestamp > bufferManager.maxBufferAge) {
      console.warn(`🚀 清理超时缓冲区: ${frameBuffer.dataType} 帧 ${frameId}`)

      // 更新掉帧统计
      const stats = frameStats[frameBuffer.dataType]
      if (stats) {
        stats.dropped++
      }

      chunkBuffers.delete(frameId)
    }
  }

  bufferManager.lastCleanup = now
}

// 分片数据处理函数
function handleChunkMessage(message) {
  if (message.type === 'chunk_header') {
    console.log(`🔧 收到分片头: frameId=${message.frameId}, type=${message.dataType}, chunks=${message.totalChunks}, size=${message.totalSize}`)

    // 🔧 正常缓冲区清理
    cleanupOldBuffers()

    // 更新统计信息
    const stats = frameStats[message.dataType]
    if (stats) {
      stats.received++

      // 检查是否有跳帧
      if (message.frameId > stats.lastFrameId + 1 && stats.lastFrameId > 0) {
        const skipped = message.frameId - stats.lastFrameId - 1
        stats.dropped += skipped
        console.warn(`🔧 检测到 ${message.dataType} 跳帧: ${skipped} 帧 (${stats.lastFrameId + 1} -> ${message.frameId})`)
      }
      stats.lastFrameId = message.frameId
    }

    // 初始化分片缓冲区
    chunkBuffers.set(message.frameId, {
      chunks: new Map(),
      totalChunks: message.totalChunks,
      totalSize: message.totalSize,
      dataType: message.dataType,
      receivedChunks: 0,
      timestamp: message.timestamp || Date.now()
    })


  } else {
    console.log(`🔧 未知消息类型:`, message)
  }
}

// 处理分片数据的二进制部分
function handleChunkData(uint8Array) {
  try {
    // 处理新的二进制分片格式
    if (uint8Array.length >= 12) {
      // 读取头部信息（小端序）
      const dataView = new DataView(uint8Array.buffer, uint8Array.byteOffset)
      const frameId = dataView.getUint32(0, true) // 小端序
      const chunkIndex = dataView.getUint32(4, true)
      const chunkSize = dataView.getUint32(8, true)

      // 验证数据完整性
      if (uint8Array.length === 12 + chunkSize) {
        const frameBuffer = chunkBuffers.get(frameId)
        if (!frameBuffer) {
          console.warn(`🔧 收到未知帧的分片数据: frameId=${frameId}, chunkIndex=${chunkIndex}, 分片头丢失，跳过`)
          return
        }

        // 提取实际数据
        const binaryData = uint8Array.slice(12)

        frameBuffer.chunks.set(chunkIndex, binaryData)
        frameBuffer.receivedChunks++

        // 调试信息
        if (frameBuffer.receivedChunks === 1) {
          console.log(`🔧 开始接收 ${frameBuffer.dataType} 帧 ${frameId}: ${frameBuffer.totalChunks} 分片, ${frameBuffer.totalSize} 字节`)
        }

        // 检查是否收到所有分片
        if (frameBuffer.receivedChunks === frameBuffer.totalChunks) {
          console.log(`🔧 完成接收 ${frameBuffer.dataType} 帧 ${frameId}: ${frameBuffer.receivedChunks}/${frameBuffer.totalChunks} 分片，开始重组`)
          reassembleFrame(frameId, frameBuffer)
          chunkBuffers.delete(frameId)
        } else {
          console.log(`🔧 分片进度 ${frameBuffer.dataType} 帧 ${frameId}: ${frameBuffer.receivedChunks}/${frameBuffer.totalChunks}`)
        }
        return
      }
    }

    // 尝试旧的JSON+二进制格式作为后备
    const text = new TextDecoder().decode(uint8Array)
    const separatorIndex = text.indexOf('\n')

    if (separatorIndex > 0) {
      const jsonPart = text.substring(0, separatorIndex)
      const chunkInfo = JSON.parse(jsonPart)

      if (chunkInfo.type === 'chunk_data') {
        const binaryDataStart = separatorIndex + 1
        const binaryData = uint8Array.slice(binaryDataStart)

        const frameBuffer = chunkBuffers.get(chunkInfo.frameId)
        if (!frameBuffer) {
          console.warn('收到未知帧的分片数据:', chunkInfo.frameId)
          return
        }

        if (binaryData.length === chunkInfo.chunkSize) {
          frameBuffer.chunks.set(chunkInfo.chunkIndex, binaryData)
          frameBuffer.receivedChunks++

          if (frameBuffer.receivedChunks === frameBuffer.totalChunks) {
            reassembleFrame(chunkInfo.frameId, frameBuffer)
            chunkBuffers.delete(chunkInfo.frameId)
          }
          return
        }
      }
    }
  } catch (e) {
    console.warn('分片数据解析失败:', e.message)
  }
}

// 重组完整帧
function reassembleFrame(frameId, frameBuffer) {
  const { chunks, dataType, totalChunks } = frameBuffer
  let { totalSize } = frameBuffer

  // 验证所有分片都已收到
  if (chunks.size !== totalChunks) {
    console.error(`分片不完整: ${chunks.size}/${totalChunks} for frame ${frameId}`)
    return
  }

  // 如果总大小未知，计算实际大小
  if (totalSize === 0) {
    totalSize = 0
    for (let i = 0; i < totalChunks; i++) {
      const chunk = chunks.get(i)
      if (chunk) {
        totalSize += chunk.length
      }
    }
    console.log(`🔧 动态计算总大小: ${totalSize} 字节`)
  }

  const completeData = new Uint8Array(totalSize)
  let offset = 0

  // 按顺序组装分片
  for (let i = 0; i < totalChunks; i++) {
    const chunk = chunks.get(i)
    if (!chunk) {
      console.error(`缺少分片 ${i} for frame ${frameId}`)
      return
    }

    if (offset + chunk.length > totalSize) {
      console.error(`分片大小超出预期: frame ${frameId}, chunk ${i}, offset=${offset}, chunkSize=${chunk.length}, totalSize=${totalSize}`)
      return
    }

    completeData.set(chunk, offset)
    offset += chunk.length
  }

  // 验证总大小
  if (offset !== totalSize) {
    console.error(`重组后大小不匹配: 期望 ${totalSize}, 实际 ${offset}`)
    return
  }

  // 根据数据类型处理完整帧
  try {
    console.log(`🔧 重组完成: ${dataType} 帧 ${frameId}, ${totalSize} 字节 (${totalChunks} 分片)`)

    // 更新完成统计
    const stats = frameStats[dataType]
    if (stats) {
      stats.completed++
    }

    if (dataType === 'h264') {
      // 验证H264数据完整性
      const hasAnnexB = completeData.length >= 4 &&
        completeData[0] === 0x00 && completeData[1] === 0x00 &&
        (completeData[2] === 0x00 && completeData[3] === 0x01 || completeData[2] === 0x01)

      console.log(`🔧 H264重组数据: ${totalSize}字节, Annex-B格式: ${hasAnnexB}`)
      handleDataChannelH264Data(completeData)
    } else if (dataType === 'audio') {
      // 音频数据需要去掉AUDIO:头部
      if (completeData.length > 6) {
        const headerText = new TextDecoder().decode(completeData.slice(0, 6))
        if (headerText === 'AUDIO:') {
          const audioData = completeData.slice(6)
          console.log(`🔧 音频重组数据: ${audioData.length}字节 (去掉AUDIO:头部)`)
          handleDataChannelAudioData(audioData)
        } else {
          // 直接处理音频数据（可能没有AUDIO:头部）
          console.log(`🔧 音频重组数据: ${completeData.length}字节 (无AUDIO:头部)`)
          handleDataChannelAudioData(completeData)
        }
      }
    }
  } catch (error) {
    console.error(`🔧 处理重组帧失败: ${error.message}`)
  }
}

// 日志函数
function addLog(type, message) {
  const timestamp = new Date().toLocaleTimeString()
  logs.value.unshift({
    time: timestamp,
    type: type,
    message: message
  })

  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }

  console.log(`[${type}] ${message}`)
}

function clearLogs() {
  logs.value = []
}

function getStatusType(status) {
  if (status.includes('已连接') || status.includes('成功')) return 'success'
  if (status.includes('连接中') || status.includes('等待')) return 'warning'
  if (status.includes('失败') || status.includes('错误')) return 'danger'
  return 'info'
}

// 设置DataChannel事件
function setupDataChannelEvents() {
  const dc = dataChannel.value

  dc.onopen = () => {
    addLog('SUCCESS', '📡 DataChannel已打开，可以发送输入事件')
  }

  dc.onclose = () => {
    addLog('INFO', '📡 DataChannel已关闭')
  }

  dc.onerror = (error) => {
    addLog('ERROR', `📡 DataChannel错误: ${error}`)
  }

  dc.onmessage = (event) => {
    // 调试消息类型
    console.log(`🔧 收到DataChannel消息: 类型=${typeof event.data}, ArrayBuffer=${event.data instanceof ArrayBuffer}, 大小=${event.data.byteLength || event.data.length || 'N/A'}`)

    // 检查是否是二进制数据
    if (event.data instanceof ArrayBuffer) {
      const uint8Array = new Uint8Array(event.data)

      // 检查是否是新的二进制分片格式（至少12字节头部）
      if (uint8Array.length >= 12) {
        const dataView = new DataView(uint8Array.buffer, uint8Array.byteOffset)
        const possibleFrameId = dataView.getUint32(0, true)
        const possibleChunkIndex = dataView.getUint32(4, true)
        const possibleChunkSize = dataView.getUint32(8, true)

        // 验证是否是合理的分片数据格式
        if (possibleFrameId > 0 && possibleFrameId < 1000000 &&
            possibleChunkIndex < 100 && // 最多100个分片
            possibleChunkSize > 0 && possibleChunkSize <= 15000 && // 分片大小合理
            uint8Array.length === 12 + possibleChunkSize) { // 总大小匹配

          console.log(`🔧 检测到分片数据: frameId=${possibleFrameId}, chunkIndex=${possibleChunkIndex}, size=${possibleChunkSize}`)
          handleChunkData(uint8Array)
          return
        }
      }

      // 尝试解析为旧格式分片数据（包含JSON头的格式）
      try {
        const text = new TextDecoder().decode(uint8Array)
        const separatorIndex = text.indexOf('\n')

        if (separatorIndex > 0) {
          const jsonPart = text.substring(0, separatorIndex)
          const chunkInfo = JSON.parse(jsonPart)

          if (chunkInfo.type === 'chunk_data') {
            // 这是旧格式分片数据
            handleChunkData(uint8Array)
            return
          }
        }
      } catch (e) {
        // 不是分片数据，继续处理为普通数据
      }

      // 检查数据类型
      const headerText = new TextDecoder().decode(uint8Array.slice(0, 6))

      if (headerText === 'AUDIO:') {
        // 音频数据
        const audioData = uint8Array.slice(6) // 去掉"AUDIO:"头部
        handleDataChannelAudioData(audioData)
      } else {
        // 视频数据（H264）
        handleDataChannelH264Data(uint8Array)
      }
    } else {
      // 文本消息 - 可能是分片头部信息
      try {
        const message = JSON.parse(event.data)
        console.log(`🔧 收到JSON消息:`, message)
        handleChunkMessage(message)
      } catch (e) {
        // 普通文本消息
        addLog('INFO', `📡 收到DataChannel文本消息: ${event.data}`)
      }
    }
  }
}

// WebRTC连接函数
async function connectToServer() {
  if (connecting.value || connected.value) {
    return
  }

  connecting.value = true
  connectionStatus.value = '连接中...'
  addLog('INFO', `开始连接到服务器: ${serverUrl.value}`)

  try {
    // 创建PeerConnection
    peerConnection.value = new RTCPeerConnection({
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' }
      ]
    })

    // 设置事件监听器
    setupPeerConnectionEvents()

    // 监听来自服务器的DataChannel（服务器发送offer时会创建DataChannel）
    peerConnection.value.ondatachannel = (event) => {
      addLog('SUCCESS', `📡 接收到DataChannel: ${event.channel.label}`)
      dataChannel.value = event.channel
      setupDataChannelEvents()
    }

    // 从服务器获取offer
    addLog('INFO', '正在获取服务器offer...')
    const offerResponse = await fetch(`${serverUrl.value}/offer`, {
      method: 'GET',
      headers: { 'Content-Type': 'application/json' }
    })

    if (!offerResponse.ok) {
      throw new Error(`HTTP ${offerResponse.status}: ${offerResponse.statusText}`)
    }

    const offerData = await offerResponse.json()
    addLog('SUCCESS', '收到服务器offer')

    // 设置远程描述
    await peerConnection.value.setRemoteDescription({
      type: 'offer',
      sdp: offerData.sdp
    })
    addLog('SUCCESS', '设置远程描述成功')

    // 创建并发送answer
    const answer = await peerConnection.value.createAnswer()
    await peerConnection.value.setLocalDescription(answer)
    addLog('SUCCESS', '创建本地answer成功')

    // 发送answer到服务器
    const answerResponse = await fetch(`${serverUrl.value}/answer`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'answer',
        sdp: answer.sdp
      })
    })

    if (!answerResponse.ok) {
      throw new Error(`发送answer失败: HTTP ${answerResponse.status}`)
    }

    addLog('SUCCESS', 'Answer发送成功，等待连接建立...')

  } catch (error) {
    addLog('ERROR', `连接失败: ${error.message}`)
    connecting.value = false
    connectionStatus.value = '连接失败'

    if (peerConnection.value) {
      peerConnection.value.close()
      peerConnection.value = null
    }
  }
}

function disconnectFromServer() {
  if (peerConnection.value) {
    peerConnection.value.close()
    peerConnection.value = null
  }

  connected.value = false
  connecting.value = false
  connectionStatus.value = '未连接'
  isReceivingVideo.value = false
  isReceivingAudio.value = false

  addLog('INFO', '已断开连接')
}

// 请求关键帧（解决花屏）
async function handleRequestKeyFrame() {
  try {
    // 重置播放器来强制重新配置
    // 重置Clean播放器
    await initCleanH264Player(remoteCanvas.value, { width: 1920, height: 1080 })
    addLog('SUCCESS', '🔑 已重置播放器')
  } catch (error) {
    addLog('ERROR', `❌ 请求关键帧失败: ${error.message}`)
  }
}

// 重置播放器（解决严重花屏）
async function handleResetPlayer() {
  try {
    await initCleanH264Player(remoteCanvas.value, { width: 1920, height: 1080 })
    addLog('SUCCESS', '🔄 播放器已重置')
  } catch (error) {
    addLog('ERROR', `❌ 重置播放器失败: ${error.message}`)
  }
}

// 诊断解码器状态
function handleDiagnoseDecoder() {
  try {
    addLog('INFO', '🔍 开始诊断解码器状态...')
    const diagnosis = diagnoseDecoder()

    // 根据诊断结果给出建议
    if (!diagnosis.webCodecsSupport) {
      addLog('ERROR', '❌ 浏览器不支持WebCodecs API，请使用Chrome 94+')
    } else if (!diagnosis.decoderExists) {
      addLog('WARNING', '⚠️ 解码器未创建，请先初始化播放器')
    } else if (diagnosis.decoderState === 'closed') {
      addLog('WARNING', '⚠️ 解码器已关闭，建议重置播放器')
    } else if (!diagnosis.isConfigured) {
      addLog('WARNING', '⚠️ 解码器未配置，可能是编码参数不兼容')
      addLog('INFO', '💡 建议: 使用 screen-capture-fixed.bat 脚本进行屏幕捕获')
    } else {
      addLog('SUCCESS', '✅ 解码器状态正常')
    }

  } catch (error) {
    addLog('ERROR', `❌ 诊断失败: ${error.message}`)
  }
}

// 测试连接功能
function testConnection() {
  if (!connected.value) {
    addLog('ERROR', '❌ 连接未建立，无法测试')
    return
  }

  addLog('INFO', '🧪 开始测试WebRTC连接...')

  // 测试PeerConnection状态
  const pc = peerConnection.value
  addLog('INFO', `🔗 PeerConnection状态: ${pc.connectionState}`)
  addLog('INFO', `🧊 ICE连接状态: ${pc.iceConnectionState}`)
  addLog('INFO', `📡 信令状态: ${pc.signalingState}`)

  // 测试轨道状态
  const senders = pc.getSenders()
  const receivers = pc.getReceivers()
  addLog('INFO', `📤 发送器数量: ${senders.length}`)
  addLog('INFO', `📥 接收器数量: ${receivers.length}`)

  receivers.forEach((receiver, index) => {
    const track = receiver.track
    if (track) {
      addLog('INFO', `📥 接收器${index}: ${track.kind} - ${track.readyState} - 启用:${track.enabled} - 静音:${track.muted}`)
    }
  })

  // 测试视频元素状态
  const video = remoteVideo.value
  if (video) {
    addLog('INFO', `📺 视频元素: readyState=${video.readyState}, paused=${video.paused}, currentTime=${video.currentTime}`)
    addLog('INFO', `📺 视频尺寸: ${video.videoWidth}x${video.videoHeight}`)
    addLog('INFO', `📺 视频源: ${video.srcObject ? '已设置' : '未设置'}`)

    if (video.srcObject) {
      const stream = video.srcObject
      addLog('INFO', `📺 流状态: active=${stream.active}, tracks=${stream.getTracks().length}`)
      stream.getTracks().forEach((track, index) => {
        addLog('INFO', `📺 流轨道${index}: ${track.kind} - ${track.readyState} - 启用:${track.enabled}`)
      })
    }
  }

  addLog('SUCCESS', '✅ 连接测试完成')
}

// 取消视频静音
function unmuteVideo() {
  const video = remoteVideo.value
  if (!video) {
    addLog('ERROR', '❌ 视频元素不存在')
    return
  }

  addLog('INFO', '🔊 尝试取消视频静音...')

  // 取消视频元素静音
  video.muted = false

  // 尝试播放视频
  video.play().then(() => {
    addLog('SUCCESS', '✅ 视频播放成功')
  }).catch(error => {
    addLog('ERROR', `❌ 视频播放失败: ${error.message}`)

    // 如果自动播放失败，提示用户手动点击
    addLog('INFO', '💡 请手动点击视频播放按钮')
  })

  // 检查视频轨道状态
  if (video.srcObject) {
    const stream = video.srcObject
    stream.getTracks().forEach(track => {
      if (track.kind === 'video') {
        addLog('INFO', `📺 视频轨道状态: ${track.readyState}, 静音: ${track.muted}, 启用: ${track.enabled}`)
      }
    })
  }
}

// 鼠标移动节流
let lastMouseMoveTime = 0
const MOUSE_MOVE_THROTTLE = 16 // 约60fps

// 键鼠事件处理 - 异步化避免阻塞视频传输
function handleMouseEvent(event) {
  if (!connected.value) return

  event.preventDefault()

  // 🔥 关键修复：鼠标移动事件节流，减少事件频率
  if (event.type === 'mousemove') {
    const now = performance.now()
    if (now - lastMouseMoveTime < MOUSE_MOVE_THROTTLE) {
      return
    }
    lastMouseMoveTime = now
  }

  // 🔥 关键修复：异步处理输入事件，避免阻塞视频传输
  requestAnimationFrame(() => {
    const rect = event.target.getBoundingClientRect()
    const x = Math.round((event.clientX - rect.left) / rect.width * 1920) // 假设远程桌面是1920x1080
    const y = Math.round((event.clientY - rect.top) / rect.height * 1080)

    const eventData = {
      type: event.type,
      x: x,
      y: y,
      button: event.button || 0,
      timestamp: Date.now()
    }

    // 异步发送，不阻塞主线程
    setTimeout(() => {
      sendInputEvent(eventData)
      if (event.type !== 'mousemove' || Math.random() < 0.1) { // 只记录10%的鼠标移动日志
        addLog('INPUT', `🖱️ ${event.type}: (${x}, ${y})`)
      }
    }, 0)
  })
}

function handleMouseAndFocus(event) {
  // 处理鼠标点击事件
  handleMouseEvent(event)

  // 设置焦点以捕获键盘事件
  if (videoContainer.value) {
    videoContainer.value.focus()
    addLog('INFO', '🎯 视频容器已获得焦点，可以捕获键盘事件')
  }
}

function handleKeyEvent(event) {
  if (!connected.value) return

  event.preventDefault()

  // 🔥 关键修复：异步处理键盘事件，避免阻塞视频传输
  requestAnimationFrame(() => {
    const eventData = {
      type: event.type,
      key: event.key,
      code: event.code,
      keyCode: event.keyCode,
      ctrlKey: event.ctrlKey,
      shiftKey: event.shiftKey,
      altKey: event.altKey,
      timestamp: Date.now()
    }

    // 异步发送，不阻塞主线程
    setTimeout(() => {
      sendInputEvent(eventData)
      addLog('INPUT', `⌨️ ${event.type}: ${event.key}`)
    }, 0)
  })
}

// 发送输入事件到服务器
function sendInputEvent(eventData) {
  if (!connected.value) {
    addLog('ERROR', '❌ 连接未建立，无法发送输入事件')
    return false
  }

  if (!dataChannel.value || dataChannel.value.readyState !== 'open') {
    addLog('WARNING', '⚠️ DataChannel未就绪，无法发送输入事件')
    return false
  }

  try {
    const message = JSON.stringify(eventData)
    dataChannel.value.send(message)
    return true
  } catch (error) {
    addLog('ERROR', `❌ 发送输入事件失败: ${error.message}`)
    return false
  }
}

// 检查编解码器支持
function checkCodecSupport() {
  addLog('INFO', '🔍 检查浏览器编解码器支持...')

  // 检查H264支持
  const h264Codecs = [
    'video/mp4; codecs="avc1.42e01f"',
    'video/mp4; codecs="avc1.42001e"',
    'video/mp4; codecs="avc1.4d001e"',
    'video/mp4; codecs="avc1.640028"'
  ]

  h264Codecs.forEach(codec => {
    const supported = MediaRecorder.isTypeSupported ? MediaRecorder.isTypeSupported(codec) : 'unknown'
    const canPlay = document.createElement('video').canPlayType(codec)
    addLog('INFO', `📹 ${codec}: MediaRecorder=${supported}, canPlayType=${canPlay}`)
  })

  // 检查Opus音频支持
  const opusCodec = 'audio/ogg; codecs="opus"'
  const opusSupported = MediaRecorder.isTypeSupported ? MediaRecorder.isTypeSupported(opusCodec) : 'unknown'
  const opusCanPlay = document.createElement('audio').canPlayType(opusCodec)
  addLog('INFO', `🔊 ${opusCodec}: MediaRecorder=${opusSupported}, canPlayType=${opusCanPlay}`)

  // 检查WebRTC编解码器支持
  if (RTCRtpReceiver.getCapabilities) {
    const videoCapabilities = RTCRtpReceiver.getCapabilities('video')
    const audioCapabilities = RTCRtpReceiver.getCapabilities('audio')

    addLog('INFO', '📺 WebRTC视频编解码器支持:')
    videoCapabilities.codecs.forEach(codec => {
      addLog('INFO', `  - ${codec.mimeType} (${codec.clockRate}Hz)`)
    })

    addLog('INFO', '🔊 WebRTC音频编解码器支持:')
    audioCapabilities.codecs.forEach(codec => {
      addLog('INFO', `  - ${codec.mimeType} (${codec.clockRate}Hz)`)
    })
  }

  addLog('SUCCESS', '✅ 编解码器支持检查完成')
}

// 设置视频轨道数据监听（实验性）
function setupVideoTrackDataListener(track) {
  addLog('INFO', '🔍 尝试监听视频轨道数据...')

  // 方法1：尝试使用MediaStreamTrackProcessor（如果支持）
  if (window.MediaStreamTrackProcessor) {
    try {
      const processor = new MediaStreamTrackProcessor({ track })
      const reader = processor.readable.getReader()

      let frameCount = 0
      const readFrame = async () => {
        try {
          const { done, value } = await reader.read()
          if (done) {
            addLog('INFO', '📺 视频轨道数据流结束')
            return
          }

          frameCount++
          if (frameCount % 30 === 0) { // 每30帧记录一次
            addLog('SUCCESS', `📺 接收到视频帧 #${frameCount}`)
          }

          // 继续读取下一帧
          readFrame()
        } catch (error) {
          addLog('ERROR', `📺 读取视频帧失败: ${error.message}`)
        }
      }

      readFrame()
      addLog('SUCCESS', '✅ 视频轨道数据监听已启动')
    } catch (error) {
      addLog('WARNING', `⚠️ MediaStreamTrackProcessor不可用: ${error.message}`)
    }
  } else {
    addLog('WARNING', '⚠️ 浏览器不支持MediaStreamTrackProcessor')
  }
}

// 处理DataChannel接收到的H264数据
let h264TotalBytes = 0
let h264FrameCount = 0

// 处理DataChannel接收到的音频数据
let audioTotalBytes = 0
let audioFrameCount = 0
let audioContext = null
let audioQueue = []
let audioElement = null
let mediaSource = null
let sourceBuffer = null

async function handleDataChannelH264Data(data) {
  // 更新统计信息
  h264TotalBytes += data.length
  h264FrameCount++

  // 每30帧记录一次（减少日志噪音）
  if (h264FrameCount % 30 === 0) {
    addLog('SUCCESS', `📺 已接收 ${h264FrameCount} 帧，总计 ${(h264TotalBytes/1024/1024).toFixed(2)} MB`)
  }

  // 🎮 使用修复版Clean播放器处理H264数据
  try {
    const success = await handleCleanH264Data(data)
    if (success) {
      // 每100帧记录一次成功
      if (h264FrameCount % 100 === 0) {
        addLog('SUCCESS', `📺 Clean播放器处理第 ${h264FrameCount} 帧`)

        // 显示播放器统计信息
        const playerStats = getCleanStats()
        addLog('INFO', `📊 视频=${playerStats.totalFrames}帧 | 音频=${playerStats.audioFrames}帧 | 解码=${playerStats.decodedFrames} | 错误=${playerStats.errorFrames}`)
        addLog('INFO', `📺 播放状态: 初始化=${playerStats.isInitialized} | 播放中=${playerStats.isPlaying}`)
      }
    }
  } catch (error) {
    addLog('ERROR', `❌ Clean H264解码失败: ${error.message}`)
  }
}

// 处理DataChannel接收到的音频数据 - 使用同步播放器
async function handleDataChannelAudioData(data) {
  // 更新统计信息
  audioTotalBytes += data.length
  audioFrameCount++

  // 每30帧记录一次（减少日志噪音）
  if (audioFrameCount % 30 === 0) {
    addLog('SUCCESS', `🔊 已接收 ${audioFrameCount} 音频帧，总计 ${(audioTotalBytes/1024/1024).toFixed(2)} MB`)
  }

  // 🎵 使用修复版Clean播放器处理音频数据
  try {
    const success = await handleCleanAudioData(data)
    if (!success && audioFrameCount % 50 === 0) {
      addLog('WARNING', '⚠️ 音频解码失败，可能需要重新初始化')
    } else if (success && audioFrameCount % 100 === 0) {
      addLog('SUCCESS', `🎵 音频解码成功: 第${audioFrameCount}帧`)
    }
  } catch (error) {
    addLog('ERROR', `❌ 音频处理失败: ${error.message}`)
  }
}



// 设置PeerConnection事件监听器
function setupPeerConnectionEvents() {
  const pc = peerConnection.value

  // 连接状态变化
  pc.onconnectionstatechange = () => {
    const state = pc.connectionState
    addLog('INFO', `🔗 PeerConnection状态: ${state}`)

    if (state === 'connected') {
      connected.value = true
      connecting.value = false
      connectionStatus.value = '已连接'
      addLog('SUCCESS', '🎉 WebRTC连接建立成功！')
    } else if (state === 'disconnected' || state === 'failed') {
      connected.value = false
      connecting.value = false
      connectionStatus.value = '连接断开'
      addLog('WARNING', '⚠️ WebRTC连接断开')
    } else if (state === 'connecting') {
      connectionStatus.value = '正在连接...'
      addLog('INFO', '🔄 WebRTC正在建立连接...')
    }
  }

  // ICE连接状态变化
  pc.oniceconnectionstatechange = () => {
    const iceState = pc.iceConnectionState
    addLog('INFO', `🧊 ICE连接状态: ${iceState}`)

    if (iceState === 'connected' || iceState === 'completed') {
      addLog('SUCCESS', '✅ ICE连接建立成功')
    } else if (iceState === 'failed') {
      addLog('ERROR', '❌ ICE连接失败')
    } else if (iceState === 'disconnected') {
      addLog('WARNING', '⚠️ ICE连接断开')
    }
  }

  // ICE候选者收集状态
  pc.onicegatheringstatechange = () => {
    addLog('INFO', `🔍 ICE收集状态: ${pc.iceGatheringState}`)
  }

  // 信令状态变化
  pc.onsignalingstatechange = () => {
    addLog('INFO', `📡 信令状态: ${pc.signalingState}`)
  }

  // ICE候选者
  pc.onicecandidate = async (event) => {
    if (event.candidate) {
      addLog('INFO', '发送ICE候选者到服务器')
      try {
        // 后端期望的格式是 {"ice": "candidate_string"}
        const response = await fetch(`${serverUrl.value}/ice`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            ice: event.candidate.candidate
          })
        })

        if (response.ok) {
          addLog('SUCCESS', '✅ ICE候选者发送成功')
        } else {
          const errorText = await response.text()
          addLog('ERROR', `❌ ICE候选者发送失败: HTTP ${response.status} - ${errorText}`)
        }
      } catch (error) {
        addLog('ERROR', `❌ 发送ICE候选者失败: ${error.message}`)
      }
    } else {
      addLog('INFO', '🏁 ICE候选者收集完成')
    }
  }

  // 接收媒体轨道
  pc.ontrack = (event) => {
    addLog('SUCCESS', `🎥 接收到${event.track.kind}轨道！`)

    if (event.track.kind === 'video') {
      const stream = event.streams[0] || new MediaStream([event.track])

      // 重要：不要直接设置srcObject，因为这是原始H264数据
      // remoteVideo.value.srcObject = stream

      isReceivingVideo.value = true
      addLog('SUCCESS', '📺 视频轨道已接收，准备处理H264数据')

      // 调试轨道状态
      addLog('INFO', `📺 视频轨道状态: ${event.track.readyState}`)
      addLog('INFO', `📺 视频轨道启用: ${event.track.enabled}`)
      addLog('INFO', `📺 视频轨道静音: ${event.track.muted}`)
      addLog('INFO', `📺 流轨道数量: ${stream.getTracks().length}`)

      // 监听轨道状态变化
      event.track.onended = () => addLog('WARNING', '📺 视频轨道已结束')
      event.track.onmute = () => {
        addLog('WARNING', '📺 视频轨道被静音 - 这是正常的，因为我们通过DataChannel接收H264数据')
      }
      event.track.onunmute = () => addLog('SUCCESS', '📺 视频轨道取消静音')

      // 尝试监听轨道数据（实验性）
      setupVideoTrackDataListener(event.track)

      // 添加视频事件监听器
      const video = remoteVideo.value
      video.onloadstart = () => addLog('INFO', '📺 视频开始加载')
      video.onloadedmetadata = () => addLog('INFO', `📺 视频元数据加载完成 (${video.videoWidth}x${video.videoHeight})`)
      video.onloadeddata = () => addLog('INFO', '📺 视频数据加载完成')
      video.oncanplay = () => addLog('SUCCESS', '📺 视频可以开始播放')
      video.onplaying = () => addLog('SUCCESS', '📺 视频正在播放！')
      video.onerror = (e) => addLog('ERROR', `📺 视频错误: ${e.target.error?.message || '未知错误'}`)
      video.onstalled = () => addLog('WARNING', '📺 视频播放停滞')
      video.onwaiting = () => addLog('WARNING', '📺 视频等待数据')
      video.onprogress = () => addLog('INFO', '📺 视频数据正在下载')
      video.ontimeupdate = () => {
        // 只在第一次时间更新时记录
        if (video.currentTime > 0 && !video.hasLoggedTimeUpdate) {
          addLog('SUCCESS', `📺 视频时间更新: ${video.currentTime.toFixed(2)}s`)
          video.hasLoggedTimeUpdate = true
        }
      }

      // 尝试播放视频
      video.play().catch(e => {
        addLog('WARNING', `📺 视频自动播放失败: ${e.message}`)
      })

      // 定期检查视频状态
      const statusInterval = setInterval(() => {
        if (video.readyState >= 2) { // HAVE_CURRENT_DATA
          addLog('SUCCESS', `📺 视频准备就绪 (readyState: ${video.readyState})`)
          clearInterval(statusInterval)
        }
      }, 1000)

    } else if (event.track.kind === 'audio') {
      isReceivingAudio.value = true
      addLog('SUCCESS', '🔊 音频轨道已接收')

      // 调试音频轨道状态
      addLog('INFO', `🔊 音频轨道状态: ${event.track.readyState}`)
      addLog('INFO', `🔊 音频轨道启用: ${event.track.enabled}`)
    }
  }
}

// 组件挂载时初始化
onMounted(() => {
  addLog('INFO', '🚀 WebRTC远程控制系统初始化完成')
  addLog('INFO', '点击"连接"按钮开始连接到服务器')

  // 初始化修复版H264播放器
  nextTick(async () => {
    if (remoteCanvas.value) {
      try {
        const success = await initCleanH264Player(remoteCanvas.value, {
          width: 1920,
          height: 1080
        })
        if (success) {
          addLog('SUCCESS', '📺 Clean H264播放器已初始化')
        } else {
          addLog('ERROR', '❌ 简单H264播放器初始化失败')
        }
      } catch (error) {
        addLog('ERROR', `❌ 简单H264播放器初始化失败: ${error.message}`)
      }
    }
  })

  // 添加页面刷新/关闭事件处理
  window.addEventListener('beforeunload', handleBeforeUnload)

  // 定期报告掉帧统计
  setInterval(() => {
    const h264Stats = frameStats.h264
    const audioStats = frameStats.audio

    if (h264Stats.received > 0 || audioStats.received > 0) {
      console.log(`📊 帧统计 - H264: ${h264Stats.completed}/${h264Stats.received} (掉帧: ${h264Stats.dropped}), 音频: ${audioStats.completed}/${audioStats.received} (掉帧: ${audioStats.dropped})`)
    }
  }, 5000) // 每5秒报告一次
})

// 组件卸载时清理
onUnmounted(() => {
  // 移除事件监听器
  window.removeEventListener('beforeunload', handleBeforeUnload)

  // 清理播放器
  cleanupSyncedPlayer()
  addLog('INFO', '🧹 简单播放器已清理')

  // 断开连接并清理资源
  if (connected.value || connecting.value) {
    disconnectFromServer()
  }
})

// 页面刷新/关闭前处理
function handleBeforeUnload() {
  if (connected.value || connecting.value) {
    addLog('INFO', '页面刷新/关闭，断开WebRTC连接')
    disconnectFromServer()
  }
}
</script>

<style scoped>
.main-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: transparent;
  overflow: hidden;
}

.control-panel {
  flex-shrink: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 16px;
}

.control-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.connection-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.content-area {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
  min-height: 0;
}

.video-section {
  flex: 1;
  min-width: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

.video-container {
  flex: 1;
  position: relative;
  background: #000;
  border-radius: 12px;
  overflow: hidden;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
}

.video-placeholder {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #64748b;
}

.placeholder-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}



.info-panel {
  width: 320px;
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-card,
.logs-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
}

.status-card {
  flex-shrink: 0;
}

.logs-card {
  flex: 1;
  min-height: 0;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  font-weight: 600;
  color: #1e293b;
}

.status-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-weight: 500;
  color: #64748b;
}

.logs-container {
  height: 400px;
  overflow-y: auto;
  padding: 8px;
  background: #f8fafc;
  border-radius: 8px;
}

.log-entry {
  display: flex;
  gap: 8px;
  padding: 6px 8px;
  margin-bottom: 4px;
  border-radius: 6px;
  font-size: 12px;
  line-height: 1.4;
}

.log-entry.log-success {
  background: rgba(34, 197, 94, 0.1);
  border-left: 3px solid #22c55e;
}

.log-entry.log-error {
  background: rgba(239, 68, 68, 0.1);
  border-left: 3px solid #ef4444;
}

.log-entry.log-warning {
  background: rgba(245, 158, 11, 0.1);
  border-left: 3px solid #f59e0b;
}

.log-entry.log-info {
  background: rgba(59, 130, 246, 0.1);
  border-left: 3px solid #3b82f6;
}

.log-time {
  color: #64748b;
  font-weight: 500;
  min-width: 60px;
}

.log-type {
  font-weight: 600;
  min-width: 60px;
}

.log-message {
  flex: 1;
  color: #1e293b;
}

/* 深色主题适配 */
:global(.dark) .control-panel {
  background: rgba(15, 23, 42, 0.95);
  border-bottom-color: rgba(51, 65, 85, 0.6);
}

:global(.dark) .video-section,
:global(.dark) .status-card,
:global(.dark) .logs-card {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(51, 65, 85, 0.8);
}

:global(.dark) .card-header {
  color: #f1f5f9;
}

:global(.dark) .status-label {
  color: #94a3b8;
}

:global(.dark) .logs-container {
  background: rgba(15, 23, 42, 0.8);
}

:global(.dark) .log-message {
  color: #f1f5f9;
}


</style>
