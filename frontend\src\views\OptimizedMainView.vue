<template>
  <div class="optimized-main-view">
    <!-- 头部工具栏 -->
    <div class="toolbar">
      <div class="connection-section">
        <el-input
          v-model="serverUrl"
          placeholder="WebRTC服务器地址"
          style="width: 300px"
          :disabled="connecting || connected"
        >
          <template #prepend>服务器</template>
        </el-input>
        
        <el-button
          v-if="!connected"
          @click="handleConnect"
          :loading="connecting"
          type="primary"
        >
          {{ connecting ? '连接中...' : '连接服务器' }}
        </el-button>
        
        <el-button
          v-else
          @click="handleDisconnect"
          type="danger"
        >
          断开连接
        </el-button>
      </div>
      
      <div class="status-section">
        <el-tag :type="connectionStatusType" size="large">
          {{ connectionStatus }}
        </el-tag>
        
        <el-tag v-if="connected" :type="networkQualityType" size="small">
          网络: {{ networkQualityText }}
        </el-tag>
      </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 视频播放器 -->
      <div class="video-section">
        <OptimizedVideoPlayer
          ref="videoPlayer"
          :video-element="videoElement"
          :remote-stream="remoteStream"
          :stats="stats"
          :network-quality="networkQuality"
          :connected="connected"
          :connection-status="connectionStatus"
          @video-ready="onVideoReady"
          @video-error="onVideoError"
          @fullscreen-change="onFullscreenChange"
        />
      </div>
      
      <!-- 侧边栏 -->
      <div class="sidebar" v-if="!isFullscreen">
        <!-- 连接信息 -->
        <el-card class="info-card" header="连接信息">
          <div class="info-item">
            <span class="label">连接状态:</span>
            <el-tag :type="connectionStatusType" size="small">{{ connectionStatus }}</el-tag>
          </div>
          <div class="info-item">
            <span class="label">数据通道:</span>
            <el-tag :type="dataChannelOpen ? 'success' : 'danger'" size="small">
              {{ dataChannelOpen ? '已打开' : '未打开' }}
            </el-tag>
          </div>
          <div class="info-item">
            <span class="label">视频流:</span>
            <el-tag :type="videoReceived ? 'success' : 'warning'" size="small">
              {{ videoReceived ? '已接收' : '等待中' }}
            </el-tag>
          </div>
        </el-card>
        
        <!-- 实时统计 -->
        <el-card class="stats-card" header="实时统计">
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-label">视频帧数</div>
              <div class="stat-value">{{ stats.video.framesReceived }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">丢帧数</div>
              <div class="stat-value">{{ stats.video.framesDropped }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">延迟 (RTT)</div>
              <div class="stat-value">{{ Math.round(stats.video.rtt) }}ms</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">丢包数</div>
              <div class="stat-value">{{ stats.video.packetsLost }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">音频帧数</div>
              <div class="stat-value">{{ stats.audio.framesReceived }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">网络质量</div>
              <div class="stat-value">
                <el-tag :type="networkQualityType" size="small">{{ networkQualityText }}</el-tag>
              </div>
            </div>
          </div>
        </el-card>
        
        <!-- 控制面板 -->
        <el-card class="control-card" header="控制选项">
          <div class="control-options">
            <el-switch
              v-model="mouseControlEnabled"
              active-text="鼠标控制"
              :disabled="!dataChannelOpen"
            />
            
            <el-switch
              v-model="keyboardControlEnabled"
              active-text="键盘控制"
              :disabled="!dataChannelOpen"
            />
            
            <el-switch
              v-model="adaptiveBitrateEnabled"
              active-text="自适应码率"
            />
          </div>
          
          <div class="control-buttons">
            <el-button @click="requestFullscreen" size="small" type="primary">
              全屏显示
            </el-button>
            <el-button @click="toggleStats" size="small">
              {{ showDetailedStats ? '隐藏' : '显示' }}详细统计
            </el-button>
          </div>
        </el-card>
      </div>
    </div>
    
    <!-- 日志面板 (可折叠) -->
    <div class="log-panel" v-if="showLogs && !isFullscreen">
      <div class="log-header">
        <span>系统日志</span>
        <el-button @click="clearLogs" size="small" text>清空</el-button>
        <el-button @click="showLogs = false" size="small" text>隐藏</el-button>
      </div>
      <div class="log-content" ref="logContent">
        <div
          v-for="(log, index) in logs"
          :key="index"
          :class="['log-entry', `log-${log.level.toLowerCase()}`]"
        >
          <span class="log-time">{{ formatTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
    
    <!-- 底部状态栏 -->
    <div class="status-bar" v-if="!isFullscreen">
      <div class="status-left">
        <span>WebRTC优化版本 v2.0</span>
        <span v-if="connected">• 已连接到 {{ serverUrl }}</span>
      </div>
      <div class="status-right">
        <el-button @click="showLogs = !showLogs" size="small" text>
          {{ showLogs ? '隐藏' : '显示' }}日志
        </el-button>
        <el-button @click="exportLogs" size="small" text>导出日志</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useOptimizedWebRTC } from '@/composables/useOptimizedWebRTC'
import { useWebRTCStore } from '@/stores/webrtc'
import OptimizedVideoPlayer from '@/components/OptimizedVideoPlayer.vue'

// Store
const webrtcStore = useWebRTCStore()

// WebRTC composable
const {
  connected,
  connecting,
  connectionStatus,
  dataChannelOpen,
  videoReceived,
  pc,
  dc,
  videoElement,
  remoteStream,
  stats,
  networkQuality,
  connect,
  disconnect,
  sendInputEvent,
  setOnTrackReceived,
  setOnDataChannelMessage,
  setOnConnectionStateChange,
  setLogCallback
} = useOptimizedWebRTC()

// Template refs
const videoPlayer = ref(null)
const logContent = ref(null)

// 响应式数据
const serverUrl = ref('http://localhost:8080')
const isFullscreen = ref(false)
const showLogs = ref(false)
const showDetailedStats = ref(false)
const mouseControlEnabled = ref(true)
const keyboardControlEnabled = ref(true)
const adaptiveBitrateEnabled = ref(true)

// 日志系统
const logs = ref([])
const maxLogs = 1000

// 计算属性
const connectionStatusType = computed(() => {
  if (connected.value) return 'success'
  if (connecting.value) return 'warning'
  return 'danger'
})

const networkQualityType = computed(() => {
  switch (networkQuality.value) {
    case 'excellent': return 'success'
    case 'good': return 'success'
    case 'fair': return 'warning'
    case 'poor': return 'danger'
    case 'very-poor': return 'danger'
    default: return 'info'
  }
})

const networkQualityText = computed(() => {
  switch (networkQuality.value) {
    case 'excellent': return '优秀'
    case 'good': return '良好'
    case 'fair': return '一般'
    case 'poor': return '较差'
    case 'very-poor': return '很差'
    default: return '未知'
  }
})

// 方法
async function handleConnect() {
  try {
    await connect(serverUrl.value)
    ElMessage.success('连接成功！')
  } catch (error) {
    ElMessage.error(`连接失败: ${error.message}`)
  }
}

function handleDisconnect() {
  ElMessageBox.confirm('确定要断开连接吗？', '确认', {
    type: 'warning'
  }).then(() => {
    disconnect()
    ElMessage.info('已断开连接')
  }).catch(() => {
    // 用户取消
  })
}

function onVideoReady(videoInfo) {
  addLog('SUCCESS', `视频就绪: ${videoInfo.width}x${videoInfo.height}`)
}

function onVideoError(error) {
  addLog('ERROR', `视频错误: ${error}`)
}

function onFullscreenChange(fullscreen) {
  isFullscreen.value = fullscreen
}

function requestFullscreen() {
  if (videoPlayer.value) {
    videoPlayer.value.toggleFullscreen()
  }
}

function toggleStats() {
  showDetailedStats.value = !showDetailedStats.value
  if (videoPlayer.value) {
    videoPlayer.value.toggleOverlay()
  }
}

// 日志管理
function addLog(level, message) {
  const log = {
    timestamp: new Date(),
    level,
    message
  }
  
  logs.value.push(log)
  
  // 限制日志数量
  if (logs.value.length > maxLogs) {
    logs.value.shift()
  }
  
  // 自动滚动到底部
  nextTick(() => {
    if (logContent.value) {
      logContent.value.scrollTop = logContent.value.scrollHeight
    }
  })
}

function clearLogs() {
  logs.value = []
}

function formatTime(timestamp) {
  return timestamp.toLocaleTimeString()
}

function exportLogs() {
  const logText = logs.value.map(log => 
    `[${formatTime(log.timestamp)}] ${log.level}: ${log.message}`
  ).join('\n')
  
  const blob = new Blob([logText], { type: 'text/plain' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `webrtc-logs-${new Date().toISOString().slice(0, 19)}.txt`
  a.click()
  URL.revokeObjectURL(url)
}

// 设置WebRTC回调
onMounted(() => {
  // 设置日志回调
  setLogCallback(addLog)
  
  // 设置轨道接收回调
  setOnTrackReceived((kind, element) => {
    addLog('SUCCESS', `接收到${kind}轨道`)
  })
  
  // 设置数据通道消息回调
  setOnDataChannelMessage((message) => {
    addLog('DEBUG', `数据通道消息: ${message}`)
  })
  
  // 设置连接状态变化回调
  setOnConnectionStateChange((state) => {
    addLog('INFO', `连接状态: ${state}`)
  })
  
  // 初始日志
  addLog('INFO', 'WebRTC优化客户端已启动')
})

onUnmounted(() => {
  if (connected.value) {
    disconnect()
  }
})
</script>

<style scoped>
.optimized-main-view {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.connection-section {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-section {
  display: flex;
  gap: 8px;
  align-items: center;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 16px;
  padding: 16px;
  overflow: hidden;
}

.video-section {
  flex: 1;
  min-height: 0;
}

.sidebar {
  width: 300px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-card, .stats-card, .control-card {
  flex-shrink: 0;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.stat-item {
  text-align: center;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.control-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.control-buttons {
  display: flex;
  gap: 8px;
}

.log-panel {
  height: 200px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-weight: bold;
}

.log-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-entry {
  display: flex;
  gap: 8px;
  margin-bottom: 2px;
  padding: 2px 4px;
  border-radius: 2px;
}

.log-time {
  color: #666;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-success { background: #f0f9ff; color: #059669; }
.log-info { background: #f8fafc; color: #0369a1; }
.log-warning { background: #fffbeb; color: #d97706; }
.log-error { background: #fef2f2; color: #dc2626; }
.log-debug { background: #f9fafb; color: #6b7280; }

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border-top: 1px solid #e0e0e0;
  font-size: 12px;
  color: #666;
}

.status-left {
  display: flex;
  gap: 8px;
}

.status-right {
  display: flex;
  gap: 8px;
}
</style>
