<template>
  <div class="settings-view">
    <div class="settings-container">
      <!-- 设置分类标签页 -->
      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- 显示设置 -->
        <el-tab-pane label="显示设置" name="display">
          <div class="settings-section">
            <h3>主题设置</h3>
            <el-form-item label="主题模式">
              <el-radio-group v-model="settingsStore.theme" @change="settingsStore.setTheme">
                <el-radio label="light">浅色主题</el-radio>
                <el-radio label="dark">深色主题</el-radio>
                <el-radio label="auto">跟随系统</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="语言">
              <el-select v-model="settingsStore.language" @change="settingsStore.setLanguage">
                <el-option label="简体中文" value="zh-CN" />
                <el-option label="English" value="en-US" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="显示选项">
              <el-checkbox v-model="settingsStore.showVideoInfo">显示视频信息</el-checkbox>
              <el-checkbox v-model="settingsStore.showPerformanceStats">显示性能统计</el-checkbox>
            </el-form-item>
            
            <el-form-item label="日志级别">
              <el-select v-model="settingsStore.logLevel">
                <el-option label="调试" value="debug" />
                <el-option label="信息" value="info" />
                <el-option label="警告" value="warn" />
                <el-option label="错误" value="error" />
              </el-select>
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 视频设置 -->
        <el-tab-pane label="视频设置" name="video">
          <div class="settings-section">
            <h3>视频参数</h3>
            <el-form-item label="首选分辨率">
              <el-select v-model="settingsStore.videoSettings.preferredResolution">
                <el-option label="自动" value="auto" />
                <el-option label="1920x1080" value="1920x1080" />
                <el-option label="1280x720" value="1280x720" />
                <el-option label="640x360" value="640x360" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="首选帧率">
              <el-input-number
                v-model="settingsStore.videoSettings.preferredFrameRate"
                :min="15"
                :max="60"
                :step="5" />
            </el-form-item>
            
            <el-form-item label="硬件加速">
              <el-select v-model="settingsStore.videoSettings.hardwareAcceleration">
                <el-option label="优先硬件" value="prefer-hardware" />
                <el-option label="优先软件" value="prefer-software" />
                <el-option label="无偏好" value="no-preference" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="低延迟模式">
              <el-switch v-model="settingsStore.videoSettings.lowLatencyMode" />
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 音频设置 -->
        <el-tab-pane label="音频设置" name="audio">
          <div class="settings-section">
            <h3>音频参数</h3>
            <el-form-item label="采样率">
              <el-select v-model="settingsStore.audioSettings.sampleRate">
                <el-option label="44100 Hz" :value="44100" />
                <el-option label="48000 Hz" :value="48000" />
                <el-option label="22050 Hz" :value="22050" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="声道数">
              <el-radio-group v-model="settingsStore.audioSettings.channels">
                <el-radio :label="1">单声道</el-radio>
                <el-radio :label="2">立体声</el-radio>
              </el-radio-group>
            </el-form-item>
            
            <el-form-item label="码率">
              <el-select v-model="settingsStore.audioSettings.bitrate">
                <el-option label="64 kbps" :value="64" />
                <el-option label="128 kbps" :value="128" />
                <el-option label="192 kbps" :value="192" />
                <el-option label="256 kbps" :value="256" />
              </el-select>
            </el-form-item>
            
            <el-form-item label="音频增强">
              <el-checkbox v-model="settingsStore.audioSettings.enableNoiseSuppression">
                噪声抑制
              </el-checkbox>
              <el-checkbox v-model="settingsStore.audioSettings.enableEchoCancellation">
                回声消除
              </el-checkbox>
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 网络设置 -->
        <el-tab-pane label="网络设置" name="network">
          <div class="settings-section">
            <h3>连接参数</h3>
            <el-form-item label="连接超时">
              <el-input-number
                v-model="settingsStore.networkSettings.connectionTimeout"
                :min="5000"
                :max="30000"
                :step="1000" />
              <span class="unit">毫秒</span>
            </el-form-item>
            
            <el-form-item label="重连次数">
              <el-input-number
                v-model="settingsStore.networkSettings.reconnectAttempts"
                :min="1"
                :max="10" />
            </el-form-item>
            
            <el-form-item label="重连延迟">
              <el-input-number
                v-model="settingsStore.networkSettings.reconnectDelay"
                :min="1000"
                :max="10000"
                :step="500" />
              <span class="unit">毫秒</span>
            </el-form-item>
            
            <el-form-item label="丢包阈值">
              <el-input-number
                v-model="settingsStore.networkSettings.packetLossThreshold"
                :min="1"
                :max="20" />
              <span class="unit">%</span>
            </el-form-item>
            
            <el-form-item label="延迟阈值">
              <el-input-number
                v-model="settingsStore.networkSettings.latencyThreshold"
                :min="50"
                :max="1000"
                :step="50" />
              <span class="unit">毫秒</span>
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 控制设置 -->
        <el-tab-pane label="控制设置" name="control">
          <div class="settings-section">
            <h3>鼠标键盘</h3>
            <el-form-item label="鼠标灵敏度">
              <el-slider
                v-model="settingsStore.controlSettings.mouseSensitivity"
                :min="0.1"
                :max="3.0"
                :step="0.1"
                show-input />
            </el-form-item>
            
            <el-form-item label="键盘重复延迟">
              <el-input-number
                v-model="settingsStore.controlSettings.keyboardRepeatDelay"
                :min="100"
                :max="1000"
                :step="50" />
              <span class="unit">毫秒</span>
            </el-form-item>
            
            <el-form-item label="键盘重复速率">
              <el-input-number
                v-model="settingsStore.controlSettings.keyboardRepeatRate"
                :min="10"
                :max="200"
                :step="10" />
              <span class="unit">毫秒</span>
            </el-form-item>
            
            <el-form-item label="控制选项">
              <el-checkbox v-model="settingsStore.controlSettings.enableMouseAcceleration">
                启用鼠标加速
              </el-checkbox>
              <el-checkbox v-model="settingsStore.controlSettings.enableKeyboardShortcuts">
                启用键盘快捷键
              </el-checkbox>
            </el-form-item>
          </div>
        </el-tab-pane>

        <!-- 开发者设置 -->
        <el-tab-pane label="开发者" name="developer">
          <div class="settings-section">
            <h3>调试选项</h3>
            <el-form-item label="调试模式">
              <el-switch v-model="settingsStore.developerSettings.enableDebugMode" />
            </el-form-item>
            
            <el-form-item label="WebRTC统计">
              <el-switch v-model="settingsStore.developerSettings.showWebRTCStats" />
            </el-form-item>
            
            <el-form-item label="详细日志">
              <el-switch v-model="settingsStore.developerSettings.enableVerboseLogging" />
            </el-form-item>
            
            <el-form-item label="保存日志到文件">
              <el-switch v-model="settingsStore.developerSettings.saveLogsToFile" />
            </el-form-item>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 操作按钮 -->
      <div class="settings-actions">
        <el-button @click="resetSettings" type="danger">恢复默认</el-button>
        <el-button @click="exportSettings" type="info">导出设置</el-button>
        <el-button @click="importSettings" type="primary">导入设置</el-button>
        <input
          ref="fileInput"
          type="file"
          accept=".json"
          style="display: none"
          @change="handleFileImport" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useSettingsStore } from '../stores/settings'

const settingsStore = useSettingsStore()

// 当前活动标签页
const activeTab = ref('display')

// 文件输入引用
const fileInput = ref(null)

// 方法
function resetSettings() {
  ElMessageBox.confirm(
    '确定要恢复所有设置到默认值吗？此操作不可撤销。',
    '确认重置',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    settingsStore.resetToDefaults()
    ElMessage.success('设置已恢复到默认值')
  }).catch(() => {
    // 用户取消
  })
}

function exportSettings() {
  const settings = {
    theme: settingsStore.theme,
    language: settingsStore.language,
    showVideoInfo: settingsStore.showVideoInfo,
    showPerformanceStats: settingsStore.showPerformanceStats,
    logLevel: settingsStore.logLevel,
    videoSettings: settingsStore.videoSettings,
    audioSettings: settingsStore.audioSettings,
    networkSettings: settingsStore.networkSettings,
    controlSettings: settingsStore.controlSettings,
    developerSettings: settingsStore.developerSettings,
    exportTime: new Date().toISOString()
  }

  const dataStr = JSON.stringify(settings, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })

  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `webrtc-settings-${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
  link.click()

  ElMessage.success('设置已导出')
}

function importSettings() {
  fileInput.value.click()
}

function handleFileImport(event) {
  const file = event.target.files[0]
  if (!file) return

  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const settings = JSON.parse(e.target.result)
      
      // 验证设置格式
      if (typeof settings !== 'object') {
        throw new Error('无效的设置文件格式')
      }

      // 应用设置
      if (settings.theme) settingsStore.setTheme(settings.theme)
      if (settings.language) settingsStore.setLanguage(settings.language)
      if (typeof settings.showVideoInfo === 'boolean') settingsStore.showVideoInfo = settings.showVideoInfo
      if (typeof settings.showPerformanceStats === 'boolean') settingsStore.showPerformanceStats = settings.showPerformanceStats
      if (settings.logLevel) settingsStore.logLevel = settings.logLevel
      
      if (settings.videoSettings) settingsStore.updateVideoSettings(settings.videoSettings)
      if (settings.audioSettings) settingsStore.updateAudioSettings(settings.audioSettings)
      if (settings.networkSettings) settingsStore.updateNetworkSettings(settings.networkSettings)
      if (settings.controlSettings) settingsStore.updateControlSettings(settings.controlSettings)
      if (settings.developerSettings) settingsStore.updateDeveloperSettings(settings.developerSettings)

      ElMessage.success('设置已导入')
    } catch (error) {
      ElMessage.error(`导入设置失败: ${error.message}`)
    }
  }
  reader.readAsText(file)
  
  // 清空文件输入
  event.target.value = ''
}
</script>

<style scoped>
.settings-view {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.settings-container {
  max-width: 800px;
  margin: 0 auto;
}

.settings-tabs {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section {
  padding: 16px 0;
}

.settings-section h3 {
  margin: 0 0 20px 0;
  color: var(--el-text-color-primary);
  font-size: 16px;
  font-weight: 600;
  border-bottom: 1px solid var(--el-border-color-lighter);
  padding-bottom: 8px;
}

.el-form-item {
  margin-bottom: 20px;
}

.unit {
  margin-left: 8px;
  color: var(--el-text-color-regular);
  font-size: 12px;
}

.settings-actions {
  margin-top: 24px;
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-actions .el-button {
  margin: 0 8px;
}

/* 深色主题适配 */
:global(.dark) .settings-tabs,
:global(.dark) .settings-actions {
  background: var(--el-bg-color-page);
}
</style>
