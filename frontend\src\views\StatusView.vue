<template>
  <div class="stats-view">
    <div class="stats-grid">
      <!-- 系统概览卡片 -->
      <el-card class="stats-card overview-card">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>系统概览</span>
          </div>
        </template>

        <div class="overview-content">
          <div class="overview-item">
            <div class="overview-icon success">
              <el-icon><CircleCheck /></el-icon>
            </div>
            <div class="overview-info">
              <div class="overview-title">系统运行正常</div>
              <div class="overview-subtitle">运行时间: {{ formatUptime(systemUptime) }}</div>
            </div>
          </div>

          <div class="overview-stats">
            <div class="overview-stat">
              <div class="stat-number">{{ totalConnections }}</div>
              <div class="stat-desc">总连接数</div>
            </div>
            <div class="overview-stat">
              <div class="stat-number">{{ totalDataTransferred }}</div>
              <div class="stat-desc">数据传输(MB)</div>
            </div>
            <div class="overview-stat">
              <div class="stat-number">{{ averageLatency }}</div>
              <div class="stat-desc">平均延迟(ms)</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 视频统计卡片 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><VideoCamera /></el-icon>
            <span>视频统计</span>
          </div>
        </template>
        
        <div class="stats-content">
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.videoStats.resolution }}</div>
              <div class="stat-label">分辨率</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.videoStats.fps }}</div>
              <div class="stat-label">FPS</div>
            </div>
          </div>
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.videoStats.framesReceived }}</div>
              <div class="stat-label">已接收帧数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.videoStats.framesDropped }}</div>
              <div class="stat-label">丢帧数</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 音频统计卡片 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><Microphone /></el-icon>
            <span>音频统计</span>
          </div>
        </template>
        
        <div class="stats-content">
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.audioStats.sampleRate }}</div>
              <div class="stat-label">采样率 (Hz)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.audioStats.channels }}</div>
              <div class="stat-label">声道数</div>
            </div>
          </div>
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.audioStats.framesPlayed }}</div>
              <div class="stat-label">已播放帧数</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.audioStats.decodeErrors }}</div>
              <div class="stat-label">解码错误</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 性能统计卡片 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>性能统计</span>
          </div>
        </template>
        
        <div class="stats-content">
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.performanceStats.networkLatency }}</div>
              <div class="stat-label">网络延迟 (ms)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.performanceStats.packetLoss }}</div>
              <div class="stat-label">丢包率 (%)</div>
            </div>
          </div>
          <div class="stat-row">
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.performanceStats.cpuUsage }}</div>
              <div class="stat-label">CPU使用率 (%)</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ webrtcStore.performanceStats.memoryUsage }}</div>
              <div class="stat-label">内存使用 (MB)</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 系统信息卡片 -->
      <el-card class="status-card system-info">
        <template #header>
          <div class="card-header">
            <el-icon><Monitor /></el-icon>
            <span>系统信息</span>
          </div>
        </template>
        
        <div class="system-content">
          <div class="info-item">
            <span class="info-label">浏览器</span>
            <span class="info-value">{{ systemInfo.browser }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">WebRTC支持</span>
            <span class="info-value">{{ systemInfo.webrtcSupport ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">WebCodecs支持</span>
            <span class="info-value">{{ systemInfo.webcodecsSupport ? '是' : '否' }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">屏幕分辨率</span>
            <span class="info-value">{{ systemInfo.screenResolution }}</span>
          </div>
          <div class="info-item">
            <span class="info-label">运行时间</span>
            <span class="info-value">{{ formatUptime(uptime) }}</span>
          </div>
        </div>
      </el-card>

      <!-- 日志统计卡片 -->
      <el-card class="status-card">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>日志统计</span>
          </div>
        </template>
        
        <div class="log-stats">
          <div class="log-stat-item" v-for="(count, type) in logsStore.logStats" :key="type">
            <div class="log-count" :class="`log-${type}`">{{ count }}</div>
            <div class="log-type">{{ getLogTypeName(type) }}</div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  VideoCamera,
  Microphone,
  DataAnalysis,
  Monitor,
  Document,
  CircleCheck
} from '@element-plus/icons-vue'

import { useWebRTCStore } from '../stores/webrtc'
import { useLogsStore } from '../stores/logs'

const webrtcStore = useWebRTCStore()
const logsStore = useLogsStore()

// 系统信息
const systemInfo = ref({
  browser: '',
  webrtcSupport: false,
  webcodecsSupport: false,
  screenResolution: ''
})

// 运行时间
const uptime = ref(0)
let uptimeTimer = null

// 系统统计数据
const systemUptime = ref(0)
const totalConnections = ref(0)
const totalDataTransferred = ref(0)
const averageLatency = ref(0)

// 模拟统计数据更新
let statsTimer = null

// 方法
function detectSystemInfo() {
  // 检测浏览器
  const userAgent = navigator.userAgent
  if (userAgent.includes('Chrome')) {
    systemInfo.value.browser = 'Chrome'
  } else if (userAgent.includes('Firefox')) {
    systemInfo.value.browser = 'Firefox'
  } else if (userAgent.includes('Safari')) {
    systemInfo.value.browser = 'Safari'
  } else if (userAgent.includes('Edge')) {
    systemInfo.value.browser = 'Edge'
  } else {
    systemInfo.value.browser = '未知浏览器'
  }

  // 检测WebRTC支持
  systemInfo.value.webrtcSupport = !!(window.RTCPeerConnection || window.webkitRTCPeerConnection)

  // 检测WebCodecs支持
  systemInfo.value.webcodecsSupport = !!(window.VideoDecoder && window.AudioDecoder)

  // 获取屏幕分辨率
  systemInfo.value.screenResolution = `${screen.width}x${screen.height}`
}

function getLogTypeName(type) {
  const typeMap = {
    total: '总计',
    debug: '调试',
    info: '信息',
    success: '成功',
    warning: '警告',
    error: '错误',
    input: '输入'
  }
  return typeMap[type] || type
}

// 格式化运行时间
function formatUptime(seconds) {
  const days = Math.floor(seconds / 86400)
  const hours = Math.floor((seconds % 86400) / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (days > 0) {
    return `${days}天 ${hours}小时 ${minutes}分钟`
  } else if (hours > 0) {
    return `${hours}小时 ${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取进度条颜色
function getProgressColor(percentage) {
  if (percentage < 50) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 更新统计数据
function updateStats() {
  systemUptime.value++
  totalConnections.value = Math.floor(Math.random() * 100) + 50
  totalDataTransferred.value = (Math.random() * 1000 + 500).toFixed(1)
  averageLatency.value = Math.floor(Math.random() * 50) + 10
}

// 生命周期
onMounted(() => {
  detectSystemInfo()

  // 启动运行时间计时器
  uptimeTimer = setInterval(() => {
    uptime.value++
  }, 1000)

  // 启动统计数据更新计时器
  statsTimer = setInterval(updateStats, 5000)
  updateStats() // 立即更新一次
})

onUnmounted(() => {
  if (uptimeTimer) {
    clearInterval(uptimeTimer)
  }
  if (statsTimer) {
    clearInterval(statsTimer)
  }
})
</script>

<style scoped>
.stats-view {
  padding: 24px;
  height: 100%;
  overflow-y: auto;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.stats-card {
  height: fit-content;
  background: rgba(255, 255, 255, 0.98);
  backdrop-filter: blur(12px);
  border-radius: 16px;
  border: 1px solid rgba(226, 232, 240, 0.6);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.12);
}

.overview-card {
  grid-column: 1 / -1;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 16px;
}

.indicator-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  flex-shrink: 0;
}

.indicator-dot.connected {
  background: var(--el-color-success);
  box-shadow: 0 0 8px var(--el-color-success);
}

.indicator-dot.connecting {
  background: var(--el-color-warning);
  animation: pulse 2s infinite;
}

.indicator-dot.disconnected {
  background: var(--el-color-info);
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.indicator-text {
  flex: 1;
}

.status-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.status-subtitle {
  font-size: 14px;
  color: var(--el-text-color-regular);
  margin-top: 4px;
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-item .label {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.detail-item .value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.stats-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-row {
  display: flex;
  gap: 16px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 16px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: var(--el-color-primary);
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.system-info .system-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  color: var(--el-text-color-regular);
  font-size: 14px;
}

.info-value {
  color: var(--el-text-color-primary);
  font-weight: 500;
}

.log-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  gap: 12px;
}

.log-stat-item {
  text-align: center;
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 8px;
}

.log-count {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 4px;
}

.log-type {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.log-debug { color: var(--el-color-info); }
.log-info { color: var(--el-color-primary); }
.log-success { color: var(--el-color-success); }
.log-warning { color: var(--el-color-warning); }
.log-error { color: var(--el-color-danger); }
.log-input { color: var(--el-color-purple); }

/* 系统概览样式 */
.overview-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.overview-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.05) 0%, rgba(22, 163, 74, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.overview-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.overview-icon.success {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
}

.overview-info {
  flex: 1;
}

.overview-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  margin-bottom: 4px;
}

.overview-subtitle {
  font-size: 14px;
  color: #64748b;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

.overview-stat {
  text-align: center;
  padding: 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.6);
}

.stat-number {
  font-size: 20px;
  font-weight: 700;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 4px;
}

.stat-desc {
  font-size: 12px;
  color: #64748b;
  font-weight: 500;
}
</style>
