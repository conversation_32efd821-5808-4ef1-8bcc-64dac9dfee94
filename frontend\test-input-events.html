<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>键鼠事件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-area {
            width: 800px;
            height: 600px;
            background: #000;
            border: 3px solid #007bff;
            border-radius: 8px;
            margin: 20px auto;
            position: relative;
            cursor: crosshair;
            outline: none;
        }
        
        .test-area:focus {
            border-color: #28a745;
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }
        
        .status {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .log-area {
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 3px;
        }
        
        .log-mouse { background: #e3f2fd; }
        .log-key { background: #f3e5f5; }
        .log-wheel { background: #e8f5e8; }
        .log-focus { background: #fff3e0; }
        
        .controls {
            text-align: center;
            margin: 20px 0;
        }
        
        button {
            padding: 10px 20px;
            margin: 0 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        .instructions {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 键鼠事件测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ul>
                <li>点击黑色测试区域获得焦点</li>
                <li>在测试区域内移动鼠标、点击、滚轮滚动</li>
                <li>按下键盘按键测试键盘事件</li>
                <li>右键点击测试右键菜单阻止</li>
                <li>所有事件都会在下方日志区域显示详细信息</li>
            </ul>
        </div>
        
        <div class="test-area" 
             tabindex="0" 
             id="testArea">
            <div class="status" id="status">
                点击获得焦点
            </div>
        </div>
        
        <div class="controls">
            <button class="btn-primary" onclick="focusTestArea()">获得焦点</button>
            <button class="btn-success" onclick="clearLogs()">清空日志</button>
            <button class="btn-danger" onclick="testSpecialKeys()">测试特殊按键</button>
        </div>
        
        <h3>📋 事件日志：</h3>
        <div class="log-area" id="logArea"></div>
    </div>

    <script>
        const testArea = document.getElementById('testArea');
        const logArea = document.getElementById('logArea');
        const status = document.getElementById('status');
        let isActive = false;

        // 日志函数
        function addLog(type, message) {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }

        // 焦点事件
        testArea.addEventListener('focus', () => {
            isActive = true;
            status.textContent = '🎮 键鼠控制已激活';
            status.style.background = 'rgba(40, 167, 69, 0.9)';
            status.style.color = 'white';
            addLog('focus', '🎮 测试区域获得焦点，键鼠控制已激活');
        });

        testArea.addEventListener('blur', () => {
            isActive = false;
            status.textContent = '点击获得焦点';
            status.style.background = 'rgba(255, 255, 255, 0.9)';
            status.style.color = 'black';
            addLog('focus', '🎮 测试区域失去焦点，键鼠控制已停用');
        });

        // 鼠标事件
        ['mousedown', 'mouseup', 'mousemove', 'click'].forEach(eventType => {
            testArea.addEventListener(eventType, (event) => {
                if (!isActive && eventType !== 'click') return;
                
                event.preventDefault();
                
                const rect = testArea.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                const normalizedX = (x / rect.width).toFixed(3);
                const normalizedY = (y / rect.height).toFixed(3);
                
                if (eventType === 'mousemove') {
                    // 降低移动事件的日志频率
                    if (Math.random() < 0.1) {
                        addLog('mouse', `🖱️ 鼠标移动: (${x.toFixed(1)}, ${y.toFixed(1)}) 归一化(${normalizedX}, ${normalizedY})`);
                    }
                } else {
                    const buttonNames = ['左键', '中键', '右键'];
                    const buttonName = buttonNames[event.button] || `按钮${event.button}`;
                    addLog('mouse', `🖱️ 鼠标${eventType}: ${buttonName} 在(${x.toFixed(1)}, ${y.toFixed(1)}) 归一化(${normalizedX}, ${normalizedY})`);
                    addLog('mouse', `🖱️ 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`);
                }
            });
        });

        // 右键菜单
        testArea.addEventListener('contextmenu', (event) => {
            event.preventDefault();
            addLog('mouse', '🖱️ 右键菜单被阻止');
        });

        // 滚轮事件
        testArea.addEventListener('wheel', (event) => {
            if (!isActive) return;
            
            event.preventDefault();
            
            const rect = testArea.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            const direction = event.deltaY > 0 ? '向下' : event.deltaY < 0 ? '向上' : '水平';
            
            addLog('wheel', `🎡 滚轮滚动: ${direction} 在(${x.toFixed(1)}, ${y.toFixed(1)}) deltaY=${event.deltaY.toFixed(1)}`);
            addLog('wheel', `🎡 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`);
        });

        // 键盘事件
        ['keydown', 'keyup'].forEach(eventType => {
            testArea.addEventListener(eventType, (event) => {
                if (!isActive) return;
                
                event.preventDefault();
                
                const actionName = eventType === 'keydown' ? '按下' : '释放';
                const keyInfo = event.key.length === 1 ? `'${event.key}'` : event.key;
                const codeInfo = event.code !== event.key ? ` (${event.code})` : '';
                
                addLog('key', `⌨️ 键盘${actionName}: ${keyInfo}${codeInfo} keyCode=${event.keyCode}`);
                addLog('key', `⌨️ 修饰键: Ctrl=${event.ctrlKey}, Shift=${event.shiftKey}, Alt=${event.altKey}, Meta=${event.metaKey}`);
                
                if (event.repeat) {
                    addLog('key', `⌨️ 重复按键事件`);
                }
            });
        });

        // 控制函数
        function focusTestArea() {
            testArea.focus();
        }

        function clearLogs() {
            logArea.innerHTML = '';
            addLog('focus', '📋 日志已清空');
        }

        function testSpecialKeys() {
            addLog('focus', '🧪 请按下以下特殊按键进行测试：');
            addLog('focus', '   • 方向键 (ArrowUp, ArrowDown, ArrowLeft, ArrowRight)');
            addLog('focus', '   • 功能键 (F1-F12)');
            addLog('focus', '   • 修饰键 (Ctrl, Shift, Alt, Meta)');
            addLog('focus', '   • 特殊键 (Enter, Escape, Tab, Backspace, Delete)');
            testArea.focus();
        }

        // 初始化
        addLog('focus', '🚀 键鼠事件测试页面已加载');
        addLog('focus', '💡 点击黑色测试区域开始测试');
    </script>
</body>
</html>
