@echo off
echo 🚀 启动兼容性高质量视频流...

echo 📺 配置1: CPU软件编码 (最兼容)
echo 使用x264软件编码器，兼容所有设备
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v libx264 ^
  -preset ultrafast ^
  -tune zerolatency ^
  -profile:v high ^
  -level 4.1 ^
  -b:v 5M ^
  -maxrate 6M ^
  -bufsize 8M ^
  -keyint_min 15 ^
  -g 30 ^
  -bf 0 ^
  -refs 1 ^
  -crf 20 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

pause

echo 📺 配置2: NVENC简化版 (如果支持)
echo 简化的NVENC参数，降低硬件要求
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_nvenc ^
  -preset fast ^
  -profile:v high ^
  -b:v 4M ^
  -maxrate 5M ^
  -bufsize 6M ^
  -g 30 ^
  -bf 0 ^
  -refs 1 ^
  -rc cbr ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

pause

echo 📺 配置3: Intel QuickSync (如果支持)
echo 使用Intel集成显卡硬件编码
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_qsv ^
  -preset fast ^
  -profile:v high ^
  -b:v 4M ^
  -maxrate 5M ^
  -bufsize 6M ^
  -g 30 ^
  -bf 0 ^
  -refs 1 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

pause

echo 📺 配置4: AMD AMF (如果支持)
echo 使用AMD显卡硬件编码
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_amf ^
  -quality speed ^
  -profile:v high ^
  -b:v 4M ^
  -maxrate 5M ^
  -bufsize 6M ^
  -g 30 ^
  -bf 0 ^
  -refs 1 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

echo ✅ 请选择适合你硬件的配置！
pause
