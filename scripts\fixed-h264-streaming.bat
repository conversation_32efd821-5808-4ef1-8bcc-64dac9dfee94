@echo off
echo 🚀 修复H264解码问题的优化流媒体配置
echo.

echo ========================================
echo 问题诊断和解决方案
echo ========================================
echo.
echo 检测到的问题:
echo ❌ H264解码失败: A key frame is required after configure()
echo ❌ 视频质量差，大量解码错误
echo ❌ 传输中断问题
echo.
echo 解决方案:
echo ✅ 强制生成IDR关键帧
echo ✅ 添加SPS/PPS参数集
echo ✅ 优化编码参数
echo ✅ 改善网络传输
echo.

echo ========================================
echo 选择测试配置
echo ========================================
echo.
echo 1. 基础修复版 (推荐开始)
echo 2. 高质量版本
echo 3. 低延迟版本
echo 4. 兼容性最大化版本
echo 5. 显示所有命令
echo.

set /p choice="请选择配置 (1-5): "

if "%choice%"=="1" (
    echo.
    echo [INFO] 启动基础修复版...
    echo [INFO] 强制每秒生成一个IDR帧，确保解码器能正常工作
    echo.
    
    REM 基础修复版 - 确保关键帧和参数集
    ffmpeg -f gdigrab -framerate 15 -i desktop ^
      -c:v libx264 ^
      -preset ultrafast ^
      -tune zerolatency ^
      -profile:v baseline ^
      -level 3.1 ^
      -b:v 1000k ^
      -maxrate 1500k ^
      -bufsize 500k ^
      -g 15 ^
      -keyint_min 15 ^
      -force_key_frames "expr:gte(t,n_forced*1)" ^
      -x264-params "scenecut=0:bframes=0:ref=1" ^
      -pix_fmt yuv420p ^
      -f rtp rtp://127.0.0.1:5000
      
) else if "%choice%"=="2" (
    echo.
    echo [INFO] 启动高质量版本...
    echo [INFO] 平衡质量和稳定性
    echo.
    
    REM 高质量版本
    ffmpeg -f gdigrab -framerate 25 -i desktop ^
      -c:v libx264 ^
      -preset fast ^
      -tune zerolatency ^
      -profile:v high ^
      -level 4.1 ^
      -b:v 2500k ^
      -maxrate 3500k ^
      -bufsize 1000k ^
      -g 25 ^
      -keyint_min 25 ^
      -force_key_frames "expr:gte(t,n_forced*1)" ^
      -x264-params "scenecut=0:bframes=0:ref=2:aq-mode=2" ^
      -pix_fmt yuv420p ^
      -f rtp rtp://127.0.0.1:5000
      
) else if "%choice%"=="3" (
    echo.
    echo [INFO] 启动低延迟版本...
    echo [INFO] 最小延迟，适合实时交互
    echo.
    
    REM 低延迟版本
    ffmpeg -f gdigrab -framerate 30 -i desktop ^
      -c:v libx264 ^
      -preset ultrafast ^
      -tune zerolatency ^
      -profile:v baseline ^
      -level 3.1 ^
      -b:v 1500k ^
      -maxrate 2000k ^
      -bufsize 300k ^
      -g 10 ^
      -keyint_min 10 ^
      -force_key_frames "expr:gte(t,n_forced*0.5)" ^
      -x264-params "scenecut=0:bframes=0:ref=1:rc-lookahead=0" ^
      -pix_fmt yuv420p ^
      -fflags nobuffer ^
      -flags low_delay ^
      -f rtp rtp://127.0.0.1:5000
      
) else if "%choice%"=="4" (
    echo.
    echo [INFO] 启动兼容性最大化版本...
    echo [INFO] 最大兼容性，确保所有浏览器都能解码
    echo.
    
    REM 兼容性最大化版本
    ffmpeg -f gdigrab -framerate 20 -i desktop ^
      -c:v libx264 ^
      -preset medium ^
      -profile:v baseline ^
      -level 3.0 ^
      -b:v 800k ^
      -maxrate 1200k ^
      -bufsize 400k ^
      -g 20 ^
      -keyint_min 20 ^
      -force_key_frames "expr:gte(t,n_forced*1)" ^
      -x264-params "scenecut=0:bframes=0:ref=1:cabac=0" ^
      -pix_fmt yuv420p ^
      -f rtp rtp://127.0.0.1:5000
      
) else (
    echo.
    echo ========================================
    echo 所有修复命令
    echo ========================================
    echo.
    
    echo 基础修复版:
    echo ffmpeg -f gdigrab -framerate 15 -i desktop -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -b:v 1000k -maxrate 1500k -bufsize 500k -g 15 -keyint_min 15 -force_key_frames "expr:gte(t,n_forced*1)" -x264-params "scenecut=0:bframes=0:ref=1" -pix_fmt yuv420p -f rtp rtp://127.0.0.1:5000
    echo.
    
    echo 高质量版本:
    echo ffmpeg -f gdigrab -framerate 25 -i desktop -c:v libx264 -preset fast -tune zerolatency -profile:v high -level 4.1 -b:v 2500k -maxrate 3500k -bufsize 1000k -g 25 -keyint_min 25 -force_key_frames "expr:gte(t,n_forced*1)" -x264-params "scenecut=0:bframes=0:ref=2:aq-mode=2" -pix_fmt yuv420p -f rtp rtp://127.0.0.1:5000
    echo.
    
    echo 低延迟版本:
    echo ffmpeg -f gdigrab -framerate 30 -i desktop -c:v libx264 -preset ultrafast -tune zerolatency -profile:v baseline -level 3.1 -b:v 1500k -maxrate 2000k -bufsize 300k -g 10 -keyint_min 10 -force_key_frames "expr:gte(t,n_forced*0.5)" -x264-params "scenecut=0:bframes=0:ref=1:rc-lookahead=0" -pix_fmt yuv420p -fflags nobuffer -flags low_delay -f rtp rtp://127.0.0.1:5000
    echo.
    
    echo 兼容性最大化版本:
    echo ffmpeg -f gdigrab -framerate 20 -i desktop -c:v libx264 -preset medium -profile:v baseline -level 3.0 -b:v 800k -maxrate 1200k -bufsize 400k -g 20 -keyint_min 20 -force_key_frames "expr:gte(t,n_forced*1)" -x264-params "scenecut=0:bframes=0:ref=1:cabac=0" -pix_fmt yuv420p -f rtp rtp://127.0.0.1:5000
    echo.
)

echo.
echo ========================================
echo 关键修复说明
echo ========================================
echo.
echo 1. force_key_frames: 强制生成IDR关键帧
echo 2. scenecut=0: 禁用场景切换检测，确保固定关键帧间隔
echo 3. bframes=0: 禁用B帧，简化解码
echo 4. baseline profile: 最大兼容性
echo 5. 固定GOP大小: 确保关键帧间隔可预测
echo.

echo ========================================
echo 预期结果
echo ========================================
echo.
echo 后端应该显示:
echo ✅ [H264] SPS received, PPS received
echo ✅ [H264] Sent key frame (XXXX bytes)
echo ✅ 定期的关键帧日志
echo.
echo 前端应该显示:
echo ✅ [SUCCESS] SPS已提取, PPS已提取
echo ✅ [SUCCESS] 解码器已使用SPS/PPS重新配置
echo ✅ [SUCCESS] 关键帧解码成功
echo ❌ 不再有 "key frame is required" 错误
echo.

echo 如果仍有问题，请检查:
echo 1. 浏览器是否支持WebCodecs
echo 2. 硬件解码是否有问题（尝试软件解码）
echo 3. 网络连接是否稳定
echo.

pause
