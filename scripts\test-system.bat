@echo off
echo 🧪 测试修复后的WebRTC远程桌面系统
echo ================================================

echo.
echo 📋 测试步骤:
echo 1. 启动WebRTC服务器
echo 2. 启动前端开发服务器
echo 3. 启动超低延迟视频流
echo 4. 打开浏览器测试
echo.

echo 🚀 第1步: 启动WebRTC服务器...
echo 请在新的命令行窗口中运行:
echo cd webrtc-server\x64\Debug
echo .\webrtc-server.exe
echo.
pause

echo 🌐 第2步: 启动前端开发服务器...
echo 请在新的命令行窗口中运行:
echo cd frontend
echo npm run dev
echo.
pause

echo 📺 第3步: 启动超低延迟视频流...
echo 使用优化后的FFmpeg配置...
echo.

REM 启动超低延迟流
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_nvenc ^
  -preset p1 ^
  -tune ull ^
  -profile:v high ^
  -level 4.1 ^
  -b:v 3M ^
  -maxrate 4M ^
  -bufsize 0 ^
  -keyint_min 3 ^
  -g 9 ^
  -bf 0 ^
  -refs 1 ^
  -rc cbr ^
  -cq 20 ^
  -zerolatency 1 ^
  -no-scenecut 1 ^
  -forced-idr 1 ^
  -delay 0 ^
  -fflags nobuffer ^
  -flags low_delay ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

echo.
echo ✅ 测试完成！
echo.
echo 📊 性能指标检查:
echo - 延迟应该 < 100ms
echo - 视频应该流畅无花屏
echo - WebCodecs解码器应该工作
echo - 控制响应应该及时
echo.
pause
