@echo off
echo 🚀 启动超低延迟远程桌面流...

REM 专为远程桌面优化的超低延迟配置
REM 目标：延迟 < 50ms

echo 📺 超低延迟远程桌面配置
echo ⚡ 目标延迟: < 50ms
echo 🎯 优化: 零缓冲 + 硬件编码 + 高频关键帧

ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_nvenc ^
  -preset p1 ^
  -tune ull ^
  -profile:v high ^
  -level 4.1 ^
  -b:v 3M ^
  -maxrate 4M ^
  -bufsize 0 ^
  -keyint_min 3 ^
  -g 9 ^
  -bf 0 ^
  -refs 1 ^
  -rc cbr ^
  -cq 20 ^
  -zerolatency 1 ^
  -no-scenecut 1 ^
  -forced-idr 1 ^
  -delay 0 ^
  -fflags nobuffer ^
  -flags low_delay ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

echo ✅ 超低延迟流配置完成！
echo 💡 提示: 如果出现花屏，请适当增加码率
pause
