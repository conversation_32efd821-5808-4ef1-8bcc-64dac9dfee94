@echo off
echo 🚀 启动超高质量视频流...

REM 超高质量H264编码配置
REM 解决模糊和花屏问题的专业参数

echo 📺 配置1: 超高质量 (推荐)
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_nvenc ^
  -preset p1 ^
  -tune ll ^
  -profile:v high ^
  -level 4.1 ^
  -b:v 8M ^
  -maxrate 10M ^
  -bufsize 16M ^
  -keyint_min 30 ^
  -g 60 ^
  -bf 0 ^
  -refs 1 ^
  -rc vbr ^
  -cq 18 ^
  -qmin 15 ^
  -qmax 25 ^
  -spatial_aq 1 ^
  -temporal_aq 1 ^
  -aq-strength 8 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

pause

REM 备选配置2: 平衡质量和性能
echo 📺 配置2: 平衡模式
ffmpeg -f gdigrab -framerate 30 -i desktop ^
  -c:v h264_nvenc ^
  -preset p4 ^
  -tune ll ^
  -profile:v high ^
  -b:v 5M ^
  -maxrate 6M ^
  -bufsize 10M ^
  -keyint_min 15 ^
  -g 30 ^
  -bf 0 ^
  -refs 2 ^
  -rc cbr ^
  -cq 20 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

pause

REM 备选配置3: 低延迟模式
echo 📺 配置3: 低延迟模式  
ffmpeg -f gdigrab -framerate 60 -i desktop ^
  -c:v h264_nvenc ^
  -preset p1 ^
  -tune ull ^
  -profile:v high ^
  -b:v 6M ^
  -maxrate 8M ^
  -bufsize 4M ^
  -keyint_min 10 ^
  -g 20 ^
  -bf 0 ^
  -refs 1 ^
  -rc cbr ^
  -cq 16 ^
  -zerolatency 1 ^
  -pix_fmt yuv420p ^
  -f rtp rtp://127.0.0.1:5000

echo ✅ 流媒体配置完成！
pause
