{"Version": 1, "WorkspaceRootPath": "D:\\webrtc-demo\\webrtc-server\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server-clean.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\enhanced-webrtc-server-clean.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\UDPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\RTPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\WebRTCManager.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\WebRTCManager.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AdaptiveQualityController.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\AdaptiveQualityController.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\RTPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\FFmpegStreamer.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\libdatachannel\\include\\rtc\\track.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility||{3B902123-F8A7-4915-9F01-361F908088D0}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\libdatachannel\\include\\rtc\\channel.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\libdatachannel\\include\\rtc\\description.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-desktop-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\enhanced-desktop-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\NVIDIACapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\SimpleNVENCCapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\SimpleNVENCCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\InputHandler.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\DesktopCapture.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCaptureDebug.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\DesktopCaptureDebug.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\DesktopCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\InputHandler.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\NVIDIACapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\Video_Codec_Interface_13.0.19\\Video_Codec_Interface_13.0.19\\Interface\\nvEncodeAPI.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\media-test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\media-test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-simple.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\webrtc-server-simple.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\minimal-video-test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\minimal-video-test.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-full.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\webrtc-server-full.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\UDPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\httplib.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\httplib.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AudioUDPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\AudioUDPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\json.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A93A77E9-CFB2-492F-B3FA-91589863A901}|webrtc-server\\webrtc-server.vcxproj|solutionrelative:webrtc-server\\json.hpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\FFmpegStreamer.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\enhanced-webrtc-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\webrtc-server.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-safe.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\webrtc-server-safe.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIADesktopCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\NVIDIADesktopCapture.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\AAC_RTPReceiver.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\RTSPClient.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\AAC_RTPReceiver.cpp||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}", "RelativeMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|solutionrelative:webrtc-server\\RTSPClient.h||{D0E1A5C6-B359-4E41-9B60-3365922C2A22}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Document", "DocumentIndex": 5, "Title": "AdaptiveQualityController.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AdaptiveQualityController.cpp", "RelativeDocumentMoniker": "webrtc-server\\AdaptiveQualityController.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AdaptiveQualityController.cpp", "RelativeToolTip": "webrtc-server\\AdaptiveQualityController.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAIMBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-31T03:08:16.437Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "enhanced-webrtc-server-clean.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server-clean.cpp", "RelativeDocumentMoniker": "webrtc-server\\enhanced-webrtc-server-clean.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server-clean.cpp", "RelativeToolTip": "webrtc-server\\enhanced-webrtc-server-clean.cpp", "ViewState": "AgIAAKwBAAAAAAAAAAAAADMAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-23T11:35:36.249Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "RTPReceiver.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.cpp", "RelativeDocumentMoniker": "webrtc-server\\RTPReceiver.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.cpp", "RelativeToolTip": "webrtc-server\\RTPReceiver.cpp", "ViewState": "AgIAANQBAAAAAAAAAAAAAMUBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-23T08:06:45.332Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "WebRTCManager.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.cpp", "RelativeDocumentMoniker": "webrtc-server\\WebRTCManager.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.cpp", "RelativeToolTip": "webrtc-server\\WebRTCManager.cpp", "ViewState": "AgIAAF8BAAAAAAAAAAAnwGsBAAAVAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T11:21:30.735Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "RTPReceiver.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.h", "RelativeDocumentMoniker": "webrtc-server\\RTPReceiver.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTPReceiver.h", "RelativeToolTip": "webrtc-server\\RTPReceiver.h", "ViewState": "AgIAAFEAAAAAAAAAAAAAAGEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-23T08:06:03.288Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "WebRTCManager.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.h", "RelativeDocumentMoniker": "webrtc-server\\WebRTCManager.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\WebRTCManager.h", "RelativeToolTip": "webrtc-server\\WebRTCManager.h", "ViewState": "AgIAAJAAAAAAAAAAAAAIwFcAAAAIAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-21T11:42:02.21Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "UDPReceiver.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.cpp", "RelativeDocumentMoniker": "webrtc-server\\UDPReceiver.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.cpp", "RelativeToolTip": "webrtc-server\\UDPReceiver.cpp", "ViewState": "AgIAAEgAAAAAAAAAAAAAAA8AAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T13:17:17.968Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "SimpleNVENCCapture.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.h", "RelativeDocumentMoniker": "webrtc-server\\SimpleNVENCCapture.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.h", "RelativeToolTip": "webrtc-server\\SimpleNVENCCapture.h", "ViewState": "AgIAAA4AAAAAAAAAAAAAAB4AAAAGAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T09:42:48.847Z"}, {"$type": "Document", "DocumentIndex": 14, "Title": "SimpleNVENCCapture.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.cpp", "RelativeDocumentMoniker": "webrtc-server\\SimpleNVENCCapture.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\SimpleNVENCCapture.cpp", "RelativeToolTip": "webrtc-server\\SimpleNVENCCapture.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAADoBAAAgAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T09:40:11.594Z"}, {"$type": "Document", "DocumentIndex": 13, "Title": "NVIDIACapture.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.cpp", "RelativeDocumentMoniker": "webrtc-server\\NVIDIACapture.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.cpp", "RelativeToolTip": "webrtc-server\\NVIDIACapture.cpp", "ViewState": "AgIAAFcBAAAAAAAAAAAAABwAAAAKAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T09:24:59.155Z"}, {"$type": "Document", "DocumentIndex": 35, "Title": "NVIDIADesktopCapture.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIADesktopCapture.h", "RelativeDocumentMoniker": "webrtc-server\\NVIDIADesktopCapture.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIADesktopCapture.h", "RelativeToolTip": "webrtc-server\\NVIDIADesktopCapture.h", "ViewState": "AgIAAAMAAAAAAAAAAAAAABAAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-30T02:18:03.832Z"}, {"$type": "Document", "DocumentIndex": 7, "Title": "FFmpegStreamer.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.cpp", "RelativeDocumentMoniker": "webrtc-server\\FFmpegStreamer.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.cpp", "RelativeToolTip": "webrtc-server\\FFmpegStreamer.cpp", "ViewState": "AgIAACACAAAAAAAAAAAAADECAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T08:11:54.269Z"}, {"$type": "Document", "DocumentIndex": 12, "Title": "enhanced-desktop-server.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-desktop-server.cpp", "RelativeDocumentMoniker": "webrtc-server\\enhanced-desktop-server.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-desktop-server.cpp", "RelativeToolTip": "webrtc-server\\enhanced-desktop-server.cpp", "ViewState": "AgIAAA4BAAAAAAAAAAAnwBoBAAAsAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T08:08:55.929Z"}, {"$type": "Document", "DocumentIndex": 16, "Title": "InputHandler.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.cpp", "RelativeDocumentMoniker": "webrtc-server\\InputHandler.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.cpp", "RelativeToolTip": "webrtc-server\\InputHandler.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA8AAAAhAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T07:48:18.055Z"}, {"$type": "Document", "DocumentIndex": 17, "Title": "DesktopCapture.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.cpp", "RelativeDocumentMoniker": "webrtc-server\\DesktopCapture.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.cpp", "RelativeToolTip": "webrtc-server\\DesktopCapture.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAADwCAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-29T09:40:33.971Z"}, {"$type": "Document", "DocumentIndex": 27, "Title": "UDPReceiver.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.h", "RelativeDocumentMoniker": "webrtc-server\\UDPReceiver.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\UDPReceiver.h", "RelativeToolTip": "webrtc-server\\UDPReceiver.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAdAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-22T08:27:49.861Z"}, {"$type": "Document", "DocumentIndex": 28, "Title": "httplib.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\httplib.h", "RelativeDocumentMoniker": "webrtc-server\\httplib.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\httplib.h", "RelativeToolTip": "webrtc-server\\httplib.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAF8VAAAMAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-21T11:32:22.779Z"}, {"$type": "Document", "DocumentIndex": 31, "Title": "FFmpegStreamer.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.h", "RelativeDocumentMoniker": "webrtc-server\\FFmpegStreamer.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\FFmpegStreamer.h", "RelativeToolTip": "webrtc-server\\FFmpegStreamer.h", "ViewState": "AgIAAJkAAAAAAAAAAAAAAAkAAAAbAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T07:47:19.384Z"}, {"$type": "Document", "DocumentIndex": 18, "Title": "DesktopCaptureDebug.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCaptureDebug.h", "RelativeDocumentMoniker": "webrtc-server\\DesktopCaptureDebug.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCaptureDebug.h", "RelativeToolTip": "webrtc-server\\DesktopCaptureDebug.h", "ViewState": "AgIAAJwAAAAAAAAAAAAAACgAAAAfAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T08:30:53.317Z"}, {"$type": "Document", "DocumentIndex": 19, "Title": "DesktopCapture.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.h", "RelativeDocumentMoniker": "webrtc-server\\DesktopCapture.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\DesktopCapture.h", "RelativeToolTip": "webrtc-server\\DesktopCapture.h", "ViewState": "AgIAAPIAAAAAAAAAAAAAAOYAAAAUAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T07:47:23.285Z"}, {"$type": "Document", "DocumentIndex": 29, "Title": "AudioUDPReceiver.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AudioUDPReceiver.h", "RelativeDocumentMoniker": "webrtc-server\\AudioUDPReceiver.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AudioUDPReceiver.h", "RelativeToolTip": "webrtc-server\\AudioUDPReceiver.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAoAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T08:36:42.56Z"}, {"$type": "Document", "DocumentIndex": 36, "Title": "AAC_RTPReceiver.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.h", "RelativeDocumentMoniker": "webrtc-server\\AAC_RTPReceiver.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.h", "RelativeToolTip": "webrtc-server\\AAC_RTPReceiver.h", "ViewState": "AgIAAEsAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-23T09:30:23.209Z"}, {"$type": "Document", "DocumentIndex": 20, "Title": "InputHandler.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.h", "RelativeDocumentMoniker": "webrtc-server\\InputHandler.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\InputHandler.h", "RelativeToolTip": "webrtc-server\\InputHandler.h", "ViewState": "AgIAAK8AAAAAAAAAAAAAAAwAAAATAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T07:48:52.685Z"}, {"$type": "Document", "DocumentIndex": 21, "Title": "NVIDIACapture.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.h", "RelativeDocumentMoniker": "webrtc-server\\NVIDIACapture.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\NVIDIACapture.h", "RelativeToolTip": "webrtc-server\\NVIDIACapture.h", "ViewState": "AgIAAIMAAAAAAAAAAAAAAHkAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T09:24:46.353Z"}, {"$type": "Document", "DocumentIndex": 37, "Title": "RTSPClient.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.cpp", "RelativeDocumentMoniker": "webrtc-server\\RTSPClient.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.cpp", "RelativeToolTip": "webrtc-server\\RTSPClient.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAKABAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-23T09:27:58.679Z"}, {"$type": "Document", "DocumentIndex": 22, "Title": "nvEncodeAPI.h", "DocumentMoniker": "D:\\Video_Codec_Interface_13.0.19\\Video_Codec_Interface_13.0.19\\Interface\\nvEncodeAPI.h", "RelativeDocumentMoniker": "..\\..\\Video_Codec_Interface_13.0.19\\Video_Codec_Interface_13.0.19\\Interface\\nvEncodeAPI.h", "ToolTip": "D:\\Video_Codec_Interface_13.0.19\\Video_Codec_Interface_13.0.19\\Interface\\nvEncodeAPI.h", "RelativeToolTip": "..\\..\\Video_Codec_Interface_13.0.19\\Video_Codec_Interface_13.0.19\\Interface\\nvEncodeAPI.h", "ViewState": "AgIAADIRAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-29T08:46:49.936Z"}, {"$type": "Document", "DocumentIndex": 32, "Title": "enhanced-webrtc-server.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server.cpp", "RelativeDocumentMoniker": "webrtc-server\\enhanced-webrtc-server.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\enhanced-webrtc-server.cpp", "RelativeToolTip": "webrtc-server\\enhanced-webrtc-server.cpp", "ViewState": "AgIAAJUBAAAAAAAAAAAAAK0AAAA1AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-23T09:28:09.923Z"}, {"$type": "Document", "DocumentIndex": 38, "Title": "AAC_RTPReceiver.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.cpp", "RelativeDocumentMoniker": "webrtc-server\\AAC_RTPReceiver.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\AAC_RTPReceiver.cpp", "RelativeToolTip": "webrtc-server\\AAC_RTPReceiver.cpp", "ViewState": "AgIAAN8AAAAAAAAAAAAnwO0AAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-23T09:31:58.237Z"}, {"$type": "Document", "DocumentIndex": 39, "Title": "RTSPClient.h", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.h", "RelativeDocumentMoniker": "webrtc-server\\RTSPClient.h", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\RTSPClient.h", "RelativeToolTip": "webrtc-server\\RTSPClient.h", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEQAAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-23T09:29:51.01Z"}, {"$type": "Document", "DocumentIndex": 30, "Title": "json.hpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\json.hpp", "RelativeDocumentMoniker": "webrtc-server\\json.hpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\json.hpp", "RelativeToolTip": "webrtc-server\\json.hpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-22T08:27:52.691Z"}, {"$type": "Document", "DocumentIndex": 23, "Title": "media-test.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\media-test.cpp", "RelativeDocumentMoniker": "webrtc-server\\media-test.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\media-test.cpp", "RelativeToolTip": "webrtc-server\\media-test.cpp", "ViewState": "AgIAAFAAAAAAAAAAAAAAAFgAAAAYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-19T06:40:53.019Z"}, {"$type": "Document", "DocumentIndex": 33, "Title": "webrtc-server.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server.cpp", "RelativeDocumentMoniker": "webrtc-server\\webrtc-server.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server.cpp", "RelativeToolTip": "webrtc-server\\webrtc-server.cpp", "ViewState": "AgIAAMIAAAAAAAAAAAAnwNgAAAACAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-20T11:34:35.627Z"}, {"$type": "Document", "DocumentIndex": 24, "Title": "webrtc-server-simple.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-simple.cpp", "RelativeDocumentMoniker": "webrtc-server\\webrtc-server-simple.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-simple.cpp", "RelativeToolTip": "webrtc-server\\webrtc-server-simple.cpp", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAcAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T12:25:27.982Z"}, {"$type": "Document", "DocumentIndex": 25, "Title": "minimal-video-test.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\minimal-video-test.cpp", "RelativeDocumentMoniker": "webrtc-server\\minimal-video-test.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\minimal-video-test.cpp", "RelativeToolTip": "webrtc-server\\minimal-video-test.cpp", "ViewState": "AgIAAEgAAAAAAAAAAAAAAFMAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T13:16:38.785Z"}, {"$type": "Document", "DocumentIndex": 34, "Title": "webrtc-server-safe.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-safe.cpp", "RelativeDocumentMoniker": "webrtc-server\\webrtc-server-safe.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-safe.cpp", "RelativeToolTip": "webrtc-server\\webrtc-server-safe.cpp", "ViewState": "AgIAALAAAAAAAAAAAAAAAMUAAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T11:45:16.43Z"}, {"$type": "Document", "DocumentIndex": 26, "Title": "webrtc-server-full.cpp", "DocumentMoniker": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-full.cpp", "RelativeDocumentMoniker": "webrtc-server\\webrtc-server-full.cpp", "ToolTip": "D:\\webrtc-demo\\webrtc-server\\webrtc-server\\webrtc-server-full.cpp", "RelativeToolTip": "webrtc-server\\webrtc-server-full.cpp", "ViewState": "AgIAABIBAAAAAAAAAAAAACUBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000677|", "WhenOpened": "2025-07-21T12:00:43.073Z"}, {"$type": "Document", "DocumentIndex": 8, "Title": "track.hpp", "DocumentMoniker": "D:\\libdatachannel\\include\\rtc\\track.hpp", "RelativeDocumentMoniker": "..\\..\\libdatachannel\\include\\rtc\\track.hpp", "ToolTip": "D:\\libdatachannel\\include\\rtc\\track.hpp", "RelativeToolTip": "..\\..\\libdatachannel\\include\\rtc\\track.hpp", "ViewState": "AgIAABoAAAAAAAAAAAAIwCQAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-21T10:40:10.227Z"}, {"$type": "Document", "DocumentIndex": 9, "Title": "xutility", "DocumentMoniker": "C:\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "ToolTip": "C:\\vs2022\\VC\\Tools\\MSVC\\14.44.35207\\include\\xutility", "ViewState": "AgIAAM8BAAAAAAAAAAAnwNsBAAA8AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.001001|", "WhenOpened": "2025-07-21T08:30:56.598Z"}, {"$type": "Document", "DocumentIndex": 10, "Title": "channel.hpp", "DocumentMoniker": "D:\\libdatachannel\\include\\rtc\\channel.hpp", "RelativeDocumentMoniker": "..\\..\\libdatachannel\\include\\rtc\\channel.hpp", "ToolTip": "D:\\libdatachannel\\include\\rtc\\channel.hpp", "RelativeToolTip": "..\\..\\libdatachannel\\include\\rtc\\channel.hpp", "ViewState": "AgIAAB0AAAAAAAAAAAAIwCgAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-20T13:33:41.047Z"}, {"$type": "Document", "DocumentIndex": 11, "Title": "description.hpp", "DocumentMoniker": "D:\\libdatachannel\\include\\rtc\\description.hpp", "RelativeDocumentMoniker": "..\\..\\libdatachannel\\include\\rtc\\description.hpp", "ToolTip": "D:\\libdatachannel\\include\\rtc\\description.hpp", "RelativeToolTip": "..\\..\\libdatachannel\\include\\rtc\\description.hpp", "ViewState": "AgIAADIBAAAAAAAAAAAYwEgBAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000680|", "WhenOpened": "2025-07-20T13:32:36.265Z"}]}]}]}