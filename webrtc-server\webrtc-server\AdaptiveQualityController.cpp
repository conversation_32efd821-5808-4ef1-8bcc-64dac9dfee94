#include "AdaptiveQualityController.h"
#include <iostream>
#include <algorithm>
#include <cmath>
#include <string>
#include <sstream>

AdaptiveQualityController::AdaptiveQualityController()
    : strategy_(AdaptationStrategy::BALANCED)
    , targetLatency_(DEFAULT_TARGET_LATENCY)
    , minBitrate_(DEFAULT_MIN_BITRATE)
    , maxBitrate_(DEFAULT_MAX_BITRATE)
    , adaptationEnabled_(true)
    , currentLevel_(QualityLevel::MEDIUM)
    , lastAdjustmentTime_(std::chrono::steady_clock::now()) {

    currentProfile_ = getProfileForLevel(currentLevel_);
    currentMetrics_.reset();

    logInfo("AdaptiveQualityController initialized");
}

AdaptiveQualityController::~AdaptiveQualityController() = default;

void AdaptiveQualityController::initialize() {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.reset();
    logInfo("AdaptiveQualityController initialized successfully");
}

void AdaptiveQualityController::setStrategy(AdaptationStrategy strategy) {
    strategy_ = strategy;
    std::ostringstream oss;
    oss << "Adaptation strategy set to: " << static_cast<int>(strategy);
    logInfo(oss.str());
}

void AdaptiveQualityController::setTargetLatency(double targetMs) {
    targetLatency_ = targetMs;
    std::ostringstream oss;
    oss << "Target latency set to: " << targetMs << "ms";
    logInfo(oss.str());
}

void AdaptiveQualityController::setMinBitrate(int kbps) {
    minBitrate_ = kbps;
    std::ostringstream oss;
    oss << "Min bitrate set to: " << kbps << "kbps";
    logInfo(oss.str());
}

void AdaptiveQualityController::setMaxBitrate(int kbps) {
    maxBitrate_ = kbps;
    std::ostringstream oss;
    oss << "Max bitrate set to: " << kbps << "kbps";
    logInfo(oss.str());
}

void AdaptiveQualityController::enableAdaptation(bool enable) {
    adaptationEnabled_ = enable;
    logInfo("Adaptation " + std::string(enable ? "enabled" : "disabled"));
}

void AdaptiveQualityController::updateNetworkMetrics(const NetworkMetrics& metrics) {
    {
        std::lock_guard<std::mutex> lock(metricsMutex_);
        currentMetrics_ = metrics;
        currentMetrics_.timestamp = std::chrono::steady_clock::now();
    }

    if (adaptationEnabled_ && shouldAdjustQuality()) {
        QualityLevel recommendedLevel = recommendQualityLevel();
        if (recommendedLevel != currentLevel_) {
            applyQualityAdjustment(recommendedLevel);
        }
    }
}

void AdaptiveQualityController::updateRTT(double rtt) {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.rtt = rtt;
    currentMetrics_.timestamp = std::chrono::steady_clock::now();
}

void AdaptiveQualityController::updatePacketLoss(double loss) {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.packetLoss = loss;
    currentMetrics_.timestamp = std::chrono::steady_clock::now();
}

void AdaptiveQualityController::updateJitter(double jitter) {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.jitter = jitter;
    currentMetrics_.timestamp = std::chrono::steady_clock::now();
}

void AdaptiveQualityController::updateBandwidth(double bandwidth) {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.bandwidth = bandwidth;
    currentMetrics_.timestamp = std::chrono::steady_clock::now();
}

void AdaptiveQualityController::updateThroughput(double throughput) {
    std::lock_guard<std::mutex> lock(metricsMutex_);
    currentMetrics_.throughput = throughput;
    currentMetrics_.timestamp = std::chrono::steady_clock::now();
}

QualityProfile AdaptiveQualityController::getCurrentProfile() const {
    return currentProfile_;
}

QualityLevel AdaptiveQualityController::getCurrentLevel() const {
    return currentLevel_;
}

void AdaptiveQualityController::setQualityLevel(QualityLevel level) {
    if (level != currentLevel_) {
        QualityLevel oldLevel = currentLevel_;
        currentLevel_ = level;
        currentProfile_ = getProfileForLevel(level);

        if (onQualityChanged_) {
            onQualityChanged_(currentProfile_);
        }

        logInfo("Quality level manually set to: " + qualityLevelToString(level));
    }
}

void AdaptiveQualityController::forceQualityUpdate() {
    if (adaptationEnabled_) {
        QualityLevel recommendedLevel = recommendQualityLevel();
        applyQualityAdjustment(recommendedLevel);
    }
}

bool AdaptiveQualityController::shouldAdjustQuality() {
    auto now = std::chrono::steady_clock::now();

    // Check minimum adjustment interval
    if (now - lastAdjustmentTime_ < MIN_ADJUSTMENT_INTERVAL) {
        return false;
    }

    // Check if network is stable
    if (!isStableNetwork()) {
        return false;
    }

    return true;
}

QualityLevel AdaptiveQualityController::recommendQualityLevel() {
    QualityLevel recommended = calculateOptimalLevel();
    return applyStrategy(recommended);
}

void AdaptiveQualityController::applyQualityAdjustment(QualityLevel newLevel) {
    if (newLevel == currentLevel_) {
        return;
    }

    QualityLevel oldLevel = currentLevel_;
    currentLevel_ = newLevel;
    currentProfile_ = getProfileForLevel(newLevel);
    lastAdjustmentTime_ = std::chrono::steady_clock::now();

    if (onQualityChanged_) {
        onQualityChanged_(currentProfile_);
    }

    logInfo("Quality adjusted from " + qualityLevelToString(oldLevel) +
            " to " + qualityLevelToString(newLevel));
}

// Network quality assessment
NetworkQuality AdaptiveQualityController::assessNetworkQuality() const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    // Assess network quality based on multiple metrics
    int score = 0;

    // RTT scoring
    if (currentMetrics_.rtt < 50) score += 2;
    else if (currentMetrics_.rtt < 100) score += 1;
    else if (currentMetrics_.rtt > 300) score -= 2;
    else if (currentMetrics_.rtt > 200) score -= 1;

    // Packet loss scoring
    if (currentMetrics_.packetLoss < 0.01) score += 2;
    else if (currentMetrics_.packetLoss < 0.02) score += 1;
    else if (currentMetrics_.packetLoss > 0.10) score -= 2;
    else if (currentMetrics_.packetLoss > 0.05) score -= 1;

    // Jitter scoring
    if (currentMetrics_.jitter < 10) score += 1;
    else if (currentMetrics_.jitter > 50) score -= 1;

    // Bandwidth scoring
    if (currentMetrics_.bandwidth > currentProfile_.bitrate * 2) score += 1;
    else if (currentMetrics_.bandwidth < currentProfile_.bitrate * 1.2) score -= 1;

    // Determine network quality based on total score
    if (score >= 4) return NetworkQuality::EXCELLENT;
    else if (score >= 2) return NetworkQuality::GOOD;
    else if (score >= 0) return NetworkQuality::FAIR;
    else if (score >= -2) return NetworkQuality::POOR;
    else return NetworkQuality::VERY_POOR;
}

// Callback setting
void AdaptiveQualityController::setOnQualityChanged(std::function<void(const QualityProfile&)> callback) {
    onQualityChanged_ = callback;
}

// Static methods implementation
std::vector<QualityProfile> AdaptiveQualityController::getDefaultProfiles() {
    return {
        // ULTRA_LOW
        {"Ultra Low", 320, 240, 15, 300, 30, "ultrafast", "baseline", 0.2, 22050, 1, 64},
        // LOW
        {"Low", 480, 360, 20, 500, 30, "ultrafast", "baseline", 0.4, 44100, 2, 96},
        // MEDIUM
        {"Medium", 640, 480, 25, 800, 30, "fast", "main", 0.6, 48000, 2, 128},
        // HIGH
        {"High", 1280, 720, 30, 1500, 30, "fast", "high", 0.8, 48000, 2, 160},
        // ULTRA_HIGH
        {"Ultra High", 1920, 1080, 30, 2500, 30, "medium", "high", 1.0, 48000, 2, 192}
    };
}

QualityProfile AdaptiveQualityController::getProfileForLevel(QualityLevel level) {
    auto profiles = getDefaultProfiles();
    int index = static_cast<int>(level);

    if (index >= 0 && index < profiles.size()) {
        return profiles[index];
    }

    // Default to medium quality
    return profiles[static_cast<int>(QualityLevel::MEDIUM)];
}

std::string AdaptiveQualityController::qualityLevelToString(QualityLevel level) {
    switch (level) {
        case QualityLevel::ULTRA_LOW: return "Ultra Low";
        case QualityLevel::LOW: return "Low";
        case QualityLevel::MEDIUM: return "Medium";
        case QualityLevel::HIGH: return "High";
        case QualityLevel::ULTRA_HIGH: return "Ultra High";
        case QualityLevel::ADAPTIVE: return "Adaptive";
        default: return "Unknown";
    }
}

std::string AdaptiveQualityController::networkQualityToString(NetworkQuality quality) {
    switch (quality) {
        case NetworkQuality::EXCELLENT: return "Excellent";
        case NetworkQuality::GOOD: return "Good";
        case NetworkQuality::FAIR: return "Fair";
        case NetworkQuality::POOR: return "Poor";
        case NetworkQuality::VERY_POOR: return "Very Poor";
        default: return "Unknown";
    }
}

// Internal methods implementation
bool AdaptiveQualityController::isStableNetwork() const {
    // Simple stability check based on current metrics
    std::lock_guard<std::mutex> lock(metricsMutex_);

    return currentMetrics_.rtt < thresholds_.rttDowngrade &&
           currentMetrics_.packetLoss < thresholds_.lossDowngrade;
}

QualityLevel AdaptiveQualityController::calculateOptimalLevel() const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    // Calculate optimal quality level based on network conditions
    if (currentMetrics_.rtt > thresholds_.rttDowngrade ||
        currentMetrics_.packetLoss > thresholds_.lossDowngrade) {
        return QualityLevel::LOW;
    } else if (currentMetrics_.bandwidth > 2000 &&
               currentMetrics_.rtt < thresholds_.rttUpgrade) {
        return QualityLevel::HIGH;
    } else {
        return QualityLevel::MEDIUM;
    }
}

QualityLevel AdaptiveQualityController::applyStrategy(QualityLevel recommended) const {
    switch (strategy_) {
        case AdaptationStrategy::CONSERVATIVE:
            return conservativeStrategy();
        case AdaptationStrategy::BALANCED:
            return balancedStrategy();
        case AdaptationStrategy::AGGRESSIVE:
            return aggressiveStrategy();
        case AdaptationStrategy::BANDWIDTH_BASED:
            return bandwidthBasedStrategy();
        case AdaptationStrategy::LATENCY_BASED:
            return latencyBasedStrategy();
        default:
            return recommended;
    }
}

// Strategy implementations (simplified)
QualityLevel AdaptiveQualityController::conservativeStrategy() const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    if (shouldDowngrade(currentLevel_)) {
        int newLevel = std::max(static_cast<int>(QualityLevel::ULTRA_LOW),
                               static_cast<int>(currentLevel_) - 1);
        return static_cast<QualityLevel>(newLevel);
    }

    return currentLevel_;
}

QualityLevel AdaptiveQualityController::balancedStrategy() const {
    return calculateOptimalLevel();
}

QualityLevel AdaptiveQualityController::aggressiveStrategy() const {
    return calculateOptimalLevel();
}

QualityLevel AdaptiveQualityController::bandwidthBasedStrategy() const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    if (currentMetrics_.bandwidth > 2000) {
        return QualityLevel::HIGH;
    } else if (currentMetrics_.bandwidth > 1000) {
        return QualityLevel::MEDIUM;
    } else {
        return QualityLevel::LOW;
    }
}

QualityLevel AdaptiveQualityController::latencyBasedStrategy() const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    if (currentMetrics_.rtt < targetLatency_ * 0.5) {
        return QualityLevel::HIGH;
    } else if (currentMetrics_.rtt < targetLatency_) {
        return QualityLevel::MEDIUM;
    } else {
        return QualityLevel::LOW;
    }
}

bool AdaptiveQualityController::canUpgrade(QualityLevel targetLevel) const {
    if (targetLevel <= currentLevel_) {
        return false;
    }

    std::lock_guard<std::mutex> lock(metricsMutex_);
    QualityProfile targetProfile = getProfileForLevel(targetLevel);

    return currentMetrics_.bandwidth >= targetProfile.bitrate * thresholds_.bandwidthMargin &&
           currentMetrics_.rtt <= thresholds_.rttUpgrade &&
           currentMetrics_.packetLoss <= thresholds_.lossUpgrade;
}

bool AdaptiveQualityController::shouldDowngrade(QualityLevel currentLevel) const {
    std::lock_guard<std::mutex> lock(metricsMutex_);

    return currentMetrics_.rtt > thresholds_.rttDowngrade ||
           currentMetrics_.packetLoss > thresholds_.lossDowngrade ||
           currentMetrics_.jitter > thresholds_.jitterDowngrade ||
           currentMetrics_.bandwidth < currentProfile_.bitrate;
}

// Utility methods
void AdaptiveQualityController::logInfo(const std::string& message) const {
    std::cout << "[QUALITY] " << message << std::endl;
}

void AdaptiveQualityController::logWarning(const std::string& message) const {
    std::cout << "[QUALITY-WARN] " << message << std::endl;
}

void AdaptiveQualityController::logError(const std::string& message) const {
    std::cerr << "[QUALITY-ERROR] " << message << std::endl;
}
