#pragma once

#include <chrono>
#include <vector>
#include <memory>
#include <atomic>
#include <mutex>
#include <functional>
#include <string>
#include "WebRTCTypes.h"

// Network quality metrics
struct NetworkMetrics {
    double rtt = 0.0;                    // Round trip time (ms)
    double packetLoss = 0.0;             // Packet loss rate (0.0-1.0)
    double jitter = 0.0;                 // Jitter (ms)
    double bandwidth = 0.0;              // Available bandwidth (kbps)
    double throughput = 0.0;             // Actual throughput (kbps)
    std::chrono::steady_clock::time_point timestamp;

    void reset() {
        rtt = 0.0;
        packetLoss = 0.0;
        jitter = 0.0;
        bandwidth = 0.0;
        throughput = 0.0;
        timestamp = std::chrono::steady_clock::now();
    }
};

// EncoderConfig is now defined in WebRTCTypes.h

// Quality profile for adaptive control
struct QualityProfile {
    std::string name;
    int width;
    int height;
    int framerate;
    int bitrate;              // kbps
    int keyFrameInterval;     // GOP size
    std::string preset;       // encoding preset
    std::string profile;      // H.264 profile
    double qualityFactor;     // 0.0-1.0, higher is better

    // Audio configuration
    int audioSampleRate = 48000;
    int audioChannels = 2;
    int audioBitrate = 128;   // kbps
};

// Quality level enumeration
enum class QualityLevel {
    ULTRA_LOW = 0,    // 240p, 15fps, 300kbps
    LOW = 1,          // 360p, 20fps, 500kbps
    MEDIUM = 2,       // 480p, 25fps, 800kbps
    HIGH = 3,         // 720p, 30fps, 1500kbps
    ULTRA_HIGH = 4,   // 1080p, 30fps, 2500kbps
    ADAPTIVE = 5      // Dynamic adjustment
};

// Adaptation strategy
enum class AdaptationStrategy {
    CONSERVATIVE,     // Conservative strategy, prioritize stability
    BALANCED,         // Balanced strategy, balance quality and stability
    AGGRESSIVE,       // Aggressive strategy, prioritize quality
    BANDWIDTH_BASED,  // Bandwidth-based strategy
    LATENCY_BASED     // Latency-based strategy
};

// Network quality assessment
enum class NetworkQuality {
    EXCELLENT,
    GOOD,
    FAIR,
    POOR,
    VERY_POOR
};

class AdaptiveQualityController {
public:
    AdaptiveQualityController();
    ~AdaptiveQualityController();

    // Initialization and configuration
    void initialize();
    void setStrategy(AdaptationStrategy strategy);
    void setTargetLatency(double targetMs);
    void setMinBitrate(int kbps);
    void setMaxBitrate(int kbps);
    void enableAdaptation(bool enable);

    // Network metrics update
    void updateNetworkMetrics(const NetworkMetrics& metrics);
    void updateRTT(double rtt);
    void updatePacketLoss(double loss);
    void updateJitter(double jitter);
    void updateBandwidth(double bandwidth);
    void updateThroughput(double throughput);

    // Quality control
    QualityProfile getCurrentProfile() const;
    QualityLevel getCurrentLevel() const;
    void setQualityLevel(QualityLevel level);
    void forceQualityUpdate();

    // Adaptive control
    bool shouldAdjustQuality();
    QualityLevel recommendQualityLevel();
    void applyQualityAdjustment(QualityLevel newLevel);

    // Network quality assessment
    NetworkQuality assessNetworkQuality() const;

    // Callback setting
    void setOnQualityChanged(std::function<void(const QualityProfile&)> callback);

    // Predefined quality configurations
    static std::vector<QualityProfile> getDefaultProfiles();
    static QualityProfile getProfileForLevel(QualityLevel level);
    static std::string qualityLevelToString(QualityLevel level);
    static std::string networkQualityToString(NetworkQuality quality);
private:
    // Configuration parameters
    AdaptationStrategy strategy_;
    double targetLatency_;
    int minBitrate_;
    int maxBitrate_;
    std::atomic<bool> adaptationEnabled_;

    // Current state
    QualityLevel currentLevel_;
    QualityProfile currentProfile_;
    NetworkMetrics currentMetrics_;
    mutable std::mutex metricsMutex_;

    // Adaptive logic
    std::chrono::steady_clock::time_point lastAdjustmentTime_;
    static constexpr std::chrono::milliseconds MIN_ADJUSTMENT_INTERVAL{5000}; // 5 seconds

    // Thresholds configuration
    struct Thresholds {
        double rttUpgrade = 50.0;        // ms
        double rttDowngrade = 200.0;     // ms
        double lossUpgrade = 0.01;       // 1%
        double lossDowngrade = 0.05;     // 5%
        double jitterUpgrade = 10.0;     // ms
        double jitterDowngrade = 50.0;   // ms
        double bandwidthMargin = 1.2;    // 20% margin
    } thresholds_;

    // Callback function
    std::function<void(const QualityProfile&)> onQualityChanged_;

    // Internal methods
    bool isStableNetwork() const;
    QualityLevel calculateOptimalLevel() const;
    QualityLevel applyStrategy(QualityLevel recommended) const;

    // Strategy implementations
    QualityLevel conservativeStrategy() const;
    QualityLevel balancedStrategy() const;
    QualityLevel aggressiveStrategy() const;
    QualityLevel bandwidthBasedStrategy() const;
    QualityLevel latencyBasedStrategy() const;

    // Quality assessment
    bool canUpgrade(QualityLevel targetLevel) const;
    bool shouldDowngrade(QualityLevel currentLevel) const;

    // Utility methods
    void logInfo(const std::string& message) const;
    void logWarning(const std::string& message) const;
    void logError(const std::string& message) const;

    // Constants
    static constexpr double DEFAULT_TARGET_LATENCY = 100.0; // ms
    static constexpr int DEFAULT_MIN_BITRATE = 200;         // kbps
    static constexpr int DEFAULT_MAX_BITRATE = 5000;        // kbps
};
