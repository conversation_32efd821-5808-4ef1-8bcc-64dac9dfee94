#include "AudioUDPReceiver.h"

AudioUDPReceiver::AudioUDPReceiver(int port)
    : port_(port), socket_(INVALID_SOCKET), running_(false) {

    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        std::cerr << "[Audio-UDP] WSAStartup failed: " << result << std::endl;
    }

    stats_.startTime = std::chrono::steady_clock::now();
}

AudioUDPReceiver::~AudioUDPReceiver() {
    stop();
    cleanup();
    WSACleanup();
}

bool AudioUDPReceiver::start() {
    if (running_.load()) {
        return true;
    }

    if (!initializeSocket()) {
        return false;
    }

    running_ = true;
    receiveThread_ = std::thread(&AudioUDPReceiver::receiveLoop, this);

    std::cout << "[Audio-UDP] Started listening on port " << port_ << std::endl;
    return true;
}

void AudioUDPReceiver::stop() {
    running_ = false;

    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }

    if (receiveThread_.joinable()) {
        receiveThread_.join();
    }

    std::cout << "[Audio-UDP] Stopped listening" << std::endl;
}

void AudioUDPReceiver::setOnDataReceived(std::function<void(const std::vector<uint8_t>&)> callback) {
    onDataReceived_ = callback;
}

AudioUDPReceiver::Stats AudioUDPReceiver::getStats() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

void AudioUDPReceiver::resetStats() {
    std::lock_guard<std::mutex> lock(statsMutex_);
    stats_ = Stats{};
    stats_.startTime = std::chrono::steady_clock::now();
}

bool AudioUDPReceiver::initializeSocket() {
    socket_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (socket_ == INVALID_SOCKET) {
        std::cerr << "[Audio-UDP] Failed to create socket: " << WSAGetLastError() << std::endl;
        return false;
    }

    u_long mode = 1;
    if (ioctlsocket(socket_, FIONBIO, &mode) != 0) {
        std::cerr << "[Audio-UDP] Failed to set non-blocking mode: " << WSAGetLastError() << std::endl;
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        return false;
    }

    int recvBufSize = 256 * 1024; // 256KB for audio
    if (setsockopt(socket_, SOL_SOCKET, SO_RCVBUF, (char*)&recvBufSize, sizeof(recvBufSize)) != 0) {
        std::cerr << "[Audio-UDP] Warning: Failed to set receive buffer size: " << WSAGetLastError() << std::endl;
    }

    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(port_);

    if (bind(socket_, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        std::cerr << "[Audio-UDP] Failed to bind port: " << WSAGetLastError() << std::endl;
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        return false;
    }

    return true;
}

void AudioUDPReceiver::receiveLoop() {
    std::vector<uint8_t> buffer(65536); // 64KB buffer
    sockaddr_in senderAddr;
    int senderAddrSize = sizeof(senderAddr);

    std::cout << "[Audio-UDP] Receive loop started" << std::endl;

    auto lastStatsTime = std::chrono::steady_clock::now();
    uint64_t packetsThisInterval = 0;

    while (running_.load()) {
        senderAddrSize = sizeof(senderAddr);

        int bytesReceived = recvfrom(socket_,
                                   reinterpret_cast<char*>(buffer.data()),
                                   static_cast<int>(buffer.size()),
                                   0,
                                   (sockaddr*)&senderAddr,
                                   &senderAddrSize);

        if (bytesReceived > 0) {
            // First packet debug info
            if (stats_.packetsReceived == 0) {
                std::cout << "[Audio-UDP] First packet received! " << bytesReceived << " bytes from "
                          << inet_ntoa(senderAddr.sin_addr) << ":" << ntohs(senderAddr.sin_port) << std::endl;
            }

            {
                std::lock_guard<std::mutex> lock(statsMutex_);
                stats_.packetsReceived++;
                stats_.bytesReceived += bytesReceived;
                stats_.averagePacketSize = static_cast<double>(stats_.bytesReceived) / stats_.packetsReceived;
            }

            packetsThisInterval++;

            // Create copy and send directly to callback (no buffering for audio)
            std::vector<uint8_t> data(buffer.begin(), buffer.begin() + bytesReceived);
            
            if (onDataReceived_) {
                try {
                    onDataReceived_(data);
                } catch (const std::exception& e) {
                    std::cerr << "[Audio-UDP] Error in data callback: " << e.what() << std::endl;
                }
            }

            // Print statistics every second
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStatsTime).count() >= 1) {
                std::cout << "[Audio-UDP] Stats: " << packetsThisInterval << " packets/sec, "
                          << "Total: " << stats_.packetsReceived << " packets, "
                          << "Avg size: " << static_cast<int>(stats_.averagePacketSize) << " bytes" << std::endl;
                lastStatsTime = now;
                packetsThisInterval = 0;
            }
        }
        else if (bytesReceived == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
                std::cerr << "[Audio-UDP] Error receiving data: " << error << std::endl;
                if (error == WSAECONNRESET || error == WSAENETDOWN) {
                    break;
                }
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    std::cout << "[Audio-UDP] Receive loop ended" << std::endl;
}

void AudioUDPReceiver::cleanup() {
    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }
}
