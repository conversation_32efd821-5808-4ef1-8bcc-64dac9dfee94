#pragma once

#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <thread>
#include <atomic>
#include <functional>
#include <memory>
#include <mutex>
#include <chrono>

#pragma comment(lib, "ws2_32.lib")

class AudioUDPReceiver {
public:
    AudioUDPReceiver(int port);
    ~AudioUDPReceiver();

    bool start();
    void stop();

    void setOnDataReceived(std::function<void(const std::vector<uint8_t>&)> callback);

    struct Stats {
        uint64_t packetsReceived = 0;
        uint64_t bytesReceived = 0;
        uint64_t packetsDropped = 0;
        double averagePacketSize = 0.0;
        std::chrono::steady_clock::time_point startTime;
    };

    Stats getStats() const;
    void resetStats();

private:
    int port_;
    SOCKET socket_;
    std::atomic<bool> running_;
    std::thread receiveThread_;
    std::function<void(const std::vector<uint8_t>&)> onDataReceived_;

    mutable std::mutex statsMutex_;
    Stats stats_;

    bool initializeSocket();
    void receiveLoop();
    void cleanup();
};
