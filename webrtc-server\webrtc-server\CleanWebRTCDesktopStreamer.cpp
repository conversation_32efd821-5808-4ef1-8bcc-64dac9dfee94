#include "CleanWebRTCDesktopStreamer.h"
#include "WebRTCManager.h"
#include <comdef.h>
#include <algorithm>
#include <iomanip>

CleanWebRTCDesktopStreamer::CleanWebRTCDesktopStreamer(WebRTCManager* webrtcManager) 
    : webrtcManager_(webrtcManager), streaming_(false) {
    stats_.startTime = std::chrono::steady_clock::now();
    lastStatsUpdate_ = stats_.startTime;
}

CleanWebRTCDesktopStreamer::~CleanWebRTCDesktopStreamer() {
    stopStreaming();
    cleanupDirectX();
}

bool CleanWebRTCDesktopStreamer::initialize(const StreamConfig& config) {
    config_ = config;
    
    std::cout << "[CLEAN-WEBRTC] Initializing desktop capture and streaming..." << std::endl;
    std::cout << "[CLEAN-WEBRTC] Target resolution: " << config_.width << "x" << config_.height << std::endl;
    std::cout << "[CLEAN-WEBRTC] Target framerate: " << config_.framerate << " FPS" << std::endl;
    std::cout << "[CLEAN-WEBRTC] Target bitrate: " << config_.bitrate << " kbps" << std::endl;
    std::cout << "[CLEAN-WEBRTC] Output URL: " << config_.outputUrl << std::endl;
    
    // Initialize DirectX for desktop capture
    if (config_.enableGPUCapture) {
        if (!initializeDirectX()) {
            logError("Failed to initialize DirectX");
            return false;
        }
        
        if (!initializeDesktopDuplication()) {
            logError("Failed to initialize Desktop Duplication");
            return false;
        }
    }
    
    std::cout << "[CLEAN-WEBRTC] Desktop capture initialized successfully" << std::endl;
    return true;
}

void CleanWebRTCDesktopStreamer::onDataChannelOpen() {
    std::cout << "[CLEAN-WEBRTC] DataChannel opened, starting desktop streaming..." << std::endl;
    
    if (!startStreaming()) {
        std::cout << "[CLEAN-WEBRTC] Failed to start desktop streaming" << std::endl;
    } else {
        std::cout << "[CLEAN-WEBRTC] Desktop streaming started successfully" << std::endl;
        std::cout << "[CLEAN-WEBRTC] Video stream available at: " << config_.outputUrl << std::endl;
    }
}

void CleanWebRTCDesktopStreamer::onDataChannelClose() {
    std::cout << "[CLEAN-WEBRTC] DataChannel closed, stopping desktop streaming..." << std::endl;
    stopStreaming();
}

void CleanWebRTCDesktopStreamer::onDataChannelMessage(const std::string& message) {
    std::cout << "[CLEAN-WEBRTC] Received message: " << message << std::endl;
    
    // Handle control messages from frontend
    if (message == "start_streaming") {
        startStreaming();
    } else if (message == "stop_streaming") {
        stopStreaming();
    } else if (message == "get_stats") {
        // Send stats back through DataChannel
        auto stats = getStats();
        std::stringstream statsJson;
        statsJson << "{";
        statsJson << "\"framesCaptured\":" << stats.framesCaptured << ",";
        statsJson << "\"framesStreamed\":" << stats.framesStreamed << ",";
        statsJson << "\"framesDropped\":" << stats.framesDropped << ",";
        statsJson << "\"averageFPS\":" << stats.averageFPS << ",";
        statsJson << "\"isStreaming\":" << (stats.isStreaming ? "true" : "false") << ",";
        statsJson << "\"ffmpegRunning\":" << (stats.ffmpegRunning ? "true" : "false");
        statsJson << "}";
        
        std::cout << "[CLEAN-WEBRTC] Stats: " << statsJson.str() << std::endl;
    }
}

bool CleanWebRTCDesktopStreamer::startStreaming() {
    if (streaming_.load()) {
        std::cout << "[CLEAN-WEBRTC] Streaming already active" << std::endl;
        return true;
    }

    std::cout << "[CLEAN-WEBRTC] Starting desktop capture and streaming..." << std::endl;

    // Check if desktop duplication is available
    if (!deskDupl_) {
        std::cout << "[CLEAN-WEBRTC] Desktop duplication not available, cannot start streaming" << std::endl;
        return false;
    }

    // Start FFmpeg process if streaming is enabled
    if (config_.enableFFmpegStreaming) {
        if (!startFFmpegProcess()) {
            logError("Failed to start FFmpeg process");
            return false;
        }
    }

    streaming_ = true;
    captureThread_ = std::thread(&CleanWebRTCDesktopStreamer::captureLoop, this);

    std::cout << "[CLEAN-WEBRTC] Desktop capture and streaming started" << std::endl;
    return true;
}

void CleanWebRTCDesktopStreamer::stopStreaming() {
    if (!streaming_.load()) {
        return;
    }
    
    std::cout << "[CLEAN-WEBRTC] Stopping desktop capture and streaming..." << std::endl;
    
    streaming_ = false;
    
    if (captureThread_.joinable()) {
        captureThread_.join();
    }
    
    stopFFmpegProcess();
    
    std::cout << "[CLEAN-WEBRTC] Desktop capture and streaming stopped" << std::endl;
}

CleanWebRTCDesktopStreamer::StreamStats CleanWebRTCDesktopStreamer::getStats() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

void CleanWebRTCDesktopStreamer::resetStats() {
    std::lock_guard<std::mutex> lock(statsMutex_);
    stats_ = StreamStats{};
    stats_.startTime = std::chrono::steady_clock::now();
    lastStatsUpdate_ = stats_.startTime;
}

bool CleanWebRTCDesktopStreamer::updateConfig(const StreamConfig& config) {
    if (streaming_.load()) {
        std::cout << "[CLEAN-WEBRTC] Cannot update config while streaming" << std::endl;
        return false;
    }
    
    config_ = config;
    return true;
}

bool CleanWebRTCDesktopStreamer::initializeDirectX() {
    std::cout << "[CLEAN-WEBRTC] Initializing DirectX..." << std::endl;
    
    // Create D3D11 device
    D3D_FEATURE_LEVEL featureLevel;
    HRESULT hr = D3D11CreateDevice(
        nullptr,                    // Adapter
        D3D_DRIVER_TYPE_HARDWARE,   // Driver Type
        nullptr,                    // Software
        0,                          // Flags
        nullptr,                    // Feature Levels
        0,                          // Num Feature Levels
        D3D11_SDK_VERSION,          // SDK Version
        &d3dDevice_,                // Device
        &featureLevel,              // Feature Level
        &d3dContext_                // Device Context
    );
    
    if (FAILED(hr)) {
        logError("Failed to create D3D11 device", hr);
        return false;
    }
    
    std::cout << "[CLEAN-WEBRTC] DirectX initialized successfully" << std::endl;
    return true;
}

bool CleanWebRTCDesktopStreamer::initializeDesktopDuplication() {
    std::cout << "[CLEAN-WEBRTC] Initializing Desktop Duplication..." << std::endl;
    
    // Get DXGI device
    IDXGIDevice* dxgiDevice = nullptr;
    HRESULT hr = d3dDevice_->QueryInterface(__uuidof(IDXGIDevice), (void**)&dxgiDevice);
    if (FAILED(hr)) {
        logError("Failed to get DXGI device", hr);
        return false;
    }
    
    // Get DXGI adapter
    IDXGIAdapter* dxgiAdapter = nullptr;
    hr = dxgiDevice->GetAdapter(&dxgiAdapter);
    dxgiDevice->Release();
    if (FAILED(hr)) {
        logError("Failed to get DXGI adapter", hr);
        return false;
    }
    
    // Get output
    IDXGIOutput* dxgiOutput = nullptr;
    hr = dxgiAdapter->EnumOutputs(0, &dxgiOutput);
    dxgiAdapter->Release();
    if (FAILED(hr)) {
        logError("Failed to get DXGI output", hr);
        return false;
    }
    
    // Get output1
    hr = dxgiOutput->QueryInterface(__uuidof(IDXGIOutput1), (void**)&dxgiOutput1_);
    dxgiOutput->Release();
    if (FAILED(hr)) {
        logError("Failed to get DXGI output1", hr);
        return false;
    }
    
    // Create desktop duplication
    hr = dxgiOutput1_->DuplicateOutput(d3dDevice_, &deskDupl_);
    if (FAILED(hr)) {
        logError("Failed to create desktop duplication", hr);

        // Provide detailed error information and solutions
        std::cout << "[CLEAN-WEBRTC] Desktop Duplication Error Analysis:" << std::endl;
        if (hr == DXGI_ERROR_UNSUPPORTED) {
            std::cout << "[CLEAN-WEBRTC] Error: DXGI_ERROR_UNSUPPORTED" << std::endl;
            std::cout << "[CLEAN-WEBRTC] Possible solutions:" << std::endl;
            std::cout << "[CLEAN-WEBRTC] 1. Run as Administrator" << std::endl;
            std::cout << "[CLEAN-WEBRTC] 2. Update graphics drivers" << std::endl;
            std::cout << "[CLEAN-WEBRTC] 3. Check if running in VM/RDP (not supported)" << std::endl;
        } else if (hr == E_ACCESSDENIED) {
            std::cout << "[CLEAN-WEBRTC] Error: Access Denied" << std::endl;
            std::cout << "[CLEAN-WEBRTC] Solution: Run as Administrator" << std::endl;
        } else if (hr == DXGI_ERROR_SESSION_DISCONNECTED) {
            std::cout << "[CLEAN-WEBRTC] Error: Session Disconnected" << std::endl;
            std::cout << "[CLEAN-WEBRTC] Solution: Ensure local desktop session" << std::endl;
        }
        std::cout << "[CLEAN-WEBRTC] Falling back to alternative capture method..." << std::endl;
        return false;
    }
    
    // Create staging texture for CPU access
    D3D11_TEXTURE2D_DESC stagingDesc = {};
    stagingDesc.Width = config_.width;
    stagingDesc.Height = config_.height;
    stagingDesc.MipLevels = 1;
    stagingDesc.ArraySize = 1;
    stagingDesc.Format = DXGI_FORMAT_B8G8R8A8_UNORM;
    stagingDesc.SampleDesc.Count = 1;
    stagingDesc.Usage = D3D11_USAGE_STAGING;
    stagingDesc.CPUAccessFlags = D3D11_CPU_ACCESS_READ;
    
    hr = d3dDevice_->CreateTexture2D(&stagingDesc, nullptr, &stagingTexture_);
    if (FAILED(hr)) {
        logError("Failed to create staging texture", hr);
        return false;
    }
    
    std::cout << "[CLEAN-WEBRTC] Desktop Duplication initialized successfully" << std::endl;
    return true;
}

std::string CleanWebRTCDesktopStreamer::buildFFmpegCommand() {
    std::stringstream cmd;

    // FFmpeg command for raw video input and hardware encoding
    cmd << "\"" << config_.ffmpegPath << "\"";
    cmd << " -f rawvideo";
    cmd << " -pixel_format bgra";
    cmd << " -video_size " << config_.width << "x" << config_.height;
    cmd << " -framerate " << config_.framerate;
    cmd << " -i -";  // Read from stdin

    // Try NVIDIA hardware encoding first
    cmd << " -c:v " << config_.encoder;
    cmd << " -preset fast";
    // Note: h264_nvenc doesn't support tune zerolatency, use preset instead
    if (config_.encoder == "h264_nvenc") {
        cmd << " -preset llhq";  // Low latency high quality for NVENC
        cmd << " -rc constqp";   // Constant QP for low latency
        cmd << " -qp 23";        // Quality parameter
    } else {
        cmd << " -tune zerolatency";  // Only for software encoder
    }
    cmd << " -b:v " << config_.bitrate << "k";
    cmd << " -maxrate " << (config_.bitrate * 1.2) << "k";
    cmd << " -bufsize " << (config_.bitrate * 2) << "k";
    cmd << " -g " << config_.framerate;  // GOP size = framerate (1 second)
    cmd << " -keyint_min " << config_.framerate;

    // Output format and destination
    cmd << " -f h264";
    cmd << " " << config_.outputUrl;

    // Additional options for low latency
    cmd << " -fflags +genpts";
    cmd << " -avoid_negative_ts make_zero";
    cmd << " -loglevel warning";

    return cmd.str();
}

bool CleanWebRTCDesktopStreamer::startFFmpegProcess() {
    std::string command = buildFFmpegCommand();
    std::cout << "[CLEAN-WEBRTC] FFmpeg command: " << command << std::endl;

    // Create pipes for stdin
    HANDLE hStdinRead, hStdinWrite;
    SECURITY_ATTRIBUTES saAttr;
    saAttr.nLength = sizeof(SECURITY_ATTRIBUTES);
    saAttr.bInheritHandle = TRUE;
    saAttr.lpSecurityDescriptor = nullptr;

    if (!CreatePipe(&hStdinRead, &hStdinWrite, &saAttr, 0)) {
        logError("Failed to create stdin pipe");
        return false;
    }

    // Ensure the write handle is not inherited
    if (!SetHandleInformation(hStdinWrite, HANDLE_FLAG_INHERIT, 0)) {
        logError("Failed to set handle information");
        CloseHandle(hStdinRead);
        CloseHandle(hStdinWrite);
        return false;
    }

    // Create process
    STARTUPINFOA si = {};
    PROCESS_INFORMATION pi = {};
    si.cb = sizeof(STARTUPINFOA);
    si.hStdInput = hStdinRead;
    si.hStdOutput = GetStdHandle(STD_OUTPUT_HANDLE);
    si.hStdError = GetStdHandle(STD_ERROR_HANDLE);
    si.dwFlags |= STARTF_USESTDHANDLES;

    if (!CreateProcessA(
        nullptr,                    // Application name
        const_cast<char*>(command.c_str()), // Command line
        nullptr,                    // Process security attributes
        nullptr,                    // Thread security attributes
        TRUE,                       // Inherit handles
        0,                          // Creation flags
        nullptr,                    // Environment
        nullptr,                    // Current directory
        &si,                        // Startup info
        &pi                         // Process info
    )) {
        logError("Failed to create FFmpeg process");
        CloseHandle(hStdinRead);
        CloseHandle(hStdinWrite);
        return false;
    }

    // Store handles
    ffmpegProcess_ = pi.hProcess;
    ffmpegStdin_ = hStdinWrite;

    // Close handles we don't need
    CloseHandle(pi.hThread);
    CloseHandle(hStdinRead);

    std::cout << "[CLEAN-WEBRTC] FFmpeg process started successfully" << std::endl;
    return true;
}

void CleanWebRTCDesktopStreamer::stopFFmpegProcess() {
    if (ffmpegStdin_) {
        CloseHandle(ffmpegStdin_);
        ffmpegStdin_ = nullptr;
    }

    if (ffmpegProcess_) {
        // Wait for process to terminate gracefully
        if (WaitForSingleObject(ffmpegProcess_, 3000) == WAIT_TIMEOUT) {
            // Force terminate if it doesn't exit gracefully
            TerminateProcess(ffmpegProcess_, 0);
        }
        CloseHandle(ffmpegProcess_);
        ffmpegProcess_ = nullptr;
    }

    std::cout << "[CLEAN-WEBRTC] FFmpeg process stopped" << std::endl;
}

void CleanWebRTCDesktopStreamer::captureLoop() {
    std::cout << "[CLEAN-WEBRTC] Starting capture loop..." << std::endl;

    auto frameInterval = std::chrono::microseconds(1000000 / config_.framerate);
    auto nextFrameTime = std::chrono::steady_clock::now();

    while (streaming_.load()) {
        auto currentTime = std::chrono::steady_clock::now();

        if (currentTime >= nextFrameTime) {
            if (captureFrame()) {
                updateStats();
            }

            nextFrameTime += frameInterval;

            // If we're behind, skip frames to catch up
            if (nextFrameTime < currentTime) {
                nextFrameTime = currentTime + frameInterval;
            }
        } else {
            // Sleep until next frame
            std::this_thread::sleep_until(nextFrameTime);
        }
    }

    std::cout << "[CLEAN-WEBRTC] Capture loop ended" << std::endl;
}

bool CleanWebRTCDesktopStreamer::captureFrame() {
    if (!config_.enableGPUCapture) {
        return false;
    }

    // Acquire next frame
    IDXGIResource* desktopResource = nullptr;
    DXGI_OUTDUPL_FRAME_INFO frameInfo;

    HRESULT hr = deskDupl_->AcquireNextFrame(0, &frameInfo, &desktopResource);
    if (hr == DXGI_ERROR_WAIT_TIMEOUT) {
        // No new frame available
        return true;
    }

    if (FAILED(hr)) {
        if (hr == DXGI_ERROR_ACCESS_LOST) {
            std::cout << "[CLEAN-WEBRTC] Desktop duplication access lost, reinitializing..." << std::endl;
            // Reinitialize desktop duplication
            if (deskDupl_) {
                deskDupl_->Release();
                deskDupl_ = nullptr;
            }
            if (initializeDesktopDuplication()) {
                return true; // Try again next frame
            }
        }
        logError("Failed to acquire next frame", hr);
        return false;
    }

    // Get texture from resource
    ID3D11Texture2D* acquiredTexture = nullptr;
    hr = desktopResource->QueryInterface(__uuidof(ID3D11Texture2D), (void**)&acquiredTexture);
    desktopResource->Release();

    if (FAILED(hr)) {
        logError("Failed to get texture from resource", hr);
        deskDupl_->ReleaseFrame();
        return false;
    }

    // Copy to staging texture and extract frame data
    bool success = false;
    if (copyTextureToStagingTexture(acquiredTexture, stagingTexture_)) {
        std::vector<uint8_t> frameData = extractFrameData(stagingTexture_);
        if (!frameData.empty() && config_.enableFFmpegStreaming) {
            success = sendFrameToFFmpeg(frameData);
        } else {
            success = true; // Frame captured successfully even if not streamed
        }
    }

    // Cleanup
    acquiredTexture->Release();
    deskDupl_->ReleaseFrame();

    if (success) {
        std::lock_guard<std::mutex> lock(statsMutex_);
        stats_.framesCaptured++;
        if (config_.enableFFmpegStreaming) {
            stats_.framesStreamed++;
        }
    } else {
        std::lock_guard<std::mutex> lock(statsMutex_);
        stats_.framesDropped++;
    }

    return success;
}

bool CleanWebRTCDesktopStreamer::copyTextureToStagingTexture(ID3D11Texture2D* source, ID3D11Texture2D* dest) {
    if (!source || !dest) {
        return false;
    }

    // Get source description
    D3D11_TEXTURE2D_DESC srcDesc;
    source->GetDesc(&srcDesc);

    // Create a copy region
    D3D11_BOX srcBox = {};
    srcBox.left = 0;
    srcBox.top = 0;
    srcBox.front = 0;
    srcBox.right = (std::min)((UINT)config_.width, srcDesc.Width);
    srcBox.bottom = (std::min)((UINT)config_.height, srcDesc.Height);
    srcBox.back = 1;

    // Copy the texture
    d3dContext_->CopySubresourceRegion(dest, 0, 0, 0, 0, source, 0, &srcBox);

    return true;
}

std::vector<uint8_t> CleanWebRTCDesktopStreamer::extractFrameData(ID3D11Texture2D* texture) {
    std::vector<uint8_t> frameData;

    // Map staging texture
    D3D11_MAPPED_SUBRESOURCE mappedResource;
    HRESULT hr = d3dContext_->Map(texture, 0, D3D11_MAP_READ, 0, &mappedResource);
    if (FAILED(hr)) {
        logError("Failed to map staging texture", hr);
        return frameData;
    }

    // Calculate frame size
    size_t frameSize = config_.width * config_.height * 4; // BGRA = 4 bytes per pixel
    frameData.resize(frameSize);

    // Copy frame data
    uint8_t* srcData = (uint8_t*)mappedResource.pData;
    uint8_t* dstData = frameData.data();

    for (int y = 0; y < config_.height; y++) {
        memcpy(dstData + y * config_.width * 4,
               srcData + y * mappedResource.RowPitch,
               config_.width * 4);
    }

    // Unmap texture
    d3dContext_->Unmap(texture, 0);

    return frameData;
}

bool CleanWebRTCDesktopStreamer::sendFrameToFFmpeg(const std::vector<uint8_t>& frameData) {
    if (!ffmpegStdin_ || frameData.empty()) {
        return false;
    }

    DWORD bytesWritten;
    BOOL result = WriteFile(ffmpegStdin_, frameData.data(), (DWORD)frameData.size(), &bytesWritten, nullptr);

    if (!result || bytesWritten != frameData.size()) {
        logError("Failed to write frame data to FFmpeg");
        return false;
    }

    return true;
}

void CleanWebRTCDesktopStreamer::updateStats() {
    std::lock_guard<std::mutex> lock(statsMutex_);

    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - stats_.startTime);

    if (elapsed.count() > 0) {
        stats_.averageFPS = static_cast<double>(stats_.framesStreamed) / elapsed.count();
    }

    stats_.isStreaming = streaming_.load();

    // Check if FFmpeg process is still running
    if (ffmpegProcess_) {
        DWORD exitCode;
        if (GetExitCodeProcess(ffmpegProcess_, &exitCode)) {
            stats_.ffmpegRunning = (exitCode == STILL_ACTIVE);
        }
    }

    lastStatsUpdate_ = now;
}

void CleanWebRTCDesktopStreamer::cleanupDirectX() {
    if (stagingTexture_) {
        stagingTexture_->Release();
        stagingTexture_ = nullptr;
    }

    if (deskDupl_) {
        deskDupl_->Release();
        deskDupl_ = nullptr;
    }

    if (dxgiOutput1_) {
        dxgiOutput1_->Release();
        dxgiOutput1_ = nullptr;
    }

    if (d3dContext_) {
        d3dContext_->Release();
        d3dContext_ = nullptr;
    }

    if (d3dDevice_) {
        d3dDevice_->Release();
        d3dDevice_ = nullptr;
    }
}

bool CleanWebRTCDesktopStreamer::checkSystemCompatibility() {
    std::cout << "[CLEAN-WEBRTC] Checking system compatibility..." << std::endl;

    // Check DirectX 11 support
    ID3D11Device* testDevice = nullptr;
    D3D_FEATURE_LEVEL featureLevel;
    HRESULT hr = D3D11CreateDevice(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
        nullptr, 0, D3D11_SDK_VERSION,
        &testDevice, &featureLevel, nullptr
    );

    if (FAILED(hr)) {
        std::cout << "[CLEAN-WEBRTC] DirectX 11 not available" << std::endl;
        return false;
    }

    if (testDevice) {
        testDevice->Release();
    }

    std::cout << "[CLEAN-WEBRTC] System compatibility check passed" << std::endl;
    return true;
}

std::string CleanWebRTCDesktopStreamer::getSystemInfo() {
    std::stringstream info;
    info << "=== Clean WebRTC Desktop Capture System Info ===" << std::endl;

    // Check DirectX
    ID3D11Device* testDevice = nullptr;
    D3D_FEATURE_LEVEL featureLevel;
    HRESULT hr = D3D11CreateDevice(
        nullptr, D3D_DRIVER_TYPE_HARDWARE, nullptr, 0,
        nullptr, 0, D3D11_SDK_VERSION,
        &testDevice, &featureLevel, nullptr
    );

    info << "DirectX 11: " << (SUCCEEDED(hr) ? "Available" : "Not Available") << std::endl;
    if (SUCCEEDED(hr)) {
        info << "Feature Level: " << std::hex << featureLevel << std::dec << std::endl;
        if (testDevice) testDevice->Release();
    }

    info << "Desktop Duplication: " << (SUCCEEDED(hr) ? "Supported" : "Not Supported") << std::endl;
    info << "FFmpeg Process: External process (no C API conflicts)" << std::endl;
    info << "Socket Conflicts: Resolved with WIN32_LEAN_AND_MEAN" << std::endl;
    info << "=============================================" << std::endl;

    return info.str();
}

void CleanWebRTCDesktopStreamer::logError(const std::string& message, HRESULT hr) {
    std::string fullMessage = "[CLEAN-WEBRTC ERROR] " + message;
    if (hr != S_OK) {
        fullMessage += " (HRESULT: " + getErrorString(hr) + ")";
    }
    std::cerr << fullMessage << std::endl;
}

std::string CleanWebRTCDesktopStreamer::getErrorString(HRESULT hr) {
    _com_error err(hr);
    LPCTSTR errorMsg = err.ErrorMessage();

    // Convert to std::string
    std::string result;
    if (errorMsg) {
#ifdef UNICODE
        // Convert from wide string to narrow string
        int len = WideCharToMultiByte(CP_UTF8, 0, errorMsg, -1, nullptr, 0, nullptr, nullptr);
        if (len > 0) {
            result.resize(len - 1);
            WideCharToMultiByte(CP_UTF8, 0, errorMsg, -1, &result[0], len, nullptr, nullptr);
        }
#else
        result = errorMsg;
#endif
    }
    return result;
}
