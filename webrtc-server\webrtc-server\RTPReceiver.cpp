#include "RTPReceiver.h"
#include <iostream>
#include <algorithm>

const std::vector<uint8_t> NAL_START_CODE = {0x00, 0x00, 0x00, 0x01};

RTPReceiver::RTPReceiver(int port)
    : port_(port),
      socket_(INVALID_SOCKET),
      running_(false),
      expectedSequence_(0),
      currentTimestamp_(0) {
    WSAData wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        std::cerr << "[RTP] WSAStartup failed: " << result << std::endl;
    }
}

RTPReceiver::~RTPReceiver() {
    stop();
    cleanup();
    WSACleanup();
}

bool RTPReceiver::initializeSocket() {
    socket_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (socket_ == INVALID_SOCKET) {
        std::cerr << "[RTP] Failed to create socket: " << WSAGetLastError() << std::endl;
        return false;
    }

    int reuse = 1;
    if (setsockopt(socket_, SOL_SOCKET, SO_REUSEADDR, (char*)&reuse, sizeof(reuse)) != 0) {
        std::cerr << "[RTP] Warning: Failed to set SO_REUSEADDR: " << WSAGetLastError() << std::endl;
    }

    u_long mode = 1;
    if (ioctlsocket(socket_, FIONBIO, &mode) != 0) {
        std::cerr << "[RTP] Failed to set non-blocking mode: " << WSAGetLastError() << std::endl;
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        return false;
    }

 
    int recvBufSize = 4 * 1024 * 1024; // 4MB
    if (setsockopt(socket_, SOL_SOCKET, SO_RCVBUF, (char*)&recvBufSize, sizeof(recvBufSize)) != 0) {
        std::cerr << "[RTP] Warning: Failed to set receive buffer size: " << WSAGetLastError() << std::endl;
    }

    int sendBufSize = 2 * 1024 * 1024; // 2MB
    if (setsockopt(socket_, SOL_SOCKET, SO_SNDBUF, (char*)&sendBufSize, sizeof(sendBufSize)) != 0) {
        std::cerr << "[RTP] Warning: Failed to set send buffer size: " << WSAGetLastError() << std::endl;
    }

    DWORD timeout = 100; // 100ms
    if (setsockopt(socket_, SOL_SOCKET, SO_RCVTIMEO, (char*)&timeout, sizeof(timeout)) != 0) {
        std::cerr << "[RTP] Warning: Failed to set receive timeout: " << WSAGetLastError() << std::endl;
    }

    sockaddr_in addr = {};
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(port_);


    int maxRetries = 5;
    bool bindSuccess = false;

    for (int retry = 0; retry < maxRetries; retry++) {
        if (bind(socket_, (sockaddr*)&addr, sizeof(addr)) == 0) {
            std::cout << "[OptimizedRTP] Successfully bound to port " << port_;
            if (retry > 0) {
                std::cout << " after " << retry << " retries";
            }
            std::cout << std::endl;
            bindSuccess = true;
            break;
        }

        int error = WSAGetLastError();
        if (error == WSAEADDRINUSE) {
            std::cout << "[OptimizedRTP] Port " << port_ << " in use, retry " << (retry + 1) << "/" << maxRetries << std::endl;

            std::this_thread::sleep_for(std::chrono::milliseconds(500 * (retry + 1)));

            if (retry == 2) {
                std::cout << "[OptimizedRTP] Attempting to force release port..." << std::endl;
                SOCKET tempSocket = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
                if (tempSocket != INVALID_SOCKET) {
                    int tempReuse = 1;
                    setsockopt(tempSocket, SOL_SOCKET, SO_REUSEADDR, (char*)&tempReuse, sizeof(tempReuse));
                    bind(tempSocket, (sockaddr*)&addr, sizeof(addr));
                    closesocket(tempSocket);
                    std::this_thread::sleep_for(std::chrono::milliseconds(200));
                }
            }
        } else {
            std::cerr << "[RTP] Bind failed with error " << error << " on retry " << retry << std::endl;
            break;
        }
    }

    if (!bindSuccess) {
        std::cerr << "[RTP] Failed to bind socket after " << maxRetries << " retries" << std::endl;
        cleanup();
        return false;
    }
    return true;
}

bool RTPReceiver::start() {
    if (running_.load()) {
        return true;
    }

    if (!initializeSocket()) {
        return false;
    }

    running_ = true;
    receiveThread_ = std::thread(&RTPReceiver::receiveLoop, this);
    std::cout << "[RTP] Receiver started on port " << port_ << std::endl;
    return true;
}

void RTPReceiver::stop() {
    running_ = false;

    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }

    if (receiveThread_.joinable()) {
        receiveThread_.join();
    }

    std::cout << "[RTP] Stopped" << std::endl;
}

void RTPReceiver::cleanup() {
    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }

    std::lock_guard<std::mutex> lock(frameMutex_);
    packetBuffer_.clear();
    currentFrame_.clear();
}

void RTPReceiver::setOnFrameReceived(std::function<void(const std::vector<uint8_t>&)> callback) {
    onFrameReceived_ = callback;
}

void RTPReceiver::receiveLoop() {
    std::vector<uint8_t> buffer(131072); // 128KB
    sockaddr_in senderAddr;
    int senderAddrSize = sizeof(senderAddr);

    std::cout << "[RTP] Receive loop started with enhanced buffer" << std::endl;

    auto lastStatsTime = std::chrono::steady_clock::now();
    uint64_t packetsThisInterval = 0;
    uint64_t consecutiveErrors = 0;

    while (running_.load()) {
        senderAddrSize = sizeof(senderAddr); 
        int bytesReceived = recvfrom(socket_,
                                   reinterpret_cast<char*>(buffer.data()),
                                   static_cast<int>(buffer.size()),
                                   0,
                                   (sockaddr*)&senderAddr,
                                   &senderAddrSize);

        if (bytesReceived > 0) {
            stats_.packetsReceived++;
            stats_.bytesReceived += bytesReceived;
            packetsThisInterval++;
            consecutiveErrors = 0; 

            std::vector<uint8_t> data(buffer.begin(), buffer.begin() + bytesReceived);

            processRTPPacket(data);

            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStatsTime).count() >= 1) {
                std::cout << "[RTP] Stats: " << packetsThisInterval << " packets/sec, "
                          << "Total: " << stats_.packetsReceived << " packets, "
                          << stats_.framesAssembled << " frames" << std::endl;
                lastStatsTime = now;
                packetsThisInterval = 0;
            }
        }
        else if (bytesReceived == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK && error != WSAETIMEDOUT) {
                consecutiveErrors++; 
                std::cerr << "[RTP] Error receiving data: " << error << " (consecutive: " << consecutiveErrors << ")" << std::endl;

                if (consecutiveErrors > 10) {
                    std::cerr << "[RTP] Too many consecutive errors, reinitializing socket..." << std::endl;
                    closesocket(socket_);
                    if (initializeSocket()) {
                        consecutiveErrors = 0;
                        std::cout << "[RTP] Socket reinitialized successfully" << std::endl;
                    } else {
                        std::cerr << "[RTP] Failed to reinitialize socket, stopping..." << std::endl;
                        break;
                    }
                }

                if (error == WSAECONNRESET || error == WSAENETDOWN) {
                    break;
                }
            }
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    std::cout << "[RTP] Receive loop ended" << std::endl;
}

bool RTPReceiver::parseRTPHeader(const std::vector<uint8_t>& data, RTPHeader& header, std::vector<uint8_t>& payload) {
    if (data.size() < 12) { 
        std::cerr << "[RTP] Packet too small: " << data.size() << " bytes" << std::endl;
        return false;
    }

    header.version = (data[0] >> 6) & 0x03;
    header.padding = (data[0] >> 5) & 0x01;
    header.extension = (data[0] >> 4) & 0x01;
    header.csrc_count = data[0] & 0x0F;

    header.marker = (data[1] >> 7) & 0x01;
    header.payload_type = data[1] & 0x7F;

    header.sequence_number = (data[2] << 8) | data[3];
    header.timestamp = (data[4] << 24) | (data[5] << 16) | (data[6] << 8) | data[7];
    header.ssrc = (data[8] << 24) | (data[9] << 16) | (data[10] << 8) | data[11];

    if (header.version != 2) {
        std::cerr << "[RTP] Invalid RTP version: " << (int)header.version << std::endl;
        return false;
    }

    size_t headerSize = 12 + (header.csrc_count * 4);

    if (header.extension) {
        if (data.size() < headerSize + 4) {
            std::cerr << "[RTP] Extension header incomplete" << std::endl;
            return false;
        }
        uint16_t extLength = (data[headerSize + 2] << 8) | data[headerSize + 3];
        headerSize += 4 + (extLength * 4);
    }

    if (data.size() <= headerSize) {
        std::cerr << "[RTP] No payload data" << std::endl;
        return false;
    }
    payload.assign(data.begin() + headerSize, data.end());
    if (header.padding && !payload.empty()) {
        uint8_t paddingLength = payload.back();
        if (paddingLength <= payload.size()) {
            payload.resize(payload.size() - paddingLength);
        }
    }

    return true;
}

void RTPReceiver::processRTPPacket(const std::vector<uint8_t>& data) {
    RTPHeader header;
    std::vector<uint8_t> payload;

    if (!parseRTPHeader(data, header, payload)) {
        stats_.packetsLost++;
        return;
    }

    if (header.payload_type != 96) {
        return;
    }

    std::vector<uint8_t> h264Data = processH264Payload(payload, header);

    if (!h264Data.empty()) {
        std::lock_guard<std::mutex> lock(frameMutex_);

        if (currentTimestamp_ != 0 && header.timestamp != currentTimestamp_) {
            if (!currentFrame_.empty()) {
                if (onFrameReceived_) {
                    if (isValidH264Frame(currentFrame_)) {
                        onFrameReceived_(currentFrame_);
                        stats_.framesAssembled++;
                    } else {
                        std::cerr << "[RTP] Warning: Invalid H264 frame discarded (size: " << currentFrame_.size() << ")" << std::endl;
                        stats_.packetsLost++;
                    }
                }
                currentFrame_.clear();
            }
        }

        currentTimestamp_ = header.timestamp;

        currentFrame_.insert(currentFrame_.end(), h264Data.begin(), h264Data.end());

        if (header.marker && !currentFrame_.empty()) {
            if (onFrameReceived_) {
                if (isValidH264Frame(currentFrame_)) {
                    onFrameReceived_(currentFrame_);
                    stats_.framesAssembled++;

                    if (isKeyFrame(currentFrame_)) {
                        std::cout << "[RTP] Complete key frame assembled (size: " << currentFrame_.size() << " bytes)" << std::endl;
                    }
                } else {
                    std::cerr << "[RTP] Warning: Invalid H264 frame discarded (size: " << currentFrame_.size() << ")" << std::endl;
                    stats_.packetsLost++;
                }
            }
            currentFrame_.clear();
        }
    }

    expectedSequence_ = header.sequence_number + 1;
}

std::vector<uint8_t> RTPReceiver::processH264Payload(const std::vector<uint8_t>& payload, const RTPHeader& header) {
    if (payload.empty()) {
        return {};
    }

    uint8_t nalHeader = payload[0];
    uint8_t nalType = nalHeader & 0x1F;

    std::vector<uint8_t> result;

    switch (nalType) {
        case NAL_FU_A:
            result = processFUA(payload);
            break;

        case NAL_STAP_A:
            result = processSTAPA(payload);
            break;

        default:
            result.reserve(NAL_START_CODE.size() + payload.size());
            result.insert(result.end(), NAL_START_CODE.begin(), NAL_START_CODE.end());
            result.insert(result.end(), payload.begin(), payload.end());

            if (nalType == 7) {
                std::cout << "[RTP] SPS received (type " << (int)nalType << ", size: " << payload.size() << ")" << std::endl;
            } else if (nalType == 8) {
                std::cout << "[RTP] PPS received (type " << (int)nalType << ", size: " << payload.size() << ")" << std::endl;
            } else if (nalType == 5) {
                std::cout << "[RTP] IDR frame received (type " << (int)nalType << ", size: " << payload.size() << ")" << std::endl;
            } else if (nalType == 1) {
                static int pFrameCount = 0;
                if (++pFrameCount % 100 == 1) {
                    std::cout << "[RTP] P frame received (type " << (int)nalType << ", count: " << pFrameCount << ")" << std::endl;
                }
            }
            break;
    }

    return result;
}

std::vector<uint8_t> RTPReceiver::processFUA(const std::vector<uint8_t>& payload) {
    if (payload.size() < 2) {
        return {};
    }

    uint8_t fuIndicator = payload[0];
    uint8_t fuHeader = payload[1];

    bool startBit = (fuHeader & 0x80) != 0;
    bool endBit = (fuHeader & 0x40) != 0;
    uint8_t nalType = fuHeader & 0x1F;

    std::vector<uint8_t> result;

    if (startBit) {
        result.reserve(NAL_START_CODE.size() + 1 + payload.size() - 2);
        result.insert(result.end(), NAL_START_CODE.begin(), NAL_START_CODE.end());

        uint8_t reconstructedNalHeader = (fuIndicator & 0xE0) | nalType;
        result.push_back(reconstructedNalHeader);

        result.insert(result.end(), payload.begin() + 2, payload.end());

        if (isKeyFrame({reconstructedNalHeader})) {
            std::cout << "[RTP] Key frame FU-A start (type " << (int)nalType << ")" << std::endl;
        }
    } else {
        result.reserve(payload.size() - 2);
        result.insert(result.end(), payload.begin() + 2, payload.end());
    }

    return result;
}

std::vector<uint8_t> RTPReceiver::processSTAPA(const std::vector<uint8_t>& payload) {
    if (payload.size() < 3) {
        return {};
    }

    std::vector<uint8_t> result;
    size_t offset = 1; 

    while (offset + 2 < payload.size()) {
        uint16_t nalSize = (payload[offset] << 8) | payload[offset + 1];
        offset += 2;

        if (offset + nalSize > payload.size()) {
            std::cerr << "[RTP] STAP-A NAL size exceeds payload" << std::endl;
            break;
        }

        result.insert(result.end(), NAL_START_CODE.begin(), NAL_START_CODE.end());

        result.insert(result.end(), payload.begin() + offset, payload.begin() + offset + nalSize);

        offset += nalSize;
    }

    return result;
}

void RTPReceiver::addNALStartCode(std::vector<uint8_t>& data) {
    data.insert(data.begin(), NAL_START_CODE.begin(), NAL_START_CODE.end());
}

bool RTPReceiver::isKeyFrame(const std::vector<uint8_t>& nalUnit) {
    if (nalUnit.empty()) {
        return false;
    }

    uint8_t nalType = nalUnit[0] & 0x1F;
    return (nalType == NAL_IDR_SLICE || nalType == NAL_SPS || nalType == NAL_PPS);
}

bool RTPReceiver::isValidH264Frame(const std::vector<uint8_t>& frame) {
    if (frame.size() < 4) {
        return false;
    }

    bool hasValidStartCode = false;
    for (size_t i = 0; i <= frame.size() - 4; i++) {
        if (frame[i] == 0x00 && frame[i + 1] == 0x00 &&
            frame[i + 2] == 0x00 && frame[i + 3] == 0x01) {
            hasValidStartCode = true;
            break;
        }
        if (i <= frame.size() - 3 &&
            frame[i] == 0x00 && frame[i + 1] == 0x00 && frame[i + 2] == 0x01) {
            hasValidStartCode = true;
            break;
        }
    }

    return hasValidStartCode;
}
