#pragma once

#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <thread>
#include <atomic>
#include <functional>
#include <memory>
#include <map>
#include <queue>
#include <mutex>
#include <chrono>

#pragma comment(lib, "ws2_32.lib")

// RTP Header structure (12 bytes minimum)
struct RTPHeader {
    uint8_t version : 2;
    uint8_t padding : 1;
    uint8_t extension : 1;
    uint8_t csrc_count : 4;
    uint8_t marker : 1;
    uint8_t payload_type : 7;
    uint16_t sequence_number;
    uint32_t timestamp;
    uint32_t ssrc;
};

// H264 NAL unit types
enum H264NALType {
    NAL_SLICE = 1,
    NAL_DPA = 2,
    NAL_DPB = 3,
    NAL_DPC = 4,
    NAL_IDR_SLICE = 5,
    NAL_SEI = 6,
    NAL_SPS = 7,
    NAL_PPS = 8,
    NAL_AUD = 9,
    NAL_END_SEQUENCE = 10,
    NAL_END_STREAM = 11,
    NAL_FILLER_DATA = 12,
    NAL_SPS_EXT = 13,
    NAL_STAP_A = 24,
    NAL_STAP_B = 25,
    NAL_MTAP16 = 26,
    NAL_MTAP24 = 27,
    NAL_FU_A = 28,
    NAL_FU_B = 29
};

// Fragmentation Unit header for FU-A packets
struct FUHeader {
    uint8_t start : 1;
    uint8_t end : 1;
    uint8_t reserved : 1;
    uint8_t type : 5;
};

class RTPReceiver {
public:
    RTPReceiver(int port);
    ~RTPReceiver();

    bool start();
    void stop();

    void setOnFrameReceived(std::function<void(const std::vector<uint8_t>&)> callback);

    // Statistics
    struct Stats {
        uint64_t packetsReceived = 0;
        uint64_t packetsLost = 0;
        uint64_t framesAssembled = 0;
        uint64_t bytesReceived = 0;
    };

    Stats getStats() const { return stats_; }

private:
    int port_;
    SOCKET socket_;
    std::atomic<bool> running_;
    std::thread receiveThread_;
    std::function<void(const std::vector<uint8_t>&)> onFrameReceived_;

    // RTP packet reassembly
    std::map<uint16_t, std::vector<uint8_t>> packetBuffer_;
    uint16_t expectedSequence_;
    uint32_t currentTimestamp_;
    std::vector<uint8_t> currentFrame_;
    std::mutex frameMutex_;

    // Statistics
    mutable Stats stats_;

    // Buffer management
    static const size_t MAX_FRAME_SIZE = 1024 * 1024; // 1MB max frame size
    static const size_t MAX_PACKET_BUFFER = 100; // Max packets to buffer

    // Core methods
    void receiveLoop();
    bool initializeSocket();
    void cleanup();
    void processRTPPacket(const std::vector<uint8_t>& data);
    void assembleFrame();

    // RTP parsing methods
    bool parseRTPHeader(const std::vector<uint8_t>& data, RTPHeader& header, std::vector<uint8_t>& payload);
    std::vector<uint8_t> processH264Payload(const std::vector<uint8_t>& payload, const RTPHeader& header);

    // H264 processing methods
    void addNALStartCode(std::vector<uint8_t>& data);
    bool isKeyFrame(const std::vector<uint8_t>& nalUnit);
    bool isValidH264Frame(const std::vector<uint8_t>& frame);
    std::vector<uint8_t> processFUA(const std::vector<uint8_t>& payload);
    std::vector<uint8_t> processSTAPA(const std::vector<uint8_t>& payload);
};
