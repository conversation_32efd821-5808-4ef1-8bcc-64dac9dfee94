#include "UDPReceiver.h"

UDPReceiver::UDPReceiver(int port)
    : port_(port), socket_(INVALID_SOCKET), running_(false) {

    // Initialize Winsock
    WSADATA wsaData;
    int result = WSAStartup(MAKEWORD(2, 2), &wsaData);
    if (result != 0) {
        std::cerr << "[UDP] WSAStartup failed: " << result << std::endl;
    }

    stats_.startTime = std::chrono::steady_clock::now();
}

UDPReceiver::~UDPReceiver() {
    stop();
    cleanup();
    WSACleanup();
}

bool UDPReceiver::start() {
    if (running_.load()) {
        return true;
    }

    if (!initializeSocket()) {
        return false;
    }

    running_ = true;
    receiveThread_ = std::thread(&UDPReceiver::receiveLoop, this);
    processThread_ = std::thread(&UDPReceiver::processLoop, this);

    std::cout << "[UDP] Started listening on port " << port_ << std::endl;
    return true;
}

void UDPReceiver::stop() {
    running_ = false;
    queueCondition_.notify_all();

    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }

    if (receiveThread_.joinable()) {
        receiveThread_.join();
    }

    if (processThread_.joinable()) {
        processThread_.join();
    }

    std::cout << "[UDP] Stopped listening" << std::endl;
}

void UDPReceiver::setOnDataReceived(std::function<void(const std::vector<uint8_t>&)> callback) {
    onDataReceived_ = callback;
}

UDPReceiver::Stats UDPReceiver::getStats() const {
    std::lock_guard<std::mutex> lock(statsMutex_);
    return stats_;
}

void UDPReceiver::resetStats() {
    std::lock_guard<std::mutex> lock(statsMutex_);
    stats_ = Stats{};
    stats_.startTime = std::chrono::steady_clock::now();
}

bool UDPReceiver::initializeSocket() {
    socket_ = socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
    if (socket_ == INVALID_SOCKET) {
        std::cerr << "[UDP] Failed to create socket: " << WSAGetLastError() << std::endl;
        return false;
    }

    u_long mode = 1;
    if (ioctlsocket(socket_, FIONBIO, &mode) != 0) {
        std::cerr << "[UDP] Failed to set non-blocking mode: " << WSAGetLastError() << std::endl;
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        return false;
    }

    int recvBufSize = 2 * 1024 * 1024; // 2MB
    if (setsockopt(socket_, SOL_SOCKET, SO_RCVBUF, (char*)&recvBufSize, sizeof(recvBufSize)) != 0) {
        std::cerr << "[UDP] Warning: Failed to set receive buffer size: " << WSAGetLastError() << std::endl;
    }
    sockaddr_in addr;
    addr.sin_family = AF_INET;
    addr.sin_addr.s_addr = INADDR_ANY;
    addr.sin_port = htons(port_);

    if (bind(socket_, (sockaddr*)&addr, sizeof(addr)) == SOCKET_ERROR) {
        std::cerr << "[UDP] Failed to bind port: " << WSAGetLastError() << std::endl;
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
        return false;
    }

    return true;
}

void UDPReceiver::receiveLoop() {
    std::vector<uint8_t> buffer(65536); // 64KB buffer
    sockaddr_in senderAddr;
    int senderAddrSize = sizeof(senderAddr);

    std::cout << "[UDP] Receive loop started" << std::endl;

    auto lastStatsTime = std::chrono::steady_clock::now();
    uint64_t packetsThisInterval = 0;

    while (running_.load()) {
        senderAddrSize = sizeof(senderAddr); // Reset size for each call

        int bytesReceived = recvfrom(socket_,
                                   reinterpret_cast<char*>(buffer.data()),
                                   static_cast<int>(buffer.size()),
                                   0,
                                   (sockaddr*)&senderAddr,
                                   &senderAddrSize);

        if (bytesReceived > 0) {
            {
                std::lock_guard<std::mutex> lock(statsMutex_);
                stats_.packetsReceived++;
                stats_.bytesReceived += bytesReceived;
                stats_.averagePacketSize = static_cast<double>(stats_.bytesReceived) / stats_.packetsReceived;
            }

            packetsThisInterval++;

            std::vector<uint8_t> data(buffer.begin(), buffer.begin() + bytesReceived);

 
            {
                std::lock_guard<std::mutex> lock(queueMutex_);
                if (dataQueue_.size() < MAX_QUEUE_SIZE) {
                    dataQueue_.push(std::move(data));
                    queueCondition_.notify_one();
                } else {
                   
                    std::lock_guard<std::mutex> statsLock(statsMutex_);
                    stats_.packetsDropped++;
                }
            }
            auto now = std::chrono::steady_clock::now();
            if (std::chrono::duration_cast<std::chrono::seconds>(now - lastStatsTime).count() >= 1) {
                std::cout << "[UDP] Stats: " << packetsThisInterval << " packets/sec, "
                          << "Total: " << stats_.packetsReceived << " packets, "
                          << stats_.packetsDropped << " dropped" << std::endl;
                lastStatsTime = now;
                packetsThisInterval = 0;
            }
        }
        else if (bytesReceived == SOCKET_ERROR) {
            int error = WSAGetLastError();
            if (error != WSAEWOULDBLOCK) {
                std::cerr << "[UDP] Error receiving data: " << error << std::endl;
                if (error == WSAECONNRESET || error == WSAENETDOWN) {
                    break; // Fatal errors
                }
            }
        }

        // Brief sleep to avoid high CPU usage
        std::this_thread::sleep_for(std::chrono::milliseconds(1));
    }

    std::cout << "[UDP] Receive loop ended" << std::endl;
}

void UDPReceiver::processLoop() {
    std::cout << "[UDP] Process loop started" << std::endl;

    while (running_.load()) {
        std::unique_lock<std::mutex> lock(queueMutex_);
        queueCondition_.wait(lock, [this] {
            return !dataQueue_.empty() || !running_.load();
        });

        if (!running_.load()) {
            break;
        }

        while (!dataQueue_.empty()) {
            std::vector<uint8_t> data = std::move(dataQueue_.front());
            dataQueue_.pop();
            lock.unlock();
            processH264Data(data);

            lock.lock();
        }
    }

    std::cout << "[UDP] Process loop ended" << std::endl;
}

void UDPReceiver::processH264Data(const std::vector<uint8_t>& data) {
    std::lock_guard<std::mutex> lock(frameBufferMutex_);

    frameBuffer_.insert(frameBuffer_.end(), data.begin(), data.end());

    if (frameBuffer_.size() > MAX_FRAME_SIZE) {
        size_t keepSize = MAX_FRAME_SIZE / 2;
        frameBuffer_.erase(frameBuffer_.begin(), frameBuffer_.end() - keepSize);
        std::cout << "[UDP] Frame buffer trimmed to " << keepSize << " bytes" << std::endl;
    }

    auto frames = extractH264Frames(frameBuffer_);

    for (const auto& frame : frames) {
        if (onDataReceived_) {
            try {
                onDataReceived_(frame);
            } catch (const std::exception& e) {
                std::cerr << "[UDP] Error in data callback: " << e.what() << std::endl;
            }
        }
    }

    if (!frames.empty()) {
        size_t totalProcessed = 0;
        for (const auto& frame : frames) {
            totalProcessed += frame.size();
        }
        if (totalProcessed < frameBuffer_.size()) {
            std::vector<uint8_t> remaining(frameBuffer_.begin() + totalProcessed, frameBuffer_.end());
            frameBuffer_ = std::move(remaining);
        } else {
            frameBuffer_.clear();
        }
    }
}

bool UDPReceiver::isH264StartCode(const std::vector<uint8_t>& data, size_t offset) {
    if (offset + 4 > data.size()) {
        return false;
    }

    return (data[offset] == 0x00 &&
            data[offset + 1] == 0x00 &&
            data[offset + 2] == 0x00 &&
            data[offset + 3] == 0x01);
}

std::vector<std::vector<uint8_t>> UDPReceiver::extractH264Frames(const std::vector<uint8_t>& data) {
    std::vector<std::vector<uint8_t>> frames;

    if (data.size() < 4) {
        return frames;
    }

    size_t frameStart = 0;
    bool foundFirstFrame = false;

    for (size_t i = 0; i <= data.size() - 4; ++i) {
        if (isH264StartCode(data, i)) {
            if (foundFirstFrame) {
                std::vector<uint8_t> frame(data.begin() + frameStart, data.begin() + i);
                frames.push_back(std::move(frame));
            }
            frameStart = i;
            foundFirstFrame = true;
        }
    }

    if (foundFirstFrame && frameStart < data.size()) {
        size_t remainingSize = data.size() - frameStart;
        if (remainingSize > 100) { 
            std::vector<uint8_t> frame(data.begin() + frameStart, data.end());
            frames.push_back(std::move(frame));
        }
    }

    return frames;
}

void UDPReceiver::cleanup() {
    if (socket_ != INVALID_SOCKET) {
        closesocket(socket_);
        socket_ = INVALID_SOCKET;
    }

    std::lock_guard<std::mutex> lock(queueMutex_);
    while (!dataQueue_.empty()) {
        dataQueue_.pop();
    }

    std::lock_guard<std::mutex> frameLock(frameBufferMutex_);
    frameBuffer_.clear();
}
