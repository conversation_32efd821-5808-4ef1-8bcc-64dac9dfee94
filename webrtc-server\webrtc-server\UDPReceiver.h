#pragma once

#include <winsock2.h>
#include <ws2tcpip.h>
#include <iostream>
#include <vector>
#include <thread>
#include <atomic>
#include <functional>
#include <memory>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <chrono>

#pragma comment(lib, "ws2_32.lib")

class UDPReceiver {
public:
    UDPReceiver(int port);
    ~UDPReceiver();

    bool start();
    void stop();

    void setOnDataReceived(std::function<void(const std::vector<uint8_t>&)> callback);

    struct Stats {
        uint64_t packetsReceived = 0;
        uint64_t bytesReceived = 0;
        uint64_t packetsDropped = 0;
        double averagePacketSize = 0.0;
        std::chrono::steady_clock::time_point startTime;
    };

    Stats getStats() const;
    void resetStats();

private:
    int port_;
    SOCKET socket_;
    std::atomic<bool> running_;
    std::thread receiveThread_;
    std::thread processThread_;
    std::function<void(const std::vector<uint8_t>&)> onDataReceived_;

    std::queue<std::vector<uint8_t>> dataQueue_;
    std::mutex queueMutex_;
    std::condition_variable queueCondition_;
    static const size_t MAX_QUEUE_SIZE = 100;

    mutable std::mutex statsMutex_;
    Stats stats_;

    std::vector<uint8_t> frameBuffer_;
    std::mutex frameBufferMutex_;
    static const size_t MAX_FRAME_SIZE = 2 * 1024 * 1024; // 2MB

    bool initializeSocket();
    void receiveLoop();
    void processLoop();
    void cleanup();
    bool isH264StartCode(const std::vector<uint8_t>& data, size_t offset);
    std::vector<std::vector<uint8_t>> extractH264Frames(const std::vector<uint8_t>& data);
    void processH264Data(const std::vector<uint8_t>& data);
};
