#include "WebRTCManager.h"
#include "AdaptiveQualityController.h"
#include <iostream>
#include <thread>
#include <chrono>
#include <algorithm>

using json = nlohmann::json;

WebRTCManager::WebRTCManager() : streaming(false) {
    rtc::InitLogger(rtc::LogLevel::Debug);
    rtc::Preload();

    // Initialize quality controller
    initializeQualityController();

    // Initialize encoder config with defaults
    encoderConfig.width = 1920;
    encoderConfig.height = 1080;
    encoderConfig.framerate = 30;
    encoderConfig.bitrate = 2000;
    encoderConfig.maxBitrate = 4000;
    encoderConfig.minBitrate = 500;
    encoderConfig.hardwareAcceleration = true;
    encoderConfig.adaptiveBitrate = true;

    // Initialize statistics
    videoStats.lastUpdate = std::chrono::steady_clock::now();
    audioStats.lastUpdate = std::chrono::steady_clock::now();

    // 🚀 初始化传输调度器
    lastVideoSend = std::chrono::steady_clock::now();
    lastAudioSend = std::chrono::steady_clock::now();
}

WebRTCManager::~WebRTCManager() {
    streaming = false;

    if (httpServer) {
        httpServer->stop();
    }

    resetConnection();
}

void WebRTCManager::initializeQualityController() {
    qualityController = std::make_unique<AdaptiveQualityController>();
    qualityController->setMinBitrate(encoderConfig.minBitrate);
    qualityController->setMaxBitrate(encoderConfig.maxBitrate);
}

void WebRTCManager::startHTTPServer(int port) {
    httpServer = std::make_unique<httplib::Server>();

    // CORS headers
    httpServer->set_pre_routing_handler([](const httplib::Request& req, httplib::Response& res) {
        res.set_header("Access-Control-Allow-Origin", "*");
        res.set_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
        res.set_header("Access-Control-Allow-Headers", "Content-Type");
        return httplib::Server::HandlerResponse::Unhandled;
    });

    httpServer->Options(".*", [](const httplib::Request&, httplib::Response& res) {
        return;
    });

    // WebRTC endpoints
    httpServer->Get("/offer", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetOffer(req, res);
    });

    httpServer->Post("/answer", [this](const httplib::Request& req, httplib::Response& res) {
        handlePostAnswer(req, res);
    });

    httpServer->Post("/offer", [this](const httplib::Request& req, httplib::Response& res) {
        handlePostOffer(req, res);
    });

    httpServer->Post("/ice", [this](const httplib::Request& req, httplib::Response& res) {
        handlePostICE(req, res);
    });

    httpServer->Get("/config", [this](const httplib::Request& req, httplib::Response& res) {
        json config = {
            {"encoder", {
                {"width", encoderConfig.width},
                {"height", encoderConfig.height},
                {"framerate", encoderConfig.framerate},
                {"bitrate", encoderConfig.bitrate}
            }},
            {"status", "ready"}
        };
        res.set_content(config.dump(), "application/json");
    });

    httpServer->Get("/stats", [this](const httplib::Request& req, httplib::Response& res) {
        handleGetStats(req, res);
    });

    std::cout << "[HTTP] Starting server on port " << port << std::endl;
    
    std::thread([this, port]() {
        if (!httpServer->listen("0.0.0.0", port)) {
            std::cerr << "[ERROR] Failed to start HTTP server on port " << port << std::endl;
        }
    }).detach();

    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    std::cout << "[HTTP] Server started successfully" << std::endl;
}

void WebRTCManager::setupWebRTC() {
    createPeerConnection();
}

void WebRTCManager::createPeerConnection() {
    rtc::Configuration config;
    config.iceServers.emplace_back("stun:stun.l.google.com:19302");

    peerConnection = std::make_shared<rtc::PeerConnection>(config);

    peerConnection->onStateChange([this](rtc::PeerConnection::State state) {
        std::cout << "[WebRTC] Connection state: " << static_cast<int>(state) << std::endl;
        if (state == rtc::PeerConnection::State::Connected) {
            streaming = true;
            std::cout << "[SUCCESS] WebRTC connection established!" << std::endl;
        } else if (state == rtc::PeerConnection::State::Disconnected || 
                   state == rtc::PeerConnection::State::Failed) {
            streaming = false;
            std::cout << "[INFO] WebRTC connection lost" << std::endl;
        }
    });

    peerConnection->onGatheringStateChange([this](rtc::PeerConnection::GatheringState state) {
        std::cout << "[WebRTC] Gathering state: " << static_cast<int>(state) << std::endl;
    });

    // Handle DataChannel creation
    peerConnection->onDataChannel([this](std::shared_ptr<rtc::DataChannel> dc) {
        std::cout << "[DEBUG] DataChannel received: " << dc->label() << std::endl;
        
        if (dc->label() == "input") {
            dataChannel = dc;
            setupDataChannelCallbacks(dc, "input");
        } else if (dc->label() == "video") {
            videoChannel = dc;
            setupDataChannelCallbacks(dc, "video");
        } else if (dc->label() == "audio") {
            audioChannel = dc;
            setupDataChannelCallbacks(dc, "audio");
        }
    });

    std::cout << "[DEBUG] PeerConnection created successfully" << std::endl;
}

void WebRTCManager::setupDataChannelCallbacks(std::shared_ptr<rtc::DataChannel> dc, const std::string& type) {
    dc->onOpen([this, type]() {
        std::cout << "[SUCCESS] " << type << " DataChannel opened" << std::endl;
        
        if (type == "input") {
            dataChannelReady = true;
            if (onDataChannelOpen) onDataChannelOpen();
        } else if (type == "video") {
            videoChannelReady = true;
        } else if (type == "audio") {
            audioChannelReady = true;
        }
    });

    dc->onClosed([this, type]() {
        std::cout << "[INFO] " << type << " DataChannel closed" << std::endl;
        
        if (type == "input") {
            dataChannelReady = false;
            if (onDataChannelClose) onDataChannelClose();
        } else if (type == "video") {
            videoChannelReady = false;
        } else if (type == "audio") {
            audioChannelReady = false;
        }
    });

    dc->onError([type](std::string error) {
        std::cout << "[ERROR] " << type << " DataChannel error: " << error << std::endl;
    });

    if (type == "input") {
        dc->onMessage([this](auto message) {
            if (std::holds_alternative<std::string>(message)) {
                auto msg = std::get<std::string>(message);
                std::thread([this, msg]() {
                    try {
                        if (onDataChannelMessage) {
                            onDataChannelMessage(msg);
                        }
                    } catch (const std::exception& e) {
                        std::cerr << "[ERROR] Input processing error: " << e.what() << std::endl;
                    }
                }).detach();
            }
        });
    }
}

// DataChannel transmission methods
bool WebRTCManager::sendH264Data(const std::vector<uint8_t>& data) {
    if (!videoChannel || !videoChannelReady) {
        return false;
    }

    try {
        uint64_t videoBuf = videoChannel->bufferedAmount();
        uint64_t audioBuf = audioChannel ? audioChannel->bufferedAmount() : 0;

    
        if (audioBuf > 8000 && videoBuf > 16000) {
            static int prioritySkip = 0;
            if (++prioritySkip % 2 == 0) {
                std::cout << "[PRIORITY] Skipping video for audio priority (A:" << audioBuf << ", V:" << videoBuf << ")" << std::endl;
            }
            return true;
        }

        if (videoBuf > 32000) { 
            static int skipCount = 0;
            if (++skipCount % 3 == 0) { 
                std::cout << "[WARNING] Video buffer: " << videoBuf << " bytes, skipping frame" << std::endl;
            }
            return true;
        }

        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastVideoSend).count();
        if (timeSinceLastSend < 16) {  
            return true;  
        }
        lastVideoSend = now;

        const size_t MAX_DIRECT_SIZE = 16000;  
        if (data.size() > MAX_DIRECT_SIZE) {
            return sendVideoDataInChunks(data);
        }

        videoChannel->send(reinterpret_cast<const std::byte*>(data.data()), data.size());

        static int frameCounter = 0;
        if (++frameCounter % 100 == 0) {
            std::cout << "[VIDEO] Sent H264 frame " << frameCounter << " (" << data.size() << " bytes)" << std::endl;
        }

        std::lock_guard<std::mutex> lock(statsMutex);
        videoStats.framesSent++;
        videoStats.bytesTransmitted += data.size();
        return true;

    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to send H264 data: " << e.what() << std::endl;
        return false;
    }
}

bool WebRTCManager::sendAudioData(const std::vector<uint8_t>& data) {
    if (!audioChannel || !audioChannelReady) {
        return false;
    }

    try {
        uint64_t buffered = audioChannel->bufferedAmount();
        if (buffered > 16000) { 
            static int skipCount = 0;
            if (++skipCount % 10 == 0) {
                std::cout << "[WARNING] Audio buffer: " << buffered << " bytes, skipping" << std::endl;
            }
            return true; // Skip to prevent buffer overflow
        }

        auto now = std::chrono::steady_clock::now();
        auto timeSinceLastSend = std::chrono::duration_cast<std::chrono::milliseconds>(now - lastAudioSend).count();
        if (timeSinceLastSend < 10) {  
            return true; 
        }
        lastAudioSend = now;

        audioChannel->send(reinterpret_cast<const std::byte*>(data.data()), data.size());

        static int audioFramesSent = 0;
        audioFramesSent++;

        if (audioFramesSent == 1) {
            std::cout << "[AUDIO] Sending audio frames through DataChannel: " << data.size() << " bytes per frame" << std::endl;
        }

        if (audioFramesSent % 100 == 0) {
            std::cout << "[AUDIO] Sent " << audioFramesSent << " audio frames (" << data.size() << " bytes)" << std::endl;
        }

        return true;
    } catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to send audio data: " << e.what() << std::endl;
        return false;
    }
}

// Chunked transmission for large frames
bool WebRTCManager::sendVideoDataInChunks(const std::vector<uint8_t>& data) {
    if (!videoChannel || !videoChannelReady) {
        return false;
    }

    const size_t MAX_CHUNK_SIZE = 8000; 
    const size_t totalChunks = (data.size() + MAX_CHUNK_SIZE - 1) / MAX_CHUNK_SIZE;
    static uint32_t frameId = 1;
    uint32_t currentFrameId = frameId++;

    for (size_t i = 0; i < totalChunks; ++i) {
        size_t offset = i * MAX_CHUNK_SIZE;
        size_t chunkSize = std::min(MAX_CHUNK_SIZE, data.size() - offset);

        // Create chunk header: [frameId(4)] [chunkIndex(4)] [totalChunks(4)] [chunkSize(4)] [data...]
        std::vector<uint8_t> chunk(16 + chunkSize);

        // Frame ID
        chunk[0] = (currentFrameId >> 24) & 0xFF;
        chunk[1] = (currentFrameId >> 16) & 0xFF;
        chunk[2] = (currentFrameId >> 8) & 0xFF;
        chunk[3] = currentFrameId & 0xFF;

        // Chunk index
        chunk[4] = (i >> 24) & 0xFF;
        chunk[5] = (i >> 16) & 0xFF;
        chunk[6] = (i >> 8) & 0xFF;
        chunk[7] = i & 0xFF;

        // Total chunks
        chunk[8] = (totalChunks >> 24) & 0xFF;
        chunk[9] = (totalChunks >> 16) & 0xFF;
        chunk[10] = (totalChunks >> 8) & 0xFF;
        chunk[11] = totalChunks & 0xFF;

        // Chunk size
        chunk[12] = (chunkSize >> 24) & 0xFF;
        chunk[13] = (chunkSize >> 16) & 0xFF;
        chunk[14] = (chunkSize >> 8) & 0xFF;
        chunk[15] = chunkSize & 0xFF;

        // Copy data
        std::copy(data.begin() + offset, data.begin() + offset + chunkSize, chunk.begin() + 16);

        try {
            if (videoChannel->bufferedAmount() > 24000) {
                std::cout << "[WARNING] Video buffer full during chunking, dropping frame " << currentFrameId << std::endl;
                return false;
            }

            videoChannel->send(reinterpret_cast<const std::byte*>(chunk.data()), chunk.size());


            if (totalChunks > 3 && i < totalChunks - 1) {
                std::this_thread::sleep_for(std::chrono::microseconds(100));
            }
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to send video chunk " << i << "/" << totalChunks << ": " << e.what() << std::endl;
            return false;
        }
    }

    std::cout << "[VIDEO] Sent large frame " << currentFrameId << " in " << totalChunks << " chunks (" << data.size() << " bytes)" << std::endl;
    return true;
}

bool WebRTCManager::sendAudioDataInChunks(const std::vector<uint8_t>& data) {
    if (!audioChannel || !audioChannelReady) {
        return false;
    }

    const size_t MAX_CHUNK_SIZE = 4000;  
    const size_t totalChunks = (data.size() + MAX_CHUNK_SIZE - 1) / MAX_CHUNK_SIZE;
    static uint32_t frameId = 1;
    uint32_t currentFrameId = frameId++;

    for (size_t i = 0; i < totalChunks; ++i) {
        size_t offset = i * MAX_CHUNK_SIZE;
        size_t chunkSize = std::min(MAX_CHUNK_SIZE, data.size() - offset);

        std::vector<uint8_t> chunk(16 + chunkSize);

        // Same header format as video chunks
        chunk[0] = (currentFrameId >> 24) & 0xFF;
        chunk[1] = (currentFrameId >> 16) & 0xFF;
        chunk[2] = (currentFrameId >> 8) & 0xFF;
        chunk[3] = currentFrameId & 0xFF;

        chunk[4] = (i >> 24) & 0xFF;
        chunk[5] = (i >> 16) & 0xFF;
        chunk[6] = (i >> 8) & 0xFF;
        chunk[7] = i & 0xFF;

        chunk[8] = (totalChunks >> 24) & 0xFF;
        chunk[9] = (totalChunks >> 16) & 0xFF;
        chunk[10] = (totalChunks >> 8) & 0xFF;
        chunk[11] = totalChunks & 0xFF;

        chunk[12] = (chunkSize >> 24) & 0xFF;
        chunk[13] = (chunkSize >> 16) & 0xFF;
        chunk[14] = (chunkSize >> 8) & 0xFF;
        chunk[15] = chunkSize & 0xFF;

        std::copy(data.begin() + offset, data.begin() + offset + chunkSize, chunk.begin() + 16);

        try {
            audioChannel->send(reinterpret_cast<const std::byte*>(chunk.data()), chunk.size());
        } catch (const std::exception& e) {
            std::cerr << "[ERROR] Failed to send audio chunk " << i << "/" << totalChunks << ": " << e.what() << std::endl;
            return false;
        }
    }

    return true;
}

// Callback setters
void WebRTCManager::setOnDataChannelMessage(std::function<void(const std::string&)> callback) {
    onDataChannelMessage = callback;
}

void WebRTCManager::setOnDataChannelOpen(std::function<void()> callback) {
    onDataChannelOpen = callback;
}

void WebRTCManager::setOnDataChannelClose(std::function<void()> callback) {
    onDataChannelClose = callback;
}

// Connection management
void WebRTCManager::resetConnection() {
    std::cout << "[DEBUG] Resetting WebRTC connection" << std::endl;

    // Reset state flags
    dataChannelReady = false;
    videoChannelReady = false;
    audioChannelReady = false;
    remoteDescriptionSet = false;
    streaming = false;

    // Reset DataChannels
    if (dataChannel) {
        try {
            dataChannel->close();
        } catch (const std::exception& e) {
            std::cout << "[WARNING] Error closing input DataChannel: " << e.what() << std::endl;
        }
        dataChannel.reset();
    }

    if (videoChannel) {
        try {
            videoChannel->close();
        } catch (const std::exception& e) {
            std::cout << "[WARNING] Error closing video DataChannel: " << e.what() << std::endl;
        }
        videoChannel.reset();
    }

    if (audioChannel) {
        try {
            audioChannel->close();
        } catch (const std::exception& e) {
            std::cout << "[WARNING] Error closing audio DataChannel: " << e.what() << std::endl;
        }
        audioChannel.reset();
    }

    // Close peer connection
    if (peerConnection) {
        try {
            peerConnection->close();
        } catch (const std::exception& e) {
            std::cout << "[WARNING] Error closing PeerConnection: " << e.what() << std::endl;
        }
        peerConnection.reset();
    }

    // Clear ICE candidate queue
    {
        std::lock_guard<std::mutex> lock(iceMutex);
        iceCandidateQueue.clear();
    }

    std::cout << "[DEBUG] Connection reset complete" << std::endl;
}

bool WebRTCManager::isConnectionActive() const {
    return peerConnection &&
           (peerConnection->state() == rtc::PeerConnection::State::Connected ||
            peerConnection->state() == rtc::PeerConnection::State::Connecting);
}

// ICE candidate management
void WebRTCManager::queueIceCandidate(const std::string& ice) {
    std::lock_guard<std::mutex> lock(iceMutex);
    iceCandidateQueue.push_back(ice);
    std::cout << "[DEBUG] ICE candidate queued, total: " << iceCandidateQueue.size() << std::endl;
}

void WebRTCManager::processQueuedIceCandidates() {
    std::lock_guard<std::mutex> lock(iceMutex);

    for (const auto& ice : iceCandidateQueue) {
        try {
            auto candidate = rtc::Candidate(ice, "application");
            peerConnection->addRemoteCandidate(candidate);
            std::cout << "[DEBUG] Processed queued ICE candidate" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to process queued ICE candidate: " << e.what() << std::endl;
        }
    }

    iceCandidateQueue.clear();
    std::cout << "[DEBUG] ICE candidate queue processed and cleared" << std::endl;
}

// HTTP handlers
void WebRTCManager::handleGetOffer(const httplib::Request& req, httplib::Response& res) {
    try {
        std::cout << "[DEBUG] Client requesting offer from server" << std::endl;

        if (peerConnection && peerConnection->state() != rtc::PeerConnection::State::New) {
            std::cout << "[DEBUG] Resetting existing connection" << std::endl;
            resetConnection();
        }

        createPeerConnection();

        // Create DataChannels (must be done before creating offer)
        std::cout << "[DEBUG] Creating DataChannels..." << std::endl;
        try {
            auto inputDc = peerConnection->createDataChannel("input");
            dataChannel = inputDc;
            setupDataChannelCallbacks(inputDc, "input");

            auto videoDc = peerConnection->createDataChannel("video");
            videoChannel = videoDc;
            setupDataChannelCallbacks(videoDc, "video");

            auto audioDc = peerConnection->createDataChannel("audio");
            audioChannel = audioDc;
            setupDataChannelCallbacks(audioDc, "audio");

            std::cout << "[SUCCESS] DataChannels created (input + video + audio)" << std::endl;
        } catch (const std::exception& e) {
            std::cout << "[ERROR] Failed to create DataChannels: " << e.what() << std::endl;
        }

        // Create offer
        std::cout << "[DEBUG] Creating offer..." << std::endl;
        peerConnection->setLocalDescription();

        std::this_thread::sleep_for(std::chrono::milliseconds(100));

        auto localDesc = peerConnection->localDescription();
        if (!localDesc) {
            throw std::runtime_error("Failed to create local description");
        }

        std::string sdp = localDesc->generateSdp();
        std::cout << "[DEBUG] Generated offer SDP" << std::endl;

        json response = {
            {"type", "offer"},
            {"sdp", sdp}
        };

        res.set_content(response.dump(), "application/json");
        std::cout << "[DEBUG] Sent offer to client" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to create offer: " << e.what() << std::endl;
        res.status = 500;
        json errorJson = {
            {"error", "Failed to create offer"},
            {"details", e.what()}
        };
        res.set_content(errorJson.dump(), "application/json");
    }
}

void WebRTCManager::handlePostAnswer(const httplib::Request& req, httplib::Response& res) {
    try {
        std::cout << "[DEBUG] Received answer from client: " << req.body << std::endl;

        if (req.body.empty()) {
            throw std::runtime_error("Empty request body");
        }

        auto jsonData = json::parse(req.body);

        if (!jsonData.contains("sdp") || !jsonData.contains("type")) {
            throw std::runtime_error("Missing sdp or type in request");
        }

        std::string sdp = jsonData["sdp"];
        std::string type = jsonData["type"];

        if (type != "answer") {
            throw std::runtime_error("Expected answer, got: " + type);
        }

        std::cout << "[DEBUG] Setting remote description (answer)" << std::endl;
        auto description = rtc::Description(sdp, type);
        peerConnection->setRemoteDescription(description);

        remoteDescriptionSet = true;
        processQueuedIceCandidates();

        std::cout << "[SUCCESS] Answer processed successfully" << std::endl;

        json response = {
            {"status", "success"},
            {"message", "Answer processed"}
        };

        res.set_content(response.dump(), "application/json");

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to process answer: " << e.what() << std::endl;
        res.status = 500;
        json errorJson = {
            {"error", "Failed to process answer"},
            {"details", e.what()}
        };
        res.set_content(errorJson.dump(), "application/json");
    }
}

void WebRTCManager::handlePostOffer(const httplib::Request& req, httplib::Response& res) {
    try {
        std::cout << "[DEBUG] Received offer from client: " << req.body << std::endl;

        if (req.body.empty()) {
            throw std::runtime_error("Empty request body");
        }

        auto jsonData = json::parse(req.body);
        std::string sdp = jsonData["sdp"];

        std::string answerSdp = handleOffer(sdp);
        if (answerSdp.empty()) {
            throw std::runtime_error("Failed to generate answer");
        }

        json response = {
            {"type", "answer"},
            {"sdp", answerSdp}
        };

        res.set_content(response.dump(), "application/json");
        std::cout << "[DEBUG] Sent answer to client" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to process offer: " << e.what() << std::endl;
        res.status = 500;
        json errorJson = {
            {"error", "Failed to process offer"},
            {"details", e.what()}
        };
        res.set_content(errorJson.dump(), "application/json");
    }
}

void WebRTCManager::handlePostICE(const httplib::Request& req, httplib::Response& res) {
    try {
        std::cout << "[DEBUG] Received ICE candidate: " << req.body << std::endl;

        if (req.body.empty()) {
            throw std::runtime_error("Empty request body");
        }

        auto jsonData = json::parse(req.body);
        std::string candidate = jsonData["candidate"];

        handleICE(candidate);

        json response = {
            {"status", "success"},
            {"message", "ICE candidate processed"}
        };

        res.set_content(response.dump(), "application/json");

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to process ICE candidate: " << e.what() << std::endl;
        res.status = 500;
        json errorJson = {
            {"error", "Failed to process ICE candidate"},
            {"details", e.what()}
        };
        res.set_content(errorJson.dump(), "application/json");
    }
}

std::string WebRTCManager::handleOffer(const std::string& offer) {
    try {
        std::cout << "[DEBUG] Processing offer SDP" << std::endl;

        if (peerConnection) {
            auto currentState = peerConnection->state();
            std::cout << "[DEBUG] Current connection state: " << static_cast<int>(currentState) << std::endl;
            std::cout << "[INFO] New offer received, resetting connection..." << std::endl;
            resetConnection();
        }

        createPeerConnection();

        auto description = rtc::Description(offer, "offer");

        std::cout << "[DEBUG] Setting remote description" << std::endl;
        peerConnection->setRemoteDescription(description);
        remoteDescriptionSet = true;
        processQueuedIceCandidates();

        std::cout << "[INFO] Using DataChannel-only approach for media transmission" << std::endl;

        auto answer = peerConnection->localDescription();
        if (answer) {
            std::string answerSdp = answer->generateSdp();
            std::cout << "[DEBUG] Generated answer SDP" << std::endl;
            return answerSdp;
        } else {
            std::cout << "[ERROR] Failed to generate local description" << std::endl;
            return "";
        }
    }
    catch (const std::exception& e) {
        std::cerr << "[ERROR] Failed to handle offer: " << e.what() << std::endl;
        return "";
    }
}

void WebRTCManager::handleICE(const std::string& ice) {
    try {
        std::cout << "[DEBUG] Processing ICE candidate: " << ice << std::endl;

        if (!peerConnection) {
            std::cout << "[WARNING] No active PeerConnection for ICE candidate" << std::endl;
            return;
        }

        if (!remoteDescriptionSet) {
            std::cout << "[DEBUG] Remote description not set, queueing ICE candidate" << std::endl;
            queueIceCandidate(ice);
            return;
        }

        auto candidate = rtc::Candidate(ice, "application");
        peerConnection->addRemoteCandidate(candidate);
        std::cout << "[DEBUG] ICE candidate added successfully" << std::endl;

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to process ICE candidate: " << e.what() << std::endl;
    }
}

// Statistics and configuration methods
void WebRTCManager::handleGetStats(const httplib::Request& req, httplib::Response& res) {
    try {
        updateStats();

        json statsJson;

        {
            std::lock_guard<std::mutex> lock(statsMutex);

            statsJson["video"] = {
                {"framesSent", videoStats.framesSent},
                {"bytesTransmitted", videoStats.bytesTransmitted},
                {"packetsLost", videoStats.packetsLost},
                {"currentBitrate", videoStats.currentBitrate},
                {"rtt", videoStats.rtt}
            };

            statsJson["audio"] = {
                {"framesSent", audioStats.framesSent},
                {"bytesTransmitted", audioStats.bytesTransmitted},
                {"packetsLost", audioStats.packetsLost},
                {"rtt", audioStats.rtt}
            };
        }

        statsJson["encoder"] = {
            {"width", encoderConfig.width},
            {"height", encoderConfig.height},
            {"framerate", encoderConfig.framerate},
            {"bitrate", encoderConfig.bitrate}
        };

        statsJson["connection"] = {
            {"state", peerConnection ? static_cast<int>(peerConnection->state()) : -1},
            {"dataChannelReady", dataChannelReady},
            {"videoChannelReady", videoChannelReady},
            {"audioChannelReady", audioChannelReady}
        };

        res.set_content(statsJson.dump(), "application/json");

    } catch (const std::exception& e) {
        std::cout << "[ERROR] Failed to get stats: " << e.what() << std::endl;
        res.status = 500;
        json errorJson = {
            {"error", "Failed to get stats"},
            {"details", e.what()}
        };
        res.set_content(errorJson.dump(), "application/json");
    }
}

WebRTCManager::MediaStats WebRTCManager::getVideoStats() const {
    std::lock_guard<std::mutex> lock(statsMutex);
    return videoStats;
}

WebRTCManager::MediaStats WebRTCManager::getAudioStats() const {
    std::lock_guard<std::mutex> lock(statsMutex);
    return audioStats;
}

VideoStats WebRTCManager::getVideoStatsNew() const {
    std::lock_guard<std::mutex> lock(statsMutex);
    VideoStats stats;
    stats.framesSent = videoStats.framesSent;
    stats.bytesTransmitted = videoStats.bytesTransmitted;
    stats.currentBitrate = static_cast<uint32_t>(videoStats.currentBitrate);
    stats.droppedFrames = 0;
    return stats;
}

AudioStats WebRTCManager::getAudioStatsNew() const {
    std::lock_guard<std::mutex> lock(statsMutex);
    AudioStats stats;
    stats.framesSent = audioStats.framesSent;
    stats.bytesTransmitted = audioStats.bytesTransmitted;
    stats.sampleRate = 48000;
    stats.channels = 2;
    return stats;
}

void WebRTCManager::updateStats() {
    auto now = std::chrono::steady_clock::now();

    std::lock_guard<std::mutex> lock(statsMutex);

    auto timeDiff = std::chrono::duration_cast<std::chrono::milliseconds>(
        now - videoStats.lastUpdate).count();

    if (timeDiff > 0) {
        videoStats.currentBitrate = (videoStats.bytesTransmitted * 8.0) / (timeDiff / 1000.0) / 1000.0;
    }

    videoStats.lastUpdate = now;
    audioStats.lastUpdate = now;
}

// Configuration methods
void WebRTCManager::setEncoderConfig(const EncoderConfig& config) {
    encoderConfig = config;
    if (qualityController) {
        qualityController->setMinBitrate(config.minBitrate);
        qualityController->setMaxBitrate(config.maxBitrate);
    }
}

EncoderConfig WebRTCManager::getEncoderConfig() const {
    return encoderConfig;
}

void WebRTCManager::enableAdaptiveBitrate(bool enable) {
    encoderConfig.adaptiveBitrate = enable;
}

void WebRTCManager::updateNetworkMetrics(double rtt, double packetLoss, double bandwidth) {
    if (qualityController) {
        NetworkMetrics metrics;
        metrics.rtt = rtt;
        metrics.packetLoss = packetLoss;
        metrics.bandwidth = bandwidth;
        qualityController->updateNetworkMetrics(metrics);
    }
}

void WebRTCManager::startVideoStreaming(const std::string& udpUrl) {
    std::cout << "[INFO] Video streaming through DataChannel - no separate streaming thread needed" << std::endl;
}
