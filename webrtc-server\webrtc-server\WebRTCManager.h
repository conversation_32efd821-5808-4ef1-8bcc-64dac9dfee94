#pragma once

#include <memory>
#include <string>
#include <functional>
#include <vector>
#include <rtc/rtc.hpp>
#include "httplib.h"
#include <atomic>
#include <chrono>
#include <mutex>
#include <queue>
#include "AdaptiveQualityController.h"
#include "WebRTCTypes.h"
#include <nlohmann/json.hpp>

using json = nlohmann::json;

class WebRTCManager {
public:
    WebRTCManager();
    ~WebRTCManager();

    void startHTTPServer(int port);
    void setupWebRTC();
    void startVideoStreaming(const std::string& udpUrl);

    // Callback functions
    void setOnDataChannelMessage(std::function<void(const std::string&)> callback);
    void setOnDataChannelOpen(std::function<void()> callback);
    void setOnDataChannelClose(std::function<void()> callback);

    // Track callback functions - REMOVED

    // Legacy DataChannel methods (preserved for compatibility)
    bool sendH264Data(const std::vector<uint8_t>& data);
    bool sendAudioData(const std::vector<uint8_t>& data);

    // Data chunking for large frames
    bool sendDataInChunks(const std::vector<uint8_t>& data, const std::string& dataType);
    bool sendVideoDataInChunks(const std::vector<uint8_t>& data);
    bool sendAudioDataInChunks(const std::vector<uint8_t>& data);


    // Track methods - REMOVED

    // Quality control
    void setEncoderConfig(const EncoderConfig& config);
    EncoderConfig getEncoderConfig() const;
    void enableAdaptiveBitrate(bool enable);
    void updateNetworkMetrics(double rtt, double packetLoss, double bandwidth);

    // Check if data channels are ready for sending
    bool isDataChannelReady() const { return dataChannelReady; }
    bool isVideoChannelReady() const { return videoChannelReady; }
    bool isAudioChannelReady() const { return audioChannelReady; }

    // Statistics - both old and new formats for compatibility
    struct MediaStats {
        uint64_t framesSent = 0;
        uint64_t bytesTransmitted = 0;
        uint64_t packetsLost = 0;
        double currentBitrate = 0.0;
        double rtt = 0.0;
        std::chrono::steady_clock::time_point lastUpdate;
    };

    MediaStats getVideoStats() const;
    MediaStats getAudioStats() const;

    // New statistics methods for enhanced-webrtc-server-clean compatibility
    VideoStats getVideoStatsNew() const;
    AudioStats getAudioStatsNew() const;

    // Get data channels
    std::shared_ptr<rtc::DataChannel> getDataChannel() const { return dataChannel; }
    std::shared_ptr<rtc::DataChannel> getVideoChannel() const { return videoChannel; }
    std::shared_ptr<rtc::DataChannel> getAudioChannel() const { return audioChannel; }

    // Connection management
    void resetConnection();
    bool isConnectionActive() const;

    // ICE candidate queue management
    void queueIceCandidate(const std::string& ice);
    void processQueuedIceCandidates();

private:
    // Core WebRTC components
    std::shared_ptr<rtc::PeerConnection> peerConnection;
    std::shared_ptr<rtc::DataChannel> dataChannel;      // 输入事件
    std::shared_ptr<rtc::DataChannel> videoChannel;     // 视频数据
    std::shared_ptr<rtc::DataChannel> audioChannel;     // 音频数据

    // HTTP server
    std::unique_ptr<httplib::Server> httpServer;

    // State management
    bool dataChannelReady = false;      // 输入事件通道
    bool videoChannelReady = false;     // 视频数据通道
    bool audioChannelReady = false;     // 音频数据通道
    std::atomic<bool> streaming;
    bool remoteDescriptionSet = false;

    // Quality control and optimization
    std::unique_ptr<AdaptiveQualityController> qualityController;
    EncoderConfig encoderConfig;

    // Statistics
    MediaStats videoStats;
    MediaStats audioStats;
    mutable std::mutex statsMutex;

    // Media sending with priority management
    std::queue<std::vector<uint8_t>> videoFrameQueue;
    std::queue<std::vector<uint8_t>> audioFrameQueue;
    std::mutex videoQueueMutex;
    std::mutex audioQueueMutex;

    // 🚀 传输调度器
    std::chrono::steady_clock::time_point lastVideoSend;
    std::chrono::steady_clock::time_point lastAudioSend;
    std::atomic<bool> transmissionPaused{false};

    // Callbacks
    std::function<void(const std::string&)> onDataChannelMessage;
    std::function<void()> onDataChannelOpen;
    std::function<void()> onDataChannelClose;
    // Track callbacks - REMOVED

    // ICE candidate queue for early candidates
    std::vector<std::string> iceCandidateQueue;
    std::mutex iceMutex;

    // Core WebRTC methods
    void createPeerConnection();
    std::string handleOffer(const std::string& offer);
    void handleICE(const std::string& ice);

    // DataChannel setup
    void setupDataChannelCallbacks(std::shared_ptr<rtc::DataChannel> dc, const std::string& type);

    // Media track management - REMOVED

    // Statistics update
    void updateStats();

    // Quality control integration
    void initializeQualityController();
    void onQualityChanged(const QualityProfile& profile);

    // HTTP handlers
    void handleGetOffer(const httplib::Request& req, httplib::Response& res);
    void handlePostAnswer(const httplib::Request& req, httplib::Response& res);
    void handlePostOffer(const httplib::Request& req, httplib::Response& res); // Legacy - keep for compatibility
    void handlePostICE(const httplib::Request& req, httplib::Response& res);
    void handleGetConfig(const httplib::Request& req, httplib::Response& res);
    void handleGetStats(const httplib::Request& req, httplib::Response& res);
};