#pragma once

#include <cstdint>

// Encoder configuration structure
struct EncoderConfig {
    int width = 1920;
    int height = 1080;
    int framerate = 30;
    int bitrate = 2000;
    int maxBitrate = 4000;
    int minBitrate = 500;
    bool hardwareAcceleration = true;
    bool adaptiveBitrate = true;
};

// Statistics structures for compatibility with enhanced-webrtc-server-clean
struct VideoStats {
    uint64_t framesSent = 0;
    uint64_t bytesTransmitted = 0;
    uint32_t currentBitrate = 0;
    uint32_t droppedFrames = 0;
};

struct AudioStats {
    uint64_t framesSent = 0;
    uint64_t bytesTransmitted = 0;
    uint32_t sampleRate = 0;
    uint32_t channels = 0;
};
