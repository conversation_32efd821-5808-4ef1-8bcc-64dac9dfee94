#include <iostream>
#include <string>
#include <thread>
#include <chrono>
#include <memory>
#include <atomic>
#include <sstream>
#include <iomanip>
#include <cstdint>
#include <queue>
#include <functional>
#include <future>
#include <vector>
#include <mutex>
#include <condition_variable>
#include "WebRTCManager.h"
#include "WebRTCTypes.h"
#include "UDPReceiver.h"
#include "RTPReceiver.h"
#include "AudioUDPReceiver.h"
#include "httplib.h"
#include <nlohmann/json.hpp>

using json = nlohmann::json;
using namespace std;
using namespace std::chrono;


class thread_pool {
private:
    vector<thread> workers;
    queue<function<void()>> tasks;
    mutex queue_mutex;
    condition_variable condition;
    bool stop;

public:
    thread_pool(size_t threads) : stop(false) {
        for (size_t i = 0; i < threads; ++i) {
            workers.emplace_back([this] {
                for (;;) {
                    function<void()> task;
                    {
                        unique_lock<mutex> lock(this->queue_mutex);
                        this->condition.wait(lock, [this] { return this->stop || !this->tasks.empty(); });
                        if (this->stop && this->tasks.empty()) return;
                        task = move(this->tasks.front());
                        this->tasks.pop();
                    }
                    task();
                }
            });
        }
    }

    template<class F>
    void enqueue(F&& f) {
        {
            unique_lock<mutex> lock(queue_mutex);
            if (stop) return;
            tasks.emplace(forward<F>(f));
        }
        condition.notify_one();
    }

    ~thread_pool() {
        {
            unique_lock<mutex> lock(queue_mutex);
            stop = true;
        }
        condition.notify_all();
        for (thread &worker : workers) {
            worker.join();
        }
    }
};

// Input event handler
class InputEventHandler {
private:
    string getCurrentTimestamp() {
        auto now = system_clock::now();
        auto time_t = system_clock::to_time_t(now);
        auto ms = duration_cast<milliseconds>(
            now.time_since_epoch()) % 1000;

        stringstream ss;
        ss << put_time(localtime(&time_t), "%H:%M:%S");
        ss << '.' << setfill('0') << setw(3) << ms.count();
        return ss.str();
    }

public:
    void handleMouseMove(int x, int y) {
        cout << "[MOUSE] Move to (" << x << ", " << y << ")" << endl;
    }

    void handleMouseClick(int x, int y, int button) {
        cout << "[MOUSE] Click at (" << x << ", " << y << ") button " << button << endl;
    }

    void handleKeyPress(const string& key) {
        cout << "[KEY] Press: " << key << endl;
    }
};

// Optimized video streamer - with enhanced H.264 processing
class SimpleVideoStreamer {
private:
    WebRTCManager* webrtcManager_;
    unique_ptr<RTPReceiver> rtpReceiver_;
    atomic<bool> streaming_;

public:
    SimpleVideoStreamer(WebRTCManager* manager)
        : webrtcManager_(manager), streaming_(false) {}

    bool startRTPStreaming(int port) {
        if (streaming_.load()) {
            cout << "[VIDEO] Already streaming" << endl;
            return true;
        }

        cout << "[VIDEO] Starting RTP streaming on port " << port << endl;
        rtpReceiver_ = make_unique<RTPReceiver>(port);

        // Set up the callback to forward H264 data to WebRTC with frame rate control
        rtpReceiver_->setOnFrameReceived([this](const vector<uint8_t>& data) {
            if (webrtcManager_) {
                // Send through DataChannel only (avoids MTU issues)
                bool dataChannelSuccess = webrtcManager_->sendH264Data(data);

                // Log success status occasionally
                static int frameCount = 0;
                if (++frameCount % 100 == 0) {
                    cout << "[VIDEO] Frame " << frameCount << " - DataChannel: " << (dataChannelSuccess ? "OK" : "FAIL") << endl;
                }
            }
        });

        if (rtpReceiver_->start()) {
            streaming_ = true;
            cout << "[VIDEO] RTP streaming started successfully" << endl;
            return true;
        } else {
            cout << "[VIDEO] Failed to start RTP streaming" << endl;
            return false;
        }
    }



public:

    void stopStreaming() {
        if (!streaming_.load()) return;

        cout << "[VIDEO] Stopping streaming..." << endl;

        if (rtpReceiver_) {
            rtpReceiver_->stop();
            rtpReceiver_.reset();
        }

        streaming_ = false;
        cout << "[VIDEO] Streaming stopped" << endl;
    }

    void printStats() {
        if (rtpReceiver_) {
            auto stats = rtpReceiver_->getStats();
            cout << "[VIDEO-RTP] Packets: " << stats.packetsReceived
                      << ", Bytes: " << stats.bytesReceived << endl;
        }
    }
};

// Simple audio streamer - UDP only with proper WebRTC connection
class SimpleAudioStreamer {
private:
    WebRTCManager* webrtcManager_;
    unique_ptr<AudioUDPReceiver> udpReceiver_;
    atomic<bool> streaming_;

public:
    SimpleAudioStreamer(WebRTCManager* manager) : webrtcManager_(manager), streaming_(false) {}

    bool startUDPStreaming(int port) {
        if (streaming_.load()) {
            cout << "[AUDIO] Already streaming" << endl;
            return true;
        }

        cout << "[AUDIO] Starting UDP streaming on port " << port << endl;
        udpReceiver_ = make_unique<AudioUDPReceiver>(port);

        // Set up the callback to forward audio data to WebRTC
        udpReceiver_->setOnDataReceived([this](const vector<uint8_t>& data) {
            if (webrtcManager_) {
                // Send through DataChannel only (consistent with video)
                bool dataChannelSuccess = webrtcManager_->sendAudioData(data);

                // Log success status occasionally
                static int frameCount = 0;
                if (++frameCount % 100 == 0) {
                    cout << "[AUDIO] Frame " << frameCount << " - DataChannel: " << (dataChannelSuccess ? "OK" : "FAIL") << endl;
                }
            }
        });

        if (udpReceiver_->start()) {
            streaming_ = true;
            cout << "[AUDIO] UDP streaming started successfully" << endl;
            return true;
        } else {
            cout << "[AUDIO] Failed to start UDP streaming" << endl;
            return false;
        }
    }

    void stopStreaming() {
        if (!streaming_.load()) return;

        cout << "[AUDIO] Stopping UDP streaming..." << endl;
        if (udpReceiver_) udpReceiver_->stop();
        streaming_ = false;
        cout << "[AUDIO] UDP streaming stopped" << endl;
    }

    void printStats() {
        if (udpReceiver_) {
            auto stats = udpReceiver_->getStats();
            cout << "[AUDIO-UDP] Packets: " << stats.packetsReceived
                      << ", Bytes: " << stats.bytesReceived << endl;
        }
    }
};

void printUsage() {
    cout << "\n=== Enhanced WebRTC Media Server ===" << endl;
    cout << "WebRTC server supporting multiple streaming protocols\n" << endl;

    cout << "Supported streaming protocols:" << endl;
    cout << "1. RTP H264 video stream (port 5000)" << endl;
    cout << "2. UDP audio stream (port 5001)" << endl;

    cout << "\nFFmpeg streaming examples:" << endl;
    cout << "Video RTP: ffmpeg -f gdigrab -framerate 60 -i desktop -c:v h264_nvenc -f rtp rtp://127.0.0.1:5000" << endl;
    cout << "Audio UDP: ffmpeg -re -i input.mp4 -c:a aac -f adts udp://localhost:5001" << endl;

    cout << "\nFrontend connection: http://localhost:8080" << endl;
    cout << "Press 'q' + Enter to exit\n" << endl;
}

int main() {
    try {
        printUsage();

        // Create WebRTC manager
        auto webrtcManager = make_unique<WebRTCManager>();

        // Create simple streaming receivers with WebRTC connection
        auto videoStreamer = make_unique<SimpleVideoStreamer>(webrtcManager.get());
        auto audioStreamer = make_unique<SimpleAudioStreamer>(webrtcManager.get());

        // Create input handler
        auto inputHandler = make_unique<InputEventHandler>();

        // Set track open callback - start receivers when tracks are ready
        webrtcManager->setOnVideoTrackOpen([&videoStreamer]() {
            cout << "[INFO] Video track opened, starting RTP streaming..." << endl;
            videoStreamer->startRTPStreaming(5000);
            cout << "[INFO] Video RTP receiver started on port 5000" << endl;
            cout << "[INFO] Video: ffmpeg -f gdigrab -framerate 30 -i desktop -c:v h264_nvenc -f rtp rtp://127.0.0.1:5000" << endl;
        });

        webrtcManager->setOnAudioTrackOpen([&audioStreamer]() {
            cout << "[INFO] Audio track opened, starting UDP streaming..." << endl;
            audioStreamer->startUDPStreaming(5001);
            cout << "[INFO] Audio UDP receiver started on port 5001" << endl;
            cout << "[INFO] Audio: ffmpeg -re -i input.mp4 -c:a aac -f adts udp://localhost:5001" << endl;
        });

        // Keep data channel callback for input events (if needed)
        webrtcManager->setOnDataChannelOpen([]() {
            cout << "[INFO] DataChannel opened for input events" << endl;
        });

        // Set data channel message callback with non-blocking processing
        webrtcManager->setOnDataChannelMessage([&inputHandler](const string& message) {
            static thread_pool inputThreadPool(2); 

            inputThreadPool.enqueue([&inputHandler, message]() {
                try {
                    auto jsonMsg = json::parse(message);
                    string type = jsonMsg["type"];

                    if (type == "mousemove") {
                        int x = jsonMsg["x"];
                        int y = jsonMsg["y"];
                        inputHandler->handleMouseMove(x, y);
                    } else if (type == "mousedown" || type == "mouseup") {
                        int x = jsonMsg["x"];
                        int y = jsonMsg["y"];
                        int button = jsonMsg["button"];
                        inputHandler->handleMouseClick(x, y, button);
                    } else if (type == "keydown" || type == "keyup") {
                        string key = jsonMsg["key"];
                        inputHandler->handleKeyPress(key);
                    }
                } catch (const exception& e) {
                    cerr << "[ERROR] Failed to parse input message: " << e.what() << endl;
                }
            });
        });

        // Setup WebRTC
        webrtcManager->setupWebRTC();
        cout << "[INFO] WebRTC setup completed" << endl;

        // Configure encoder settings for optimization
        EncoderConfig config;
        config.width = 1920;
        config.height = 1080;
        config.framerate = 30;
        config.bitrate = 2000;
        config.maxBitrate = 4000;
        config.minBitrate = 500;
        config.hardwareAcceleration = true;
        config.adaptiveBitrate = true;

        webrtcManager->setEncoderConfig(config);
        webrtcManager->enableAdaptiveBitrate(true);

        cout << "[INFO] Encoder optimization configured: " << config.width << "x" << config.height
                  << "@" << config.framerate << "fps, " << config.bitrate << "kbps" << endl;

        // Start HTTP server using WebRTCManager's built-in server
        cout << "[INFO] Starting HTTP server on port 8080..." << endl;
        cout << "[INFO] Frontend: http://localhost:8080" << endl;
        cout << "[INFO] Press 'q' + Enter to exit" << endl;

        // Start server in a separate thread
        thread serverThread([&webrtcManager]() {
            webrtcManager->startHTTPServer(8080);
        });

        cout << "Starting HTTP server on port 8080" << endl;

        // Statistics thread
        thread statsThread([&videoStreamer, &audioStreamer, &webrtcManager]() {
            while (true) {
                this_thread::sleep_for(chrono::seconds(10));

                // Print streaming stats
                videoStreamer->printStats();
                audioStreamer->printStats();

                // Print WebRTC optimization stats
                try {
                    auto videoStats = webrtcManager->getVideoStatsNew();
                    auto audioStats = webrtcManager->getAudioStatsNew();
                    auto config = webrtcManager->getEncoderConfig();

                    cout << "\n=== WebRTC Optimization Stats ===" << endl;
                    cout << "Video: " << videoStats.framesSent << " frames, "
                              << (videoStats.bytesTransmitted / 1024 / 1024) << " MB, "
                              << videoStats.currentBitrate << " kbps" << endl;
                    cout << "Audio: " << audioStats.framesSent << " frames, "
                              << (audioStats.bytesTransmitted / 1024) << " KB" << endl;
                    cout << "Config: " << config.width << "x" << config.height
                              << "@" << config.framerate << "fps, " << config.bitrate << "kbps" << endl;
                    cout << "Adaptive: " << (config.adaptiveBitrate ? "ON" : "OFF")
                              << ", HW Accel: " << (config.hardwareAcceleration ? "ON" : "OFF") << endl;
                    cout << "================================\n" << endl;

                } catch (const exception& e) {
                    // Ignore stats errors to avoid spam
                }
            }
        });

        // Wait for exit command
        string input;
        while (getline(cin, input)) {
            if (input == "q" || input == "quit" || input == "exit") {
                break;
            }
        }

        // Clean up resources
        videoStreamer->stopStreaming();
        audioStreamer->stopStreaming();

        if (serverThread.joinable()) {
            serverThread.detach();
        }

        cout << "[INFO] Server stopped" << endl;
        return 0;

    } catch (const exception& e) {
        cerr << "[ERROR] Exception: " << e.what() << endl;
        return -1;
    }
}
