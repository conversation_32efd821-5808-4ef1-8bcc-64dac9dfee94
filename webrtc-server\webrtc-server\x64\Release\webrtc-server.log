﻿  enhanced-webrtc-server-clean.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(398,43): warning C4101: “e”: 未引用的局部变量
  WebRTCManager.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(44,24): error C2039: "setTargetBitrate": 不是 "AdaptiveQualityController" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\AdaptiveQualityController.h(79,7):
      参见“AdaptiveQualityController”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(142,13): error C3861: “setupDataChannelCallbacks”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(145,13): error C3861: “setupDataChannelCallbacks”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(148,13): error C3861: “setupDataChannelCallbacks”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(155,21): error C2039: "setupDataChannelCallbacks": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(156,17): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(160,13): error C2065: “dataChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(161,17): error C2065: “onDataChannelOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(161,36): error C3861: “onDataChannelOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(163,13): error C2065: “videoChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(165,13): error C2065: “audioChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(169,19): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(173,13): error C2065: “dataChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(174,17): error C2065: “onDataChannelClose”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(174,37): error C3861: “onDataChannelClose”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(176,13): error C2065: “videoChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(178,13): error C2065: “audioChannelReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(187,24): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(190,30): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(192,29): error C2065: “onDataChannelMessage”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(193,29): error C3861: “onDataChannelMessage”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(187,13): error C2665: “rtc::Channel::onMessage”: 没有重载函数可以转换所有参数类型
      D:\libdatachannel\include\rtc\channel.hpp(40,7):
      可能是“void rtc::Channel::onMessage(std::function<void (rtc::message_variant)>)”
          D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(187,13):
          “void rtc::Channel::onMessage(std::function<void (rtc::message_variant)>)”: 无法将参数 1 从“setupDataChannelCallbacks::<lambda_4>”转换为“std::function<void (rtc::message_variant)>”
              D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(200,9):
              没有可用于执行该转换的用户定义的转换运算符，或者无法调用该运算符
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(187,13):
      尝试匹配参数列表“(setupDataChannelCallbacks::<lambda_4>)”时
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(824,28): error C2039: "setTargetBitrate": 不是 "AdaptiveQualityController" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\AdaptiveQualityController.h(79,7):
      参见“AdaptiveQualityController”的声明
  
