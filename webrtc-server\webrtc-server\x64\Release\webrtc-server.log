﻿  WebRTCManager.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(594,13): error C3861: “addVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(601,13): error C3861: “addAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(709,13): error C3861: “addVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(716,13): error C3861: “addAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(804,21): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(805,21): error C3861: “onAudioTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1065,21): error C2039: "setOnVideoTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1066,5): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1069,21): error C2039: "setOnAudioTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1070,5): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1105,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1107,13): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1111,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1115,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1117,13): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1121,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1194,21): error C2039: "addVideoTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1195,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1206,37): error C2065: “encoderConfig”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,22): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1213,13): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1215,13): error C3861: “setupVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1226,21): error C2039: "addAudioTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1227,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1236,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1236,22): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1238,13): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1240,13): error C3861: “setupAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1251,21): error C2039: "sendVideoFrame": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1252,10): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1261,10): error C3861: “isValidH264Frame”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1267,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1272,18): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1274,9): error C3536: “state”: 初始化之前无法使用
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1274,46): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1274,46):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1274,96): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1274,96):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1287,87): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1288,64): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1289,79): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1295,13): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1309,9): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1310,9): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1322,14): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1323,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1342,18): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1343,17): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1348,13): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1349,13): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1365,21): error C2039: "sendAudioFrame": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1366,10): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1368,16): error C3861: “sendAudioData”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1375,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1380,18): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,9): error C3536: “state”: 初始化之前无法使用
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,46): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,46):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,96): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,96):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1393,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1396,14): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1397,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1401,42): error C2065: “statsMutex”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1402,9): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1403,9): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1420,18): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1421,17): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1426,46): error C2065: “statsMutex”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1427,13): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1428,13): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1492,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1525,21): error C2039: "setupVideoTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1526,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1528,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1528,29): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1530,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1533,17): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1534,17): error C3861: “onVideoTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1539,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1539,31): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1541,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1549,33): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1553,17): error C2065: “streaming”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1553,37): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1554,17): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1555,18): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1557,17): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1560,21): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1561,21): error C3861: “onVideoTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1570,21): error C2039: "setupAudioTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1571,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1573,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1573,29): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1575,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1578,17): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1579,17): error C3861: “onAudioTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1584,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1584,31): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1586,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1593,33): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1596,17): error C2065: “streaming”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1596,37): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1597,17): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1598,18): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1598,18): error C1003: 错误计数超过 100；正在停止编译
  enhanced-webrtc-server-clean.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(298,24): error C2039: "setOnVideoTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(305,24): error C2039: "setOnAudioTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
