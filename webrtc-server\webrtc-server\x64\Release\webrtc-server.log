﻿  enhanced-webrtc-server-clean.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“enhanced-webrtc-server-clean.cpp”)
  
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(270,24): error C2039: "setOnVideoTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\enhanced-webrtc-server-clean.cpp(277,24): error C2039: "setOnAudioTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
  WebRTCManager.cpp
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(1,1): warning C4819: 该文件包含不能在当前代码页(936)中表示的字符。请将该文件保存为 Unicode 格式以防止数据丢失
  (编译源文件“WebRTCManager.cpp”)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(145,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(146,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(494,13): error C3861: “addVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(501,13): error C3861: “addAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(609,13): error C3861: “addVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(616,13): error C3861: “addAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(910,21): error C2039: "setOnVideoTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(911,5): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(914,21): error C2039: "setOnAudioTrackOpen": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(915,5): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(924,5): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(925,5): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(951,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(953,13): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(957,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(961,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(963,13): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(967,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1040,21): error C2039: "addVideoTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1041,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1052,37): error C2065: “encoderConfig”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1057,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1057,22): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1059,13): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1061,13): error C3861: “setupVideoTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1072,21): error C2039: "addAudioTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1073,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1082,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1082,22): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1084,13): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1086,13): error C3861: “setupAudioTrack”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1097,21): error C2039: "sendVideoFrame": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1098,10): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1111,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1116,18): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1118,9): error C3536: “state”: 初始化之前无法使用
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1118,46): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1118,46):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1118,96): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1118,96):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1131,87): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1135,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1138,9): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1139,9): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1151,14): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1152,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1171,18): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1172,17): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1177,13): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1178,13): error C2065: “videoStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1194,21): error C2039: "sendAudioFrame": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1195,10): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1197,16): error C3861: “sendAudioData”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1204,10): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1209,18): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,9): error C3536: “state”: 初始化之前无法使用
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,46): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,46):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,96): error C2440: “类型强制转换”: 无法从“rtc::PeerConnection::State”转换为“int”
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1211,96):
      此转换需要显式强制转换(static_cast、C 样式强制转换或带圆括号函数样式强制转换)
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1222,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1225,14): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1226,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1230,42): error C2065: “statsMutex”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1231,9): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1232,9): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1249,18): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1250,17): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1255,46): error C2065: “statsMutex”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1256,13): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1257,13): error C2065: “audioStats”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1354,21): error C2039: "setupVideoTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1355,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1357,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1357,29): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1359,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1362,17): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1363,17): error C3861: “onVideoTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1368,9): error C2065: “videoTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1368,31): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1370,13): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1378,22): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1380,17): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1380,35): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1380,104): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1382,17): error C2065: “videoTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1385,21): error C2065: “onVideoTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1386,21): error C3861: “onVideoTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1393,21): error C2039: "setupAudioTrack": 不是 "WebRTCManager" 的成员
      D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.h(19,7):
      参见“WebRTCManager”的声明
  
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1394,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1396,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1396,29): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1398,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1401,17): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1402,17): error C3861: “onAudioTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1407,9): error C2065: “audioTrack”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1407,31): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1409,13): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1416,22): error C3482: “this”只能在非静态成员函数中用作 lambda 捕获
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1418,17): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1418,35): error C2065: “peerConnection”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1418,104): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1420,17): error C2065: “audioTrackReady”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1423,21): error C2065: “onAudioTrackOpen”: 未声明的标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1424,21): error C3861: “onAudioTrackOpen”: 找不到标识符
D:\webrtc-demo\webrtc-server\webrtc-server\WebRTCManager.cpp(1424,21): error C1003: 错误计数超过 100；正在停止编译
